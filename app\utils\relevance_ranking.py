import logging
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional
from difflib import SequenceMatcher

# 为此模块创建一个专用的日志记录器
logger = logging.getLogger("relevance_ranking")


def calculate_relevance_scores(
    results: List[Dict[str, Any]],
    search_term: Optional[str] = None,
    keywords: Optional[List[str]] = None,
    keyword_weights: Optional[Dict[str, float]] = None,
) -> List[Dict[str, Any]]:
    """
    计算搜索结果的相关性分数

    Args:
        results: 搜索结果列表
        search_term: 搜索关键词
        keywords: 关键词列表
        keyword_weights: 关键词权重字典

    Returns:
        添加了相关性分数的结果列表
    """
    if not results:
        return results

    # 如果没有提供关键词但有搜索词，将搜索词作为关键词
    if not keywords and search_term and len(search_term) >= 2:
        keywords = [search_term]
        keyword_weights = {search_term: 5.0}

    scored_results = []
    # 获取当前时间，只获取一次以保持一致性
    now = datetime.now()

    for i, result in enumerate(results):
        result_copy = result.copy() if isinstance(result, dict) else dict(result)
        score = 0.0
        score_details = {}  # 用于记录各个部分的得分

        # 新增：如果资源存在text_content，增加固定分数
        if result_copy.get("text_content"):
            score += 2.0
            score_details["内容存在"] = 2.0

        # 验证状态加分
        if result_copy.get("verified_status") == "valid":
            score += 2.0
            score_details["验证状态"] = 2.0

        # 个人资源加分
        if result_copy.get("is_mine") is True:
            score += 10.0
            score_details["个人资源"] = 10.0

        # 标题匹配度评分
        if keywords and search_term:
            title = result_copy.get("title", "") or result_copy.get("file_name", "")
            if title:
                title_lower = title.lower()
                search_term_lower = search_term.lower()

                # 计算相似度和包含关系
                similarity = SequenceMatcher(
                    None, title_lower, search_term_lower
                ).ratio()
                is_contained = search_term_lower in title_lower
                is_exact_match = title_lower == search_term_lower

                # 优化：避免重复计算，按优先级处理
                if is_exact_match:
                    # 完全匹配：最高优先级，给最高分
                    score += 25.0
                    score_details["完全匹配"] = 25.0
                elif is_contained:
                    # 包含匹配：搜索词完全包含在标题中
                    base_score = 20.0
                    # 长度差异惩罚：标题比搜索词长得越多，分数越低
                    length_diff = len(title_lower) - len(search_term_lower)
                    penalty = max(0, length_diff * 0.1)  # 降低惩罚系数
                    title_score = max(15.0, base_score - penalty)
                    score += title_score
                    score_details["标题包含匹配"] = round(title_score, 2)
                elif similarity >= 0.8:
                    # 高相似度匹配：标题与搜索词非常相似
                    base_score = 18.0
                    # 长度差异惩罚
                    length_diff = abs(len(title_lower) - len(search_term_lower))
                    penalty = max(0, length_diff * 0.1)
                    title_score = max(12.0, base_score - penalty)
                    score += title_score
                    score_details["标题相似匹配"] = round(title_score, 2)
                    score_details["相似度"] = round(similarity, 2)
                else:
                    # 部分匹配评分（仅当不完全包含搜索词时）
                    # 1. 分词基础分 (基于匹配的关键词比例)
                    matched_keywords_count = 0
                    # 确保keywords不为空且包含有效关键词
                    valid_keywords = (
                        [kw for kw in keywords if kw and isinstance(kw, str)]
                        if keywords
                        else []
                    )
                    total_keywords = len(valid_keywords)

                    if total_keywords > 0:
                        for keyword in valid_keywords:
                            if keyword.lower() in title_lower:
                                matched_keywords_count += 1
                        match_ratio = matched_keywords_count / total_keywords
                        segmentation_score = match_ratio * 8.0  # 降低最高分
                        score += segmentation_score
                        score_details["分词基础分"] = round(segmentation_score, 2)

                    # 2. 关键词位置和权重分
                    keyword_score = 0.0
                    if valid_keywords:
                        for keyword in valid_keywords:
                            weight = (
                                keyword_weights.get(keyword, 1.0)
                                if keyword_weights
                                else 1.0
                            )
                            keyword_lower = keyword.lower()
                            if keyword_lower in title_lower:
                                position = title_lower.find(keyword_lower)
                                position_factor = (
                                    1.0
                                    - (position / len(title_lower))
                                    * 0.3  # 降低位置影响
                                )
                                keyword_score += (
                                    weight * position_factor * 1.5
                                )  # 降低权重
                    score += keyword_score
                    score_details["关键词权重分"] = round(keyword_score, 2)

        # 更新时间评分（越新分数越高，最高15分，降低权重）
        updated_at = result_copy.get("updated_at")
        time_score = 0.0
        if updated_at:
            try:
                date_to_compare = None
                # 兼容多种时间格式
                if isinstance(updated_at, datetime):
                    date_to_compare = updated_at
                elif isinstance(updated_at, str):
                    # 尝试解析ISO格式的字符串
                    date_to_compare = datetime.fromisoformat(
                        updated_at.replace("Z", "+00:00")
                    )
                elif isinstance(updated_at, (int, float)):
                    # 兼容Unix时间戳
                    date_to_compare = datetime.fromtimestamp(
                        updated_at, tz=timezone.utc
                    )

                # 如果成功解析出datetime对象，则进行评分
                if date_to_compare:
                    # 如果updated_at有时区信息，确保now也有，反之亦然
                    if date_to_compare.tzinfo:
                        now = datetime.now(timezone.utc)
                    else:
                        now = datetime.now()  # 使用naive datetime以匹配

                    days_diff = (now - date_to_compare).days
                    if days_diff < 0:
                        days_diff = 0  # 避免未来时间导致异常

                    # 优化时间衰减函数：降低权重，使用更平缓的衰减
                    if days_diff == 0:
                        time_score = 15.0  # 当天更新的内容
                    elif days_diff <= 7:
                        time_score = 7.0  # 一周内的内容
                    elif days_diff <= 30:
                        time_score = 5.0  # 一个月内的内容
                    elif days_diff <= 90:
                        time_score = 3.0  # 三个月内的内容
                    elif days_diff <= 365:
                        time_score = 1.0  # 一年内的内容
                    else:
                        time_score = 0.0  # 超过一年的内容

                    score += time_score
                    score_details["更新时间"] = round(time_score, 2)

            except Exception as e:
                logger.warning(
                    f"时间评分计算异常 [结果 {i}]: updated_at='{updated_at}', 错误: {e}"
                )

        # 文件类型加分（优化权重分配）
        file_type = result_copy.get("file_type", "")
        file_score = 0.0
        if file_type == "video":
            file_score = 2.0  # 视频文件权重提高
        elif file_type == "document" or file_type == "文档":
            file_score = 1.0  # 文档文件
        elif file_type == "image" or file_type == "图片":
            file_score = 0.5  # 图片文件
        elif file_type == "audio" or file_type == "音频":
            file_score = 1.5  # 音频文件
        elif file_type == "archive" or file_type == "压缩包":
            file_score = 1  # 压缩包
        else:
            file_score = 0.5  # 其他类型
        score += file_score
        score_details["文件类型"] = file_score

        # 保存最终分数
        result_copy["relevance_score"] = round(score, 2)
        scored_results.append(result_copy)

        # 记录详细的评分构成
        logger.debug(
            f"打分详情 - "
            f"资源: '{result_copy.get('title', 'N/A')[:30]}...', "
            f"总分: {result_copy['relevance_score']}, "
            f"明细: {score_details}"
        )

    return scored_results


def sort_by_relevance(
    results: List[Dict[str, Any]], sort_by: str = "relevance", sort_order: str = "desc"
) -> List[Dict[str, Any]]:
    """
    根据指定字段对结果进行排序

    Args:
        results: 要排序的结果列表
        sort_by: 排序字段，默认为'relevance'
        sort_order: 排序方向，'desc'为降序，'asc'为升序

    Returns:
        排序后的结果列表
    """
    if not results:
        return results

    sorted_results = list(results)
    reverse = sort_order.lower() == "desc"

    # 根据相关性分数或指定字段排序
    if sort_by == "relevance":
        sorted_results.sort(key=lambda x: x.get("relevance_score", 0), reverse=reverse)
    elif sort_by in results[0]:
        sorted_results.sort(key=lambda x: x.get(sort_by, ""), reverse=reverse)
    return sorted_results
