{"apps": [{"name": "pan-so-api", "script": "python3", "args": "-m app.main", "cwd": "/root/pan-so-backend", "instances": 1, "autorestart": true, "watch": false, "max_memory_restart": "1G", "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "telegram-monitor", "script": "run_telegram_monitor.py", "interpreter": "python3", "cwd": "/root/pan-so-backend", "autorestart": true, "restart_delay": 5000, "watch": false, "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "aisoua-crawler", "script": "python3", "args": "-m app.tasks.aisoua_crawler_runner", "cwd": "/root/pan-so-backend", "autorestart": true, "restart_delay": 5000, "watch": false, "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "database-backup", "script": "backup_database.sh", "cwd": "/root/pan-so-backend", "interpreter": "bash", "cron_restart": "0 3 */3 * *", "autorestart": false, "watch": false, "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "celery-beat", "script": "/usr/local/bin/celery", "args": "-A app.core.celery_app beat -l info --pidfile=./celery-beat.pid", "cwd": "/root/pan-so-backend", "interpreter": "python3", "autorestart": true, "restart_delay": 5000, "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "worker-kdocs", "script": "/usr/local/bin/celery", "args": "-A app.core.celery_app worker -l info -P gevent -c 1 -Q kdocs_queue -n kdocs_worker@%h", "cwd": "/root/pan-so-backend", "interpreter": "python3", "autorestart": true, "restart_delay": 5000, "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "worker-<PERSON><PERSON><PERSON><PERSON>", "script": "/usr/local/bin/celery", "args": "-A app.core.celery_app worker -l info -P gevent -c 1 -Q xuebapan_queue -n xuebapan_worker@%h", "cwd": "/root/pan-so-backend", "interpreter": "python3", "autorestart": true, "restart_delay": 5000, "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "worker-submission", "script": "/usr/local/bin/celery", "args": "-A app.core.celery_app worker -l info -c 2 -Q submission_queue -n submission_worker@%h", "cwd": "/root/pan-so-backend", "interpreter": "python3", "autorestart": true, "restart_delay": 5000, "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "worker-<PERSON><PERSON><PERSON>", "script": "/usr/local/bin/celery", "args": "-A app.core.celery_app worker -l info -c 1 -Q duanju_queue -n duanju_worker@%h", "cwd": "/root/pan-so-backend", "interpreter": "python3", "autorestart": true, "restart_delay": 5000, "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "worker-seo", "script": "/usr/local/bin/celery", "args": "-A app.core.celery_app worker -l info -c 1 -Q seo_queue -n seo_worker@%h", "cwd": "/root/pan-so-backend", "interpreter": "python3", "autorestart": true, "restart_delay": 5000, "log_date_format": "YYYY-MM-DD HH:mm:ss"}, {"name": "meilisearch-listener", "script": "python3", "args": "-m app.tasks.meilisearch_sync_listener", "cwd": "/root/pan-so-backend", "autorestart": true, "restart_delay": 5000, "watch": false, "log_date_format": "YYYY-MM-DD HH:mm:ss"}]}