import asyncio
import logging

from app.crawlers.telegram_crawler import TelegramCrawler
from app.db.engine import init_db, close_db

# 配置一个简单的日志记录器，以便在控制台看到服务的状态
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("TelegramMonitorRunner")


async def main():
    """
    主函数：初始化环境，启动并管理Telegram监控服务的生命周期。
    """
    logger.info("正在为监控服务初始化数据库连接...")
    try:
        # 步骤1: 使用项目中已有的函数来初始化数据库
        await init_db()
        logger.info("数据库连接成功。")

        # 步骤2: 创建爬虫实例
        crawler = TelegramCrawler()

        # 步骤3: 启动持续监控
        logger.info("启动 Telegram 持续监控...")
        await crawler.start_monitoring()

    except (KeyboardInterrupt, asyncio.CancelledError):
        # 捕获手动停止信号 (Ctrl+C) 或异步任务取消
        logger.info("接收到停止信号，正在准备关闭...")
    except Exception as e:
        # 捕获其他所有异常，记录错误
        logger.error(f"监控服务遇到无法恢复的错误: {e}", exc_info=True)
    finally:
        # 步骤4: 无论如何，最后都要安全地关闭数据库连接
        logger.info("正在关闭数据库连接...")
        await close_db()
        logger.info("服务已安全关闭。")


if __name__ == "__main__":
    asyncio.run(main())
