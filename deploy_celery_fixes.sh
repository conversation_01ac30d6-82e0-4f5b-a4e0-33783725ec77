#!/bin/bash

# Linux环境Celery多进程修复部署脚本
# 使用隔离线程解决事件循环冲突问题

set -e  # 遇到错误立即退出

echo "=== Linux Celery多进程修复部署脚本 ==="
echo "时间: $(date)"
echo "用户: $(whoami)"
echo "目录: $(pwd)"

# 检查是否在正确的目录
if [ ! -f "app/tasks/submission.py" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

echo "✅ 项目目录检查通过"

# 1. 备份当前配置
echo "📦 备份当前配置..."
cp pan_so_pm2.json pan_so_pm2.json.backup.$(date +%Y%m%d_%H%M%S)
echo "✅ 配置已备份"

# 2. 检查PM2状态
echo "🔍 检查PM2状态..."
pm2 list

# 3. 重启submission worker
echo "🔄 重启submission worker..."
pm2 restart worker-submission

# 4. 等待worker启动
echo "⏳ 等待worker启动..."
sleep 5

# 5. 检查worker状态
echo "🔍 检查worker状态..."
pm2 list | grep worker-submission

# 6. 查看最近的日志
echo "📋 查看最近的日志..."
pm2 logs worker-submission --lines 15

# 7. 运行修复验证测试
echo "🧪 运行修复验证测试..."
python3 test_eventloop_fix.py
test_result=$?

echo ""
echo "=== 部署完成 ==="
echo "✅ submission worker已重启（使用-c 4多进程模式）"
echo "✅ 异步任务使用隔离线程执行，避免事件循环冲突"

if [ $test_result -eq 0 ]; then
    echo "✅ 修复验证测试通过"
else
    echo "⚠️  修复验证测试失败，请检查日志"
fi

echo ""
echo "📋 监控命令:"
echo "  查看日志: pm2 logs worker-submission"
echo "  查看状态: pm2 list"
echo "  重启worker: pm2 restart worker-submission"
echo "  运行测试: python3 test_eventloop_fix.py"
echo ""
echo "如果仍有问题，请检查日志并联系开发团队。"
