import asyncio
import json
import logging
from tortoise.exceptions import DoesNotExist
import asyncpg

from app.db.engine import init_db, close_db
from app.models.resource import PanResource
from app.services.meilisearch_service import meilisearch_service
from app.utils.config import settings
from app.sync_meilisearch import serialize_all

# 配置日志记录
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)


async def handle_notification(payload_str: str):
    """
    解析来自数据库的通知负载，并同步变更到Meilisearch。
    """
    try:
        payload = json.loads(payload_str)
        operation = payload.get("operation")
        resource_key = payload.get("resource_key")

        if not operation or not resource_key:
            logging.warning(f"收到的负载无效: {payload_str}")
            return

        logging.info(f"收到 {operation} 操作，资源KEY: {resource_key}")

        if operation == "DELETE":
            # 对于DELETE操作，我们只需要key就能从索引中移除文档
            meilisearch_service.index.delete_document(resource_key)
            logging.info(f"已从Meilisearch删除文档 '{resource_key}'。")
        else:  # INSERT or UPDATE
            # 对于INSERT/UPDATE，从数据库获取最新数据
            # 使用 .filter().limit(1).values() 以获取一个包含单个字典的列表
            resource_list = (
                await PanResource.filter(resource_key=resource_key).limit(1).values()
            )
            if resource_list:
                doc_dict = resource_list[0]
                # 使用全量同步脚本中的序列化函数
                serialized_doc = serialize_all(doc_dict)
                task = meilisearch_service.index.add_documents([serialized_doc])
                # 不等待任务完成，以加快处理速度
                logging.info(
                    f"已将文档 '{resource_key}' 的同步任务加入队列。任务ID: {task.task_uid}"
                )
            else:
                logging.warning(
                    f"在数据库中未找到资源KEY为 '{resource_key}' 的资源 ({operation})，无法同步。"
                )

    except json.JSONDecodeError:
        logging.error(f"无法解码JSON负载: {payload_str}")
    except Exception as e:
        logging.error(f"处理负载 '{payload_str}' 的通知时出错: {e}", exc_info=True)


async def listen_for_changes():
    """
    连接到PostgreSQL并监听 'resource_changes' 频道的通知。
    """
    db_creds = (
        settings.get("database", {})
        .get("connections", {})
        .get("default", {})
        .get("credentials", {})
    )
    if not db_creds.get("host"):
        logging.error("在配置中未找到数据库连接凭证。")
        return

    conn = None
    while True:  # 添加重连循环
        try:
            conn = await asyncpg.connect(
                user=db_creds.get("user"),
                password=db_creds.get("password"),
                database=db_creds.get("database"),
                host=db_creds.get("host"),
                port=db_creds.get("port"),
            )

            async def notification_callback(connection, pid, channel, payload):
                await handle_notification(payload)

            await conn.add_listener("resource_changes", notification_callback)

            logging.info("监听服务已启动，等待来自 'resource_changes' 频道的通知...")
            await asyncio.Future()  # 无限期运行

        except (asyncpg.exceptions.PostgresError, OSError) as e:
            logging.error(f"数据库连接错误: {e}。将在5秒后重试...", exc_info=True)
            if conn and not conn.is_closed():
                await conn.close()
            await asyncio.sleep(5)  # 等待5秒后重试
        except Exception as e:
            logging.error(f"发生未知错误: {e}。将在5秒后重试...", exc_info=True)
            if conn and conn.is_closed():
                await conn.close()
            await asyncio.sleep(5)
        finally:
            if conn and not conn.is_closed():
                await conn.close()
                logging.info("数据库监听连接已关闭。")


async def main():
    """
    主函数，初始化服务并启动监听器。
    """
    logging.info("正在为监听器初始化服务...")
    await init_db()

    try:
        await listen_for_changes()
    except asyncio.CancelledError:
        logging.info("监听任务被取消。")
    finally:
        logging.info("正在关闭服务...")
        await close_db()


# 直接运行主函数
try:
    logging.info("启动Meilisearch增量同步监听服务...")
    asyncio.run(main())
except KeyboardInterrupt:
    logging.info("监听服务被用户停止。")
