from app.core.celery_app import celery_app
from app.crawlers.telegram_crawler import TelegramCrawler
from app.crawlers.kdocs_crawler import kdocs_crawler
from app.crawlers.xuebapans_crawler import XuebapansCrawler
from app.crawlers.duanju_crawler import DuanjuCrawler
import asyncio
from app.services.db_batch import batch_save_to_db
from app.db.engine import db_transaction
import logging

logger = logging.getLogger(__name__)


@celery_app.task(name="crawlers.start_telegram_monitor")
def start_telegram_monitor():
    """
    启动 Telegram 持续监控任务。
    这是一个长时运行任务，一旦启动，会一直监听新消息。
    建议手动触发此任务，而不是通过定时任务。
    """
    # 由于爬虫的 run_monitor 是 async 的，我们需要一个事件循环来运行它
    # 注意：长时运行的监控任务不适合标准的事务模型，它自己管理连接。
    telegram_crawler = TelegramCrawler()
    asyncio.run(telegram_crawler.run_monitor())


@celery_app.task(name="crawlers.run_kdocs_crawl")
def run_kdocs_crawl():
    """
    执行 Kdocs 爬虫的一次性抓取、归档和发布任务。
    这是一个适合周期性调度的任务。
    """
    # 导入 _run_async_task 函数
    from app.tasks.submission import _run_async_task

    _run_async_task(run_kdocs_crawl_async)


async def run_kdocs_crawl_async():
    """
    独立的异步任务，用于运行Kdocs爬虫，使用事务上下文保证数据一致性。
    """
    logger.info("Kdocs爬虫Celery任务开始...")
    try:
        async with db_transaction():
            logger.info("已进入数据库事务上下文。")
            # 调用统一的入口方法，执行完整的抓取、更新和发布流程
            await kdocs_crawler.run_update_and_post_task()
    except Exception as e:
        logger.error(f"Kdocs爬虫任务执行失败: {e}", exc_info=True)
    finally:
        logger.info("Kdocs爬虫任务结束。")


@celery_app.task(name="crawlers.run_xuebapans_crawl")
def run_xuebapans_crawl():
    """
    执行学霸盘爬虫的一次性抓取任务，并将结果存入数据库。
    """
    # 导入 _run_async_task 函数
    from app.tasks.submission import _run_async_task

    _run_async_task(run_xuebapans_crawl_async)


async def run_xuebapans_crawl_async():
    """
    异步执行学霸盘爬虫。
    爬虫内部已实现数据分批入库。
    """
    logger.info("学霸盘爬虫Celery任务开始...")
    try:
        async with db_transaction():
            logger.info("已进入数据库事务上下文。")
            crawler = XuebapansCrawler()
            # 执行爬虫，它会自己处理数据入库，并返回处理总数
            saved_count = await crawler.crawl()

            crawler.logger.info(
                f"学霸盘爬虫任务数据抓取阶段完成，共发现 {saved_count} 条资源。"
            )

            # 强制清空并处理队列中所有剩余的项目
            crawler.logger.info("开始处理数据库队列中剩余的所有项目...")
            await batch_save_to_db(force=True)
            crawler.logger.info("数据库队列处理完成。")
    except Exception as e:
        logger.error(f"学霸盘爬虫任务执行失败: {e}", exc_info=True)
    finally:
        logger.info("学霸盘爬虫任务结束。")


@celery_app.task(name="crawlers.run_duanju_crawl")
def run_duanju_crawl():
    """
    执行duanju.click短剧爬虫的一次性抓取任务，并将结果存入数据库。
    """
    # 导入 _run_async_task 函数
    from app.tasks.submission import _run_async_task

    _run_async_task(run_duanju_crawl_async)


async def run_duanju_crawl_async():
    """
    异步执行duanju.click短剧爬虫。
    """
    logger.info("duanju.click短剧爬虫Celery任务开始...")
    try:
        async with db_transaction():
            logger.info("已进入数据库事务上下文。")
            crawler = DuanjuCrawler()
            await crawler.fetch_and_save()
    except Exception as e:
        logger.error(f"duanju.click短剧爬虫任务执行失败: {e}", exc_info=True)
    finally:
        logger.info("数据库连接已关闭。duanju.click短剧爬虫任务结束。")
