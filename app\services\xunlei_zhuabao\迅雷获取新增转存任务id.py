import http.client
import json

conn = http.client.HTTPSConnection("api-pan.xunlei.com")
payload = json.dumps({
   "parent_id": "",
   "share_id": "VNztG8KQlOceTFiFQLJDhZ_yA1",
   "pass_code_token": "qbRuXUmy6NAJzy+jNqY42PtV80o4hy40xfO50XsiLVbeYjw6Lhr32RoCLX8/03RiqmQeHzV6UwioF+NJtmtL1w==",
   "ancestor_ids": [],
   "file_ids": [
      "VNztFvAa6KizwGZWqhABR0FJA1"
   ],
   "specify_parent_id": True
})
headers = {
   'authorization': 'Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
   'x-captcha-token': 'ck0.8WMx_F7PXDk4BIHdNDJ6mCUrwqrz_nvN5PsGBsUQXP3AsN29pP8b4zLsTs5PBPgTH489Igc5dkbS0b40kLqPZtNB2gaPn9-f4qA_kD2ce1GxsiN3kN9opt5uvaST816TJo4DILX8Gxagjnvd6xmmeVrLUF8EbPTfgnFIKvdqdjFVvbKKW0LTSXhoCsGYAQ3FnFKMybYL6Iz8OGh4seu0-dD3tb-aMehXmdZwr30uozj7gidAhoiKBBLpitE5-K43PyMyY_3OZF2fL68HC057CHZRBwvP0e91g2lrhuRcnpWc4XUtAHFdookp9OeqKOpwycD-uiCOnk1oVC1xYPcSWZdFThbo9mHSNtpvLlEz2506czciZ8TJMMoNeGWQg_davjkM4ZhpOmraHC1k7PuXDSXOnp_Lv_k0Ke5z30qaguA.ClQIl6P87u0yEhBYcXAwa0pCWFdod2FUcEI2GgcxLjkxLjIwIg5wYW4ueHVubGVpLmNvbSogODI4ODQ3OWM0ZmI5ZWJlZDA5YWQ2YmI4ZTI2MDVjYWUSgAGF83neLeF-91YeSIUFX3csvYgu08vRHyjusn9u2K8GtUThDlSO4M567JDJJWOcO-GAzu3PtVEoNRebOfAXAla8CIDS3OclosTmLRfIFREMCyRT6U1_yN2ip79Ob3yrGjGluxO5cFbAI21-IeP2Ha-qrZA3n44n70lo1GVKxbUO5Q',
   'x-client-id': 'Xqp0kJBXWhwaTpB6',
   'x-device-id': '8288479c4fb9ebed09ad6bb8e2605cae',
   'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
   'Content-Type': 'application/json',
   'Accept': '*/*',
   'Host': 'api-pan.xunlei.com',
   'Connection': 'keep-alive'
}
conn.request("POST", "/drive/v1/share/restore", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))


"""响应
{
    "share_status": "OK",
    "share_status_text": "",
    "file_id": "",
    "restore_status": "RESTORE_START",
    "restore_task_id": "VOQTiI3KLAMwBfQDpLtef-YVA1",
    "params": {}
}
"""