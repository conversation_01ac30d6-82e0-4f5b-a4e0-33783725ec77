--  File: migrations/1_create_notify_trigger.sql
--  Description: This script creates a PostgreSQL trigger to enable real-time change data capture for the pan_resources table.
--  It sends a notification to the 'resource_changes' channel upon any INSERT, UPDATE, or DELETE operation.

-- 1. Create or replace the trigger function
-- This function constructs a JSON payload with the operation type and the resource_key of the changed row,
-- then sends it as a notification.
CREATE OR REPLACE FUNCTION notify_resource_change() RETURNS TRIGGER AS $$
DECLARE
  payload JSON;
  changed_record RECORD;
BEGIN
  -- Determine which record to use based on the operation (OLD for delete, NEW for insert/update)
  IF (TG_OP = 'DELETE') THEN
    changed_record := OLD;
  ELSE
    changed_record := NEW;
  END IF;

  -- Construct the JSON payload containing the table, operation, and the unique resource_key
  payload := json_build_object(
    'table', TG_TABLE_NAME,
    'operation', TG_OP,
    'resource_key', changed_record.resource_key
  );

  -- Notify the 'resource_changes' channel with the JSON payload converted to text.
  PERFORM pg_notify('resource_changes', payload::text);

  -- Return the appropriate record for the trigger to complete.
  RETURN changed_record;
END;
$$ LANGUAGE plpgsql;

-- 2. Drop the trigger if it already exists to ensure idempotency
-- This makes the script safe to re-run without causing errors.
DROP TRIGGER IF EXISTS pan_resources_notify_trigger ON pan_resources;

-- 3. Create the trigger on the pan_resources table
-- This trigger will fire AFTER any INSERT, UPDATE, or DELETE for each row.
CREATE TRIGGER pan_resources_notify_trigger
AFTER INSERT OR UPDATE OR DELETE ON pan_resources
FOR EACH ROW EXECUTE FUNCTION notify_resource_change();

-- 4. Add a comment to the trigger for better database schema documentation
COMMENT ON TRIGGER pan_resources_notify_trigger ON pan_resources
IS 'Notifies the "resource_changes" channel of any changes to the pan_resources table for Meilisearch sync.';

-- End of script 