'''
Author: dkon <EMAIL>
Date: 2025-05-17 22:49:38
LastEditors: dkon <EMAIL>
LastEditTime: 2025-05-17 22:49:55
FilePath: \pan-so-backend\app\services\迅雷新增文件夹.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
import requests
import json

url = "https://api-pan.xunlei.com/drive/v1/files"

payload = json.dumps({
   "parent_id": "",
   "name": "K 狂医魔徒 하이퍼 나이프 (2025) 4K2",
   "kind": "drive#folder",
   "space": ""
})
headers = {
   'authorization': 'Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
   'x-captcha-token': 'ck0.CDVO81Qgn6sA12c1ya3X5IQeZg8t49Com6obTYu0hdYjXW3ZIsgUIQEzoTQ6PwWd8dsXMQykHZoIb0OvcMSYJVCLIo8tVC4SapFGUYJn4d17emf2rG4TQBhYvg2EH9lEr87ACsEI72jCjEPSRtQum3Ld2OE3UAGU39JO6wJu1e8VDRRLKkL0IYKTSVfS0cWbbCjaqbfUU0hcJE4P08-Idiz4TbQ_oVmOYSL85seGsSEHf8IpVbRWHRx5YNMusb6215yi82sjisfkB2TRpRH3KCJIoN4m5Ri8YoAc_7X_Tx3Y8PSBRmgylKIpcgoF_U_bMKyP93gIWkOSzezrNyHT6hInnsLHjYuEWANnwV43fioaBgsZp4A2G9-x6jcDlmLoPxS46p5O2VapluqYoCLAJNSTBq3ew_DZnNiu55whPIo.ClQI7O7O9O0yEhBYcXAwa0pCWFdod2FUcEI2GgcxLjkxLjIwIg5wYW4ueHVubGVpLmNvbSogODI4ODQ3OWM0ZmI5ZWJlZDA5YWQ2YmI4ZTI2MDVjYWUSgAGhOLvRlVvYXXID5qTGfvu1f78A8rEkP45pR78xn-DoQ1j-VdNHbQTG70sXnjf9Ju4uz12F5z3EZL88o5IpqI-cTZq6E7rS0PYI9hRakiWEUsw7fRkW5xFR2uziBlmkivvuglW1U_2zEZHhK4AvSjsahtbYpzYMV9KmUe7Kwwauyw',
   'x-client-id': 'Xqp0kJBXWhwaTpB6',
   'x-device-id': '8288479c4fb9ebed09ad6bb8e2605cae',
   'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
   'Content-Type': 'application/json',
   'Accept': '*/*',
   'Host': 'api-pan.xunlei.com',
   'Connection': 'keep-alive'
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)



"""响应
{
    "upload_type": "UPLOAD_TYPE_UNKNOWN",
    "file": {
        "kind": "drive#folder",
        "id": "VOQT_dCbiRp5tnYO-W6LX8fwA1",
        "parent_id": "",
        "name": "K 狂医魔徒 하이퍼 나이프 (2025) 4K2",
        "user_id": "630012855",
        "size": "0",
        "revision": "0",
        "file_extension": "",
        "mime_type": "",
        "starred": false,
        "web_content_link": "",
        "created_time": "2025-05-17T22:12:12.263+08:00",
        "modified_time": "2025-05-17T22:12:12.263+08:00",
        "icon_link": "https://backstage-img-ssl.a.88cdn.com/36de21fd06b9ebdd5092b68652b6fc6118e23c2b",
        "thumbnail_link": "",
        "md5_checksum": "",
        "hash": "",
        "links": {},
        "phase": "PHASE_TYPE_COMPLETE",
        "audit": null,
        "medias": [],
        "trashed": false,
        "delete_time": "",
        "original_url": "",
        "params": {
            "platform_icon": "https://backstage-img-ssl.a.88cdn.com/05e4f2d4a751f1895746a15da2d391105418a66d"
        },
        "original_file_index": 0,
        "space": "",
        "apps": [],
        "writable": true,
        "folder_type": "NORMAL",
        "collection": null,
        "sort_name": "K 狂医魔徒 하이퍼 나이프 (0000002025) 0000000004K0000000002",
        "user_modified_time": "2025-05-17T22:12:12.263+08:00",
        "spell_name": [],
        "file_category": "OTHER",
        "tags": [],
        "reference_events": [],
        "reference_resource": null
    },
    "task": null
}
"""