# Telegram爬虫消息遗漏处理机制

## 概述

针对telethon监控频道消息时可能遗漏消息的问题，我们对TelegramCrawler进行了全面优化，实现了多层次的消息遗漏检测和恢复机制。

## 主要改进

### 1. 消息间隙检测机制
- **实时检测**：在每次收到消息时检查pts序列是否连续
- **自动恢复**：发现间隙时自动调用`getChannelDifference`恢复遗漏消息
- **重试控制**：限制每个频道的恢复尝试次数，避免无限重试

### 2. 定期主动检查
- **周期性轮询**：每5分钟主动检查频道是否有新消息
- **智能触发**：仅对长时间无活动的频道执行主动检查
- **大频道优化**：特别针对大型频道的更新推送限制问题

### 3. 连接状态监控
- **断线检测**：每30秒检查连接状态
- **自动重连**：连接断开时自动重连并执行catch_up
- **状态恢复**：重连后恢复所有频道状态

### 4. 启动时补偿机制
- **离线补偿**：启动时自动执行catch_up获取离线期间的消息
- **状态初始化**：为每个频道初始化pts状态跟踪
- **历史恢复**：确保不遗漏服务停止期间的消息

## 新增配置选项

```yaml
telegram_crawler:
  # 消息遗漏处理配置
  enable_catch_up: true                    # 是否启用catch_up机制
  periodic_check_interval: 300             # 定期检查间隔(秒)
  connection_check_interval: 30            # 连接检查间隔(秒)
  max_gap_recovery_attempts: 3             # 最大间隙恢复尝试次数
```

## 工作原理

### 消息序列检测
1. 每个频道维护独立的pts状态
2. 收到消息时检查：`local_pts + pts_count === remote_pts`
3. 如果不等，说明存在间隙，触发恢复机制

### 三重保障策略
1. **实时监控**：正常的事件监听机制
2. **间隙检测**：实时检测并恢复消息间隙
3. **定期轮询**：主动检查长时间无活动的频道

### 大频道处理
- 对于大型频道（如50万订阅者），Telegram不会实时推送所有更新
- 通过定期调用`getChannelDifference`主动获取遗漏的消息
- 智能判断频道活跃度，避免过度轮询

## 使用方法

### 1. 更新配置文件
将新的配置选项添加到你的`config.yaml`中：

```yaml
telegram_crawler:
  enable_catch_up: true
  periodic_check_interval: 300  # 可根据需要调整
  connection_check_interval: 30
  max_gap_recovery_attempts: 3
```

### 2. 重启服务
重启telegram监控服务，新的机制将自动生效。

### 3. 监控日志
观察日志输出，关注以下信息：
- 间隙检测和恢复日志
- 定期检查结果
- 连接状态变化

## 日志示例

```
[INFO] 频道 @example_channel 发现消息间隙: 本地pts=100, 远程pts=105, 间隙大小=5
[INFO] 开始恢复频道 -1001234567890 的消息间隙...
[INFO] 频道 -1001234567890 间隙恢复完成，恢复了 3 个资源
[INFO] 频道 @example_channel 已 16.2 分钟无活动，执行主动检查
[INFO] 定期检查频道 @example_channel 发现 2 个新资源
```

## 性能影响

### 资源消耗
- **内存**：每个频道增加约100字节状态存储
- **网络**：定期检查会产生少量额外API调用
- **CPU**：间隙检测逻辑开销极小

### 优化措施
- 智能轮询：仅对无活动频道执行主动检查
- 限制重试：避免无限恢复尝试
- 批量处理：单次恢复最多100条消息

## 注意事项

1. **API限制**：频繁的`getChannelDifference`调用可能触发API限制
2. **大频道延迟**：大型频道的消息可能仍有几分钟延迟
3. **网络稳定性**：代理不稳定可能影响恢复效果
4. **配置调优**：根据实际情况调整检查间隔

## 故障排除

### 常见问题
1. **间隙恢复失败**：检查频道权限和网络连接
2. **过多API调用**：适当增加检查间隔
3. **重复消息**：正常现象，系统会自动去重

### 调试建议
1. 启用详细日志：`logging.getLogger("telethon").setLevel(logging.DEBUG)`
2. 监控API调用频率
3. 检查频道访问权限

## 总结

通过实现多层次的消息遗漏检测和恢复机制，大大提高了telegram监控的可靠性。虽然无法100%避免遗漏（受Telegram API限制），但可以将遗漏率降到最低。
