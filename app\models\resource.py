from tortoise import fields, models


class PanResource(models.Model):
    id = fields.IntField(pk=True)
    resource_key = fields.CharField(max_length=50, unique=True)  # 如 "99dcc54b7603"
    pan_type = fields.IntField()  # 1=百度网盘, 2=夸克网盘
    original_url = fields.CharField(max_length=255)  # 原始网盘链接
    title = fields.CharField(max_length=255, null=True)  # 资源标题

    # --- 新增/修改的字段 ---
    is_parsed = fields.BooleanField(
        default=False, description="是否已通过云盘服务解析并填充详细信息"
    )
    author = fields.Char<PERSON>ield(max_length=255, null=True, description="资源分享者或作者")
    author_avatar = fields.Char<PERSON>ield(
        max_length=512, null=True, description="作者头像URL"
    )
    is_mine = fields.BooleanField(default=False, description="是否由本人上传")

    thumbnail = fields.Char<PERSON>ield(max_length=512, null=True, description="资源缩略图URL")

    verified_status = fields.CharField(max_length=20, null=True)  # 验证状态
    share_url = fields.CharField(max_length=255, null=True)  # 转存后的分享链接
    share_pwd = fields.CharField(max_length=128, null=True)  # 分享密码

    created_at = fields.DatetimeField(auto_now_add=True, null=True)
    updated_at = fields.DatetimeField(
        auto_now=False, null=True
    )  # 更新时间，对应爬虫的publish_time
    expiry_date = fields.DatetimeField(null=True)  # 有效期截止日期

    file_type = fields.CharField(max_length=20, null=True)  # 文件类型
    file_size = fields.CharField(max_length=20, null=True)  # 文件大小
    access_count = fields.IntField(default=0)  # 访问次数
    text_content = fields.TextField(null=True)  # 资源预览内容

    class Meta:
        table = "pan_resources"

    def __str__(self):
        return f"{self.title} ({self.resource_key})"
