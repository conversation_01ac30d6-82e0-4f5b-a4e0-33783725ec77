import asyncio
import httpx
import logging
from typing import Dict, Any

from app.utils.config import settings

logger = logging.getLogger("panku8-service")

# 从配置加载盘酷吧爬虫相关配置
PANKU8_CONF = settings.get("panku8_crawler", {})
VIP_COOKIE = PANKU8_CONF.get("vip_cookie", "")
DEFAULT_TIMEOUT = PANKU8_CONF.get("default_timeout", 30.0)


class Panku8Service:
    """
    盘酷吧服务类，提供发帖功能
    """

    def __init__(self):
        """初始化盘酷吧服务"""
        logger.info("初始化盘酷吧服务...")
        self.post_url = "https://panku8.com/m/user/resource_post"
        self.session_cookie_id = VIP_COOKIE

        if not self.session_cookie_id:
            logger.error("盘酷吧 VIP_COOKIE 未在配置中设置！")

        self.headers = {
            "accept": "application/json, text/javascript, */*; q=0.01",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
            "origin": "https://panku8.com",
            "referer": "https://panku8.com/m/user/resource_add",
            "sec-ch-ua": '"Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "x-requested-with": "XMLHttpRequest",
        }
        request_headers = self.headers.copy()
        if VIP_COOKIE:
            request_headers["cookie"] = VIP_COOKIE
        self.cookies = {"HYPERF_SESSION_ID": self.session_cookie_id}
        self.headers = request_headers
        logger.info("盘酷吧服务初始化完成")

    async def post_resource(
        self, title: str, url: str, code: str, introduce: str
    ) -> Dict[str, Any]:
        """
        发布新资源到盘酷吧

        Args:
            title (str): 帖子标题
            url (str): 资源链接 (转存后的)
            code (str): 提取码
            introduce (str): 内容介绍

        Returns:
            Dict[str, Any]: 发布结果
        """
        if not self.session_cookie_id:
            return {"status": "error", "message": "盘酷吧 Cookie 未配置，无法发布"}

        data = {"title": title, "url": url, "code": code, "introduce": introduce}

        try:
            async with httpx.AsyncClient(timeout=DEFAULT_TIMEOUT) as client:
                response = await client.post(
                    self.post_url, headers=self.headers, data=data
                )
                response.raise_for_status()  # Raise an exception for bad status codes

                response_data = response.json()
                logger.info(f"盘酷吧返回数据: {response_data}")
                # 根据盘酷吧实际返回的成功标识来判断
                # {"code":0,"message":"OK","result":null}r
                if response_data.get("code") == 0:
                    logger.info(f"成功发布到盘酷吧: '{title}'")
                    return {
                        "status": "success",
                        "message": response_data.get("message", "发布成功"),
                    }
                else:
                    error_message = response_data.get("message", "未知错误")
                    logger.error(f"发布到盘酷吧失败: '{title}'. 原因: {error_message}")
                    return {"status": "error", "message": error_message}

        except httpx.HTTPStatusError as e:
            logger.error(
                f"发布到盘酷吧时发生HTTP错误: {e.response.status_code} - {e.response.text}"
            )
            return {"status": "error", "message": f"HTTP错误: {e.response.status_code}"}
        except Exception as e:
            logger.error(f"发布到盘酷吧时发生异常: {e}", exc_info=True)
            return {"status": "error", "message": f"发生异常: {str(e)}"}


# 创建单例实例
panku8_service = Panku8Service()