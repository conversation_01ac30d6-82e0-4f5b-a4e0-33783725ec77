import asyncio
import json
import logging
import os
import random
import re
import time
from datetime import datetime
from functools import wraps
from typing import Any, Dict, List, Optional

from playwright.async_api import TimeoutError as PlaywrightTimeoutError
from playwright.async_api import async_playwright

from app.models.resource import PanResource
from app.services.baidu_pan_service import baidu_pan_service
from app.services.panku8_service import panku8_service
from app.services.quark_pan_service import quark_pan_service
from app.utils.config import settings
from app.utils.pan_url_parser import parse_pan_url
from app.utils.common import infer_file_type, get_normalized_file_type

logger = logging.getLogger("kdocs-crawler")

# 从配置文件获取爬虫配置
CRAWLER_CONF = settings.get("kdocs_crawler", {})
COOKIE = CRAWLER_CONF.get("cookie")
DOC_URLS = CRAWLER_CONF.get("doc_urls", [])
ENABLED = CRAWLER_CONF.get("enabled", False)
AUTO_POST_RESOURCES = CRAWLER_CONF.get("auto_post_resources", True)
AUTO_SAVE_RESOURCES = CRAWLER_CONF.get("auto_save_resources", False)
DATA_FILE_PATH_CONFIG = CRAWLER_CONF.get("data_file_path", "data/kdocs_data.json")
# scrape_limit_per_doc 用于直接入库归档的数量
SCRAPE_LIMIT_PER_DOC = CRAWLER_CONF.get("scrape_limit_per_doc", 0)
# post_limit_per_run 用于进入发布流程的数量
POST_LIMIT_PER_RUN = CRAWLER_CONF.get("post_limit_per_run", 20)
SAVE_LIMIT_PER_RUN = CRAWLER_CONF.get("save_limit_per_run", 5)

# 将相对路径转换为基于项目根目录的绝对路径
DATA_FILE_PATH = os.path.abspath(
    os.path.join(os.path.dirname(__file__), "..", "..", DATA_FILE_PATH_CONFIG)
)


def timing_decorator(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        result = await func(*args, **kwargs)
        end_time = time.time()
        elapsed_time = end_time - start_time
        logger.info(f"执行 {func.__name__} 用时: {elapsed_time:.4f}秒")
        return result

    return wrapper


class KdocsCrawler:
    """
    Kdocs爬虫，融合了滚动抓取、智能数据更新与资源发布功能。
    - 抓取: 使用Playwright模拟滚动抓取长文档。
    - 数据分流: 根据配置将抓取数据分为"发布流"和"归档流"。
    - 发布: "发布流"数据进入JSON文件，根据"夸克优先"和"链接变更"规则进行发布。
    - 归档: "归档流"数据直接存入数据库。
    """

    def __init__(self):
        """
        初始化Kdocs爬虫。
        """
        logger.info("Initializing KdocsCrawler...")
        self.doc_urls = DOC_URLS
        self.cookie = COOKIE
        self.scrape_limit = SCRAPE_LIMIT_PER_DOC
        self.post_limit = POST_LIMIT_PER_RUN
        self.save_limit = SAVE_LIMIT_PER_RUN
        self.data_file_path = DATA_FILE_PATH
        self.data = []
        self.data_last_mtime = 0

        # 服务注入
        self.baidu_pan_service = baidu_pan_service
        self.quark_pan_service = quark_pan_service
        self.panku8_service = panku8_service

        self._load_data()
        logger.info(f"Kdocs data file path set to: {self.data_file_path}")
        if not ENABLED:
            logger.warning("kdocs爬虫已在配置中禁用。相关功能将不可用。")

    def _sanitize_title(self, title: str) -> str:
        """去除标题中的特殊符号，仅保留指定字符集。"""
        # 允许汉字、数字、字母、空格、.、冒号、大小括号、中括号
        pattern = r"[^\u4e00-\u9fa5a-zA-Z0-9 \.\、:：\(\)（）\[\]【】]"
        return re.sub(pattern, "", title)

    def _load_data(self):
        """从JSON文件加载用于发布的数据。"""
        if not os.path.exists(self.data_file_path):
            logger.warning(f"发布数据文件 {self.data_file_path} 不存在，将创建新文件。")
            self.data = []
            return

        try:
            with open(self.data_file_path, "r", encoding="utf-8") as f:
                self.data = json.load(f)
            logger.info(
                f"成功从 {self.data_file_path} 加载了 {len(self.data)} 条记录。"
            )
        except (json.JSONDecodeError, OSError) as e:
            logger.warning(
                f"无法加载发布数据文件 {self.data_file_path}: {e}，将使用空数据。"
            )
            self.data = []

    def _save_data(self):
        """将内存中的发布数据保存到JSON文件"""
        try:
            os.makedirs(os.path.dirname(self.data_file_path), exist_ok=True)
            with open(self.data_file_path, "w", encoding="utf-8") as f:
                json.dump(self.data, f, ensure_ascii=False, indent=4)
            logger.info(f"发布数据成功保存到 {self.data_file_path}")
        except Exception as e:
            logger.error(f"保存发布数据到 {self.data_file_path} 失败: {e}")

    async def _scrape_all_new_resources(self) -> List[Dict]:
        """
        核心Playwright爬取逻辑。提取所有资源，合并百度和夸克链接到同一条目。
        会边滚动边解析，直到达到抓取上限或无法加载更多内容。
        """
        logger.info("正在启动 Playwright 执行页面抓取 (边滚边采策略)...")
        # 使用 scrape_limit_per_doc 控制每个文档抓取上限
        limit_per_doc = self.scrape_limit
        if limit_per_doc > 0:
            logger.info(
                f"每个文档将最多抓取 {limit_per_doc} 条新资源（由 scrape_limit_per_doc 控制）。"
            )
        else:
            logger.info("每个文档抓取数量无限制。")

        all_found_resources = []

        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                context = await browser.new_context(
                    viewport={"width": 1920, "height": 1080}
                )
                if self.cookie:
                    cookies = [
                        {"name": k, "value": v, "domain": ".kdocs.cn", "path": "/"}
                        for k, v in [
                            p.strip().split("=", 1)
                            for p in self.cookie.split(";")
                            if "=" in p
                        ]
                    ]
                    await context.add_cookies(cookies)
                    logger.info("成功加载Cookie。")

                for doc_url in self.doc_urls:
                    unique_titles_per_doc = set()  # 每个文档独立的去重集合
                    resources_from_this_doc_count = 0
                    page = await context.new_page()
                    try:
                        logger.info(f"正在导航到: {doc_url}")
                        await page.goto(
                            doc_url, wait_until="networkidle", timeout=60000
                        )
                        await page.wait_for_selector("div.block_tile", timeout=30000)

                        # --- 重构为真正的"边滚边采"逻辑 ---
                        consecutive_timeouts = 0
                        max_consecutive_timeouts = 5  # 连续超时3次则认为到底
                        processed_text_lines = set()
                        JUNK_STRINGS = ["----", "注意:", "登录", "搜索方法"]
                        current_item = {}

                        def add_item_to_list(force_add=False):
                            nonlocal current_item, resources_from_this_doc_count
                            if not current_item or not current_item.get("title"):
                                return False

                            has_link = current_item.get(
                                "baidu_link"
                            ) or current_item.get("quark_link")

                            if has_link or force_add:
                                title = current_item["title"].strip()
                                if title and title not in unique_titles_per_doc:
                                    all_found_resources.append(current_item)
                                    unique_titles_per_doc.add(title)
                                    resources_from_this_doc_count += 1
                                    logger.info(
                                        f"  > 发现资源 ({resources_from_this_doc_count}/{limit_per_doc or '无限制'}): {title}"
                                    )
                                    if (
                                        limit_per_doc > 0
                                        and resources_from_this_doc_count
                                        >= limit_per_doc
                                    ):
                                        return True
                            return False

                        while True:
                            if (
                                limit_per_doc > 0
                                and resources_from_this_doc_count >= limit_per_doc
                            ):
                                logger.info(
                                    f"已达到文档抓取上限({limit_per_doc})，停止滚动和解析。"
                                )
                                break

                            initial_block_count = await page.locator(
                                "div.block_tile"
                            ).count()

                            # 解析当前可见的、未处理过的内容
                            all_blocks = await page.locator("div.block_tile").all()
                            for block in all_blocks:
                                try:
                                    block_text = (await block.inner_text()).strip()
                                    if (
                                        not block_text
                                        or block_text in processed_text_lines
                                    ):
                                        continue

                                    processed_text_lines.add(block_text)

                                    is_junk = any(
                                        junk in block_text for junk in JUNK_STRINGS
                                    )
                                    is_link_line = (
                                        "链接" in block_text
                                        or "提取码" in block_text
                                        or "http" in block_text
                                    )

                                    if not is_junk and not is_link_line:
                                        if add_item_to_list(force_add=True):
                                            break
                                        sanitized_title = self._sanitize_title(
                                            block_text
                                        )
                                        current_item = {"title": sanitized_title}
                                    elif "百度链接" in block_text and current_item:
                                        link_loc = block.locator("a")
                                        if await link_loc.count() > 0:
                                            current_item["baidu_link"] = (
                                                await link_loc.get_attribute("href")
                                            )
                                    elif "夸克链接" in block_text and current_item:
                                        link_loc = block.locator("a")
                                        if await link_loc.count() > 0:
                                            current_item["quark_link"] = (
                                                await link_loc.get_attribute("href")
                                            )

                                except Exception as e:
                                    logger.warning(f"解析单个内容块时出错: {e}")

                            if (
                                limit_per_doc > 0
                                and resources_from_this_doc_count >= limit_per_doc
                            ):
                                logger.info("解析中达到抓取上限，停止滚动。")
                                break

                            # 滚动并等待新内容
                            await page.keyboard.press("PageDown")
                            try:
                                await page.wait_for_function(
                                    f"document.querySelectorAll('div.block_tile').length > {initial_block_count}",
                                    timeout=10000,
                                )
                                logger.info(f"检测到新内容块，继续解析。")
                                consecutive_timeouts = 0
                            except PlaywrightTimeoutError:
                                consecutive_timeouts += 1
                                logger.info(
                                    f"未检测到内容块数量增加 (连续超时: {consecutive_timeouts}/{max_consecutive_timeouts})。"
                                )
                                if consecutive_timeouts >= max_consecutive_timeouts:
                                    logger.info(
                                        f"连续 {max_consecutive_timeouts} 次未加载出新内容，结束当前文档。"
                                    )
                                    break

                        # 确保最后一个条目被添加
                        add_item_to_list(force_add=True)

                    except Exception as e:
                        logger.error(f"处理文档 {doc_url} 时出错: {e}", exc_info=True)
                    finally:
                        await page.close()

        except Exception as e:
            logger.error(f"Playwright 执行失败: {e}", exc_info=True)

        logger.info(f"页面抓取完成，共发现 {len(all_found_resources)} 个资源。")
        return all_found_resources

    async def _archive_resources_to_db(self, resources: List[Dict]):
        """将资源归档至数据库，对已存在资源会检查并更新标题。"""
        if not resources:
            logger.info("没有需要归档到数据库的资源。")
            return

        logger.info(f"准备将 {len(resources)} 条资源归档到数据库...")
        new_db_entries = []
        updated_count = 0
        for i, res in enumerate(resources):
            title = res.get("title")
            if not title:
                logger.warning("    -> 跳过，资源缺少标题。")
                continue

            logger.info(f"  [归档检查 {i+1}/{len(resources)}] 正在处理: '{title}'")

            # 将夸克和百度链接分别处理
            links_to_process = []
            if res.get("quark_link"):
                links_to_process.append(
                    {"url": res["quark_link"], "pan_type": 2, "pan_type_name": "夸克"}
                )
            if res.get("baidu_link"):
                links_to_process.append(
                    {"url": res["baidu_link"], "pan_type": 1, "pan_type_name": "百度"}
                )

            if not links_to_process:
                logger.warning(f"    -> 跳过 '{title}'，因为它没有任何有效的网盘链接。")
                continue

            for link_info in links_to_process:
                url = link_info["url"]
                pan_type = link_info["pan_type"]
                pan_type_name = link_info["pan_type_name"]

                logger.info(f"    -> 正在检查 {pan_type_name} 链接: {url}")

                parsed_info = parse_pan_url(url)
                if not parsed_info or not parsed_info.get("resource_key"):
                    logger.warning(
                        f"    -> 跳过 {pan_type_name} 链接，无法从URL解析resource_key: {url}"
                    )
                    continue

                resource_key = parsed_info["resource_key"]
                safe_title = title[:255]

                existing_resource = await PanResource.get_or_none(
                    resource_key=resource_key
                )

                if existing_resource:
                    update_fields = []

                    # 检查并更新标题
                    if existing_resource.title != safe_title:
                        logger.info(
                            f"    -> {pan_type_name} 资源标题已变更，正在更新。旧: '{existing_resource.title}', 新: '{safe_title}'"
                        )
                        existing_resource.title = safe_title
                        update_fields.append("title")

                    # 检查并更新文件类型（如果为空）
                    if existing_resource.file_type is None:
                        existing_resource.file_type = "video"  # 直接设置为video
                        logger.info(f"    -> 为已存在资源设置文件类型为: video")
                        update_fields.append("file_type")

                    # 检查并更新is_mine字段（kdoc资源应该标记为个人资源）
                    if not existing_resource.is_mine:
                        logger.info(
                            f"    -> {pan_type_name} 资源is_mine字段需要更新为True（kdoc资源标记为个人资源）"
                        )
                        existing_resource.is_mine = True
                        update_fields.append("is_mine")

                    # 更新时间戳
                    if update_fields:
                        existing_resource.updated_at = datetime.now()
                        update_fields.append("updated_at")
                        await existing_resource.save(update_fields=update_fields)
                        updated_count += 1
                    else:
                        logger.info(
                            f"    -> {pan_type_name} 资源已存在且无需更新，跳过。"
                        )
                else:
                    logger.info(f"    -> 新的 {pan_type_name} 资源，准备添加入库。")

                    # 直接设置file_type为video，不再进行推断
                    file_type = "video"
                    logger.info(f"        -> 设置文件类型为: {file_type}")

                    new_db_entries.append(
                        PanResource(
                            resource_key=resource_key,
                            title=safe_title,
                            original_url=url,
                            pan_type=pan_type,
                            file_type=file_type,  # 直接使用"video"
                            updated_at=datetime.now(),
                            is_mine=True,  # kdoc资源标记为个人资源
                        )
                    )
        if new_db_entries:
            await PanResource.bulk_create(new_db_entries)

        if updated_count > 0 or new_db_entries:
            logger.info(
                f"数据库归档完成：新增 {len(new_db_entries)} 条，更新 {updated_count} 条。"
            )
        else:
            logger.info("数据库归档完成：没有发现需要新增或更新的资源。")

    async def _process_posting_queue(self, resources: List[Dict]):
        """处理待发布队列，根据规则更新JSON文件。"""
        if not resources:
            logger.info("没有需要处理的发布队列资源。")
            return

        logger.info(f"开始处理 {len(resources)} 条待发布资源的JSON文件更新...")
        self._load_data()
        old_resources_by_title = {
            res["title"]: res for res in self.data if res.get("title")
        }

        final_data = []
        new_titles = {res["title"] for res in resources}

        # 遍历新抓取的待发布资源
        for new_res in resources:
            title = new_res.get("title")
            if not title:
                continue

            old_res = old_resources_by_title.get(title)
            if old_res:
                # 资源已存在，检查链接是否更新
                new_b_link = new_res.get("baidu_link")
                new_q_link = new_res.get("quark_link")
                old_b_link = old_res.get("baidu_link")
                old_q_link = old_res.get("quark_link")

                if new_b_link != old_b_link or new_q_link != old_q_link:
                    logger.info(f"资源链接更新: '{title}'。标记为重新处理。")
                    # 链接已更新，添加新记录，重置所有状态
                    final_data.append(
                        {"is_saved": False, "posted_to_panku8": False, **new_res}
                    )
                else:
                    # 链接未变，保留旧记录以维持发布状态，仅更新标题（如果需要）
                    old_res["title"] = title  # 确保标题是最新的
                    final_data.append(old_res)
            else:
                # 全新资源
                logger.info(f"发现全新待发布资源: '{title}'。")
                final_data.append(
                    {"is_saved": False, "posted_to_panku8": False, **new_res}
                )

        # 保留那些在新抓取中未出现的旧资源
        for old_title, old_res in old_resources_by_title.items():
            if old_title not in new_titles:
                final_data.append(old_res)

        self.data = final_data
        self._save_data()

    @timing_decorator
    async def update_data_file_and_archive(self):
        """
        抓取最新数据，进行分流：
        - 前 post_limit_per_run 条进入发布流程 (更新JSON)
        - 其余的 (scrape_limit_per_doc) 直接归档入库
        """
        if not ENABLED:
            logger.info("Kdocs爬虫已禁用，跳过数据更新与归档。")
            return

        logger.info("开始执行Kdocs数据抓取、分流与归档...")

        new_resources = await self._scrape_all_new_resources()
        if not new_resources:
            logger.warning("未从Kdocs抓取到任何资源，流程终止。")
            return

        # 新增：对抓取结果进行去重
        deduped_resources = []
        seen_titles = set()
        for r in new_resources:
            title = r.get("title")
            if title and title not in seen_titles:
                deduped_resources.append(r)
                seen_titles.add(title)
        logger.info(
            f"原始抓取到 {len(new_resources)} 条资源, 去重后得到 {len(deduped_resources)} 条独立资源。"
        )
        new_resources = deduped_resources

        # 数据分流
        # 发布队列现在包含所有新抓取的资源
        posting_queue = new_resources
        # 归档队列则包含所有新抓取的资源
        archiving_queue = new_resources
        logger.info(
            f"数据分流: 总抓取 {len(new_resources)} 条, "
            f"进入发布/JSON处理队列 {len(posting_queue)} 条, "
            f"进入归档队列 {len(archiving_queue)} 条。"
        )

        await self._process_posting_queue(posting_queue)
        await self._archive_resources_to_db(archiving_queue)

    @timing_decorator
    async def process_and_post_resources(self):
        """
        处理待处理队列中的资源，根据配置进行转存和发布。
        - 依赖 `auto_save_resources` 参数，若为false则不执行任何操作。
        - 若 `auto_save_resources` 为true，则对指定数量的资源尝试转存其所有链接。
        - 若 `auto_post_resources` 也为true，则在转存后尝试使用夸克链接发布。
        """
        if not ENABLED:
            logger.info("Kdocs爬虫已禁用，跳过资源处理。")
            return

        if not AUTO_SAVE_RESOURCES:
            logger.info("Kdocs爬虫自动转存功能已禁用，跳过转存和发布流程。")
            return

        logger.info("开始处理资源队列（转存/发布）...")
        self._load_data()

        # Step 1: 优先处理需要转存的资源
        resources_to_save = [
            res
            for res in self.data
            if not res.get("is_saved")
            and (res.get("quark_link") or res.get("baidu_link"))
        ]
        # 使用 save_limit_per_run 控制本次最多转存多少条资源
        save_limit = self.save_limit
        if save_limit > 0:
            logger.info(
                f"本次最多转存 {save_limit} 条资源（由 save_limit_per_run 控制）。"
            )
            resources_to_save = resources_to_save[:save_limit]
        else:
            logger.info("本次转存数量无限制。")
        if not resources_to_save:
            logger.info("没有需要转存的新资源。")
        else:
            logger.info(f"共发现 {len(resources_to_save)} 条待转存资源，开始处理...")
            for resource in resources_to_save:
                title = resource.get("title")
                if not title:
                    logger.warning("发现无标题资源，跳过转存。")
                    continue

                logger.info(f"--- 开始转存资源: '{title}' ---")
                has_saved_successfully = False

                # 1.1 转存夸克链接
                quark_link = resource.get("quark_link")
                if quark_link and not resource.get("quark_save_url"):
                    logger.info("  > 正在尝试转存夸克链接...")
                    save_result = await self.quark_pan_service.save_shared_file(
                        share_url=quark_link,
                        save_path="转存专用",
                        expiry_days=1,
                        enable_rename=True,
                    )
                    if save_result and save_result.get("status") == "success":
                        saved_url = save_result.get("share_url")
                        resource["quark_save_url"] = saved_url
                        has_saved_successfully = True
                        logger.info(f"  > 夸克链接转存成功: {saved_url}")
                    else:
                        logger.error(
                            f"  > 夸克链接转存失败: {save_result.get('message', '未知错误')}"
                        )

                # 1.2 转存百度链接
                baidu_link = resource.get("baidu_link")
                if baidu_link and not resource.get("baidu_save_url"):
                    logger.info("  > 正在尝试转存百度链接...")
                    save_result = await self.baidu_pan_service.save_shared_file(
                        share_url=baidu_link, save_path="/转存专用", expiry_days=0
                    )
                    if save_result and save_result.get("status") == "success":
                        saved_url = save_result.get("share_url")
                        resource["baidu_save_url"] = saved_url
                        has_saved_successfully = True
                        logger.info(f"  > 百度链接转存成功: {saved_url}")
                    else:
                        logger.error(
                            f"  > 百度链接转存失败: {save_result.get('message', '未知错误')}"
                        )

                if has_saved_successfully:
                    resource["is_saved"] = True
                    logger.info(f"  > 资源 '{title}' 已成功转存，更新状态。")
                    self._save_data()  # 立即保存状态

                logger.info("处理下一个资源前随机等待1-3秒...")
                await asyncio.sleep(random.uniform(1, 3))

        # Step 2: 处理需要发布的资源
        if not AUTO_POST_RESOURCES:
            logger.info("自动发布功能已禁用，跳过发布流程。")
            return

        resources_to_post = [
            res
            for res in self.data
            if res.get("is_saved") and not res.get("posted_to_panku8")
        ]
        if not resources_to_post:
            logger.info("没有需要发布的新资源。")
            return

        logger.info(f"共发现 {len(resources_to_post)} 条待发布资源，开始处理...")
        successful_posts = 0
        for resource in resources_to_post:
            title = resource.get("title")
            quark_save_url = resource.get("quark_save_url")

            if not title or not quark_save_url:
                logger.warning(f"资源 '{title}' 缺少标题或夸克转存链接，跳过发布。")
                continue

            logger.info(f"--- 开始发布资源: '{title}' ---")
            post_result = await self.panku8_service.post_resource(
                title=title, url=quark_save_url, code="", introduce=title
            )
            if post_result and post_result.get("status") == "success":
                logger.info(f"  > 资源 '{title}' 成功发布！")
                resource["posted_to_panku8"] = True
                successful_posts += 1
                self._save_data()
            else:
                logger.error(
                    f"  > 资源 '{title}' 发布失败: {post_result.get('message', '未知错误')}"
                )

            logger.info("处理下一个资源前随机等待1-3秒...")
            await asyncio.sleep(random.uniform(1, 3))

        logger.info(f"发布流程处理完毕，共成功发布 {successful_posts} 条。")

    @timing_decorator
    async def run_update_and_post_task(self):
        """
        为外部调度器提供的统一入口，执行完整的更新、归档与发布流程。
        """
        logger.info("====== 开始执行Kdocs完整更新、归档与发布任务 ======")
        await self.update_data_file_and_archive()
        await self.process_and_post_resources()
        logger.info("====== Kdocs完整更新、归档与发布任务执行完毕 ======")

    def _get_pan_type_code(self, pan_type_str: str) -> int:
        """将网盘类型字符串转换为代码"""
        return (
            1
            if pan_type_str.lower() == "baidu"
            else 2 if pan_type_str.lower() == "quark" else 0
        )

    def _check_and_reload_data_if_updated(self):
        """检查发布数据文件是否已更新，如果是，则重新加载。"""
        try:
            if not os.path.exists(self.data_file_path):
                return
            mtime = os.path.getmtime(self.data_file_path)
            if mtime > self.data_last_mtime:
                logger.info(f"检测到 {self.data_file_path} 已从外部更新，重新加载...")
                self._load_data()
                self.data_last_mtime = mtime
        except OSError as e:
            logger.warning(f"检查文件更新时出错: {e}")


kdocs_crawler = KdocsCrawler()
