"""
配置管理API路由
"""

from fastapi import APIRouter, HTTPException, status, Depends, Query, Request
from typing import Optional, List, Dict, Any
import logging
from datetime import datetime

from app.models.config_models import (
    ConfigUpdateRequest,
    BatchConfigUpdateRequest,
    ConfigBackupRequest,
    ConfigResetRequest,
    ConfigValidationRequest,
    ConfigListResponse,
    ConfigCategory,
    ConfigItem,
    ConfigBackupListResponse,
    ConfigBackupInfo,
    ConfigHistoryResponse,
    ConfigChangeLogInfo,
    ValidationResult,
    ConfigBackup,
    ConfigChangeLog,
    ConfigDiffResponse,
    RestartStatusResponse,
)
from app.models.user import User
from app.core.permissions import RequireSystemConfig
from app.services.config_service import config_service, config_validator

logger = logging.getLogger("config-api")
router = APIRouter(tags=["配置管理"])


def get_client_ip(request: Request) -> str:
    """获取客户端IP地址"""
    forwarded = request.headers.get("X-Forwarded-For")
    if forwarded:
        return forwarded.split(",")[0].strip()
    return request.client.host if request.client else "unknown"


def get_user_agent(request: Request) -> str:
    """获取用户代理"""
    return request.headers.get("User-Agent", "unknown")


@router.get(
    "/config",
    response_model=ConfigListResponse,
    summary="获取配置列表",
    description="获取所有配置项，支持分类筛选和搜索",
)
async def get_config_list(
    category: Optional[str] = Query(None, description="配置分类筛选"),
    search: Optional[str] = Query(None, description="搜索配置项"),
    show_sensitive: bool = Query(False, description="是否显示敏感信息"),
    current_user: User = Depends(RequireSystemConfig),
):
    """获取配置列表"""
    try:
        # 加载配置
        config_data = await config_service.load_config()

        # 构建配置分类
        categories = []
        total_count = 0
        restart_required = bool(config_service._restart_required_keys)

        for cat_name, cat_info in config_service.CONFIG_CATEGORIES.items():
            # 分类筛选
            if category and category != cat_name:
                continue

            # 获取该分类下的配置项
            cat_config = config_data.get(cat_name, {})
            if not isinstance(cat_config, dict):
                continue

            configs = []
            for key, value in cat_config.items():
                full_key = f"{cat_name}.{key}"

                # 搜索筛选
                if (
                    search
                    and search.lower() not in full_key.lower()
                    and search.lower() not in str(value).lower()
                ):
                    continue

                # 敏感信息处理
                display_value = value
                is_sensitive = config_service.is_sensitive_config(full_key)
                if is_sensitive and not show_sensitive:
                    display_value = config_service.mask_sensitive_value(full_key, value)

                # 获取验证规则
                validation_rules = config_validator.VALIDATION_RULES.get(full_key, {})

                config_item = ConfigItem(
                    key=full_key,
                    display_name=key.replace("_", " ").title(),
                    value=display_value,
                    type=type(value).__name__.lower(),
                    required=validation_rules.get("required", False),
                    sensitive=is_sensitive,
                    description=validation_rules.get("description"),
                    validation_rules=validation_rules,
                    effect_type=config_service.get_effect_type(full_key),
                )
                configs.append(config_item)
                total_count += 1

            if configs:  # 只添加有配置项的分类
                category_obj = ConfigCategory(
                    name=cat_name,
                    display_name=cat_info["display_name"],
                    description=cat_info["description"],
                    icon=cat_info.get("icon"),
                    configs=configs,
                )
                categories.append(category_obj)

        return ConfigListResponse(
            categories=categories,
            total_count=total_count,
            restart_required=restart_required,
        )

    except Exception as e:
        logger.error(f"获取配置列表失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取配置列表失败: {str(e)}")


@router.get(
    "/config/{config_key:path}",
    summary="获取配置详情",
    description="获取单个配置项的详细信息",
)
async def get_config_detail(
    config_key: str, current_user: User = Depends(RequireSystemConfig)
):
    """获取配置详情"""
    try:
        config_data = await config_service.load_config()
        value = config_service.get_config_value(config_data, config_key)

        if value is None:
            raise HTTPException(status_code=404, detail="配置项不存在")

        # 敏感信息处理
        is_sensitive = config_service.is_sensitive_config(config_key)
        display_value = (
            config_service.mask_sensitive_value(config_key, value)
            if is_sensitive
            else value
        )

        # 获取验证规则
        validation_rules = config_validator.VALIDATION_RULES.get(config_key, {})

        config_item = ConfigItem(
            key=config_key,
            display_name=config_key.split(".")[-1].replace("_", " ").title(),
            value=display_value,
            type=type(value).__name__.lower(),
            required=validation_rules.get("required", False),
            sensitive=is_sensitive,
            description=validation_rules.get("description"),
            validation_rules=validation_rules,
            effect_type=config_service.get_effect_type(config_key),
        )

        return {"status": "success", "data": config_item}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取配置详情失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取配置详情失败: {str(e)}")


@router.put(
    "/config/{config_key:path}", summary="更新配置项", description="更新单个配置项的值"
)
async def update_config(
    config_key: str,
    request_data: ConfigUpdateRequest,
    request: Request,
    current_user: User = Depends(RequireSystemConfig),
):
    """更新配置项"""
    try:
        # 验证配置值
        validation_result = config_validator.validate_config_value(
            config_key, request_data.value
        )
        if not validation_result.valid:
            raise HTTPException(status_code=400, detail=validation_result.message)

        # 加载当前配置
        config_data = await config_service.load_config()
        old_value = config_service.get_config_value(config_data, config_key)

        # 创建备份（如果是重要配置）
        if config_service.is_restart_required(config_key):
            await config_service.create_backup(
                current_user.id, f"更新配置前自动备份 - {config_key}"
            )

        # 更新配置
        config_data = config_service.set_config_value(
            config_data, config_key, request_data.value
        )

        # 保存配置
        success = await config_service.save_config(config_data)

        if success:
            # 记录变更日志
            await config_service.log_config_change(
                user_id=current_user.id,
                action="update",
                config_key=config_key,
                old_value=old_value,
                new_value=request_data.value,
                comment=request_data.comment,
                ip_address=get_client_ip(request),
                user_agent=get_user_agent(request),
            )

            # 检查是否需要重启
            if config_service.is_restart_required(config_key):
                config_service._restart_required_keys.add(config_key)

            logger.info(f"配置更新成功: {config_key} = {request_data.value}")

            return {
                "status": "success",
                "message": "配置更新成功",
                "data": {
                    "key": config_key,
                    "old_value": config_service.mask_sensitive_value(
                        config_key, old_value
                    ),
                    "new_value": config_service.mask_sensitive_value(
                        config_key, request_data.value
                    ),
                    "restart_required": config_service.is_restart_required(config_key),
                },
            }
        else:
            raise HTTPException(status_code=500, detail="配置保存失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新配置失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")


@router.post(
    "/config/batch-update", summary="批量更新配置", description="批量更新多个配置项"
)
async def batch_update_config(
    request_data: BatchConfigUpdateRequest,
    request: Request,
    current_user: User = Depends(RequireSystemConfig),
):
    """批量更新配置"""
    try:
        # 验证所有配置项
        validation_errors = []
        for update_item in request_data.updates:
            validation_result = config_validator.validate_config_value(
                update_item.key, update_item.value
            )
            if not validation_result.valid:
                validation_errors.append(
                    f"{update_item.key}: {validation_result.message}"
                )

        if validation_errors:
            raise HTTPException(
                status_code=400, detail=f"配置验证失败: {'; '.join(validation_errors)}"
            )

        # 加载当前配置
        config_data = await config_service.load_config()

        # 创建备份
        await config_service.create_backup(
            current_user.id,
            f"批量更新前自动备份 - {request_data.comment or '批量更新'}",
        )

        # 批量更新
        updated_configs = []
        restart_required = False

        for update_item in request_data.updates:
            old_value = config_service.get_config_value(config_data, update_item.key)
            config_data = config_service.set_config_value(
                config_data, update_item.key, update_item.value
            )

            # 记录变更
            updated_configs.append(
                {
                    "key": update_item.key,
                    "old_value": config_service.mask_sensitive_value(
                        update_item.key, old_value
                    ),
                    "new_value": config_service.mask_sensitive_value(
                        update_item.key, update_item.value
                    ),
                }
            )

            # 检查是否需要重启
            if config_service.is_restart_required(update_item.key):
                restart_required = True
                config_service._restart_required_keys.add(update_item.key)

            # 记录变更日志
            await config_service.log_config_change(
                user_id=current_user.id,
                action="batch_update",
                config_key=update_item.key,
                old_value=old_value,
                new_value=update_item.value,
                comment=update_item.comment or request_data.comment,
                ip_address=get_client_ip(request),
                user_agent=get_user_agent(request),
            )

        # 保存配置
        success = await config_service.save_config(config_data)

        if success:
            logger.info(f"批量配置更新成功: {len(updated_configs)} 项")

            return {
                "status": "success",
                "message": f"批量更新成功，共更新 {len(updated_configs)} 项配置",
                "data": {
                    "updated_configs": updated_configs,
                    "restart_required": restart_required,
                },
            }
        else:
            raise HTTPException(status_code=500, detail="配置保存失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量更新配置失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"批量更新配置失败: {str(e)}")


@router.post(
    "/config/validate",
    response_model=ValidationResult,
    summary="验证配置值",
    description="验证配置项值的有效性",
)
async def validate_config(
    request_data: ConfigValidationRequest,
    current_user: User = Depends(RequireSystemConfig),
):
    """验证配置值"""
    try:
        validation_result = config_validator.validate_config_value(
            request_data.key, request_data.value
        )
        return validation_result

    except Exception as e:
        logger.error(f"配置验证失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"配置验证失败: {str(e)}")


@router.post("/config/backup", summary="创建配置备份", description="创建当前配置的备份")
async def create_config_backup(
    request_data: ConfigBackupRequest, current_user: User = Depends(RequireSystemConfig)
):
    """创建配置备份"""
    try:
        backup = await config_service.create_backup(
            current_user.id, request_data.comment
        )

        logger.info(f"配置备份创建成功: ID={backup.id}")

        return {
            "status": "success",
            "message": "配置备份创建成功",
            "data": {
                "backup_id": backup.id,
                "comment": backup.comment,
                "created_at": backup.created_at,
            },
        }

    except Exception as e:
        logger.error(f"创建配置备份失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"创建配置备份失败: {str(e)}")


@router.get(
    "/config/backups",
    response_model=ConfigBackupListResponse,
    summary="获取配置备份列表",
    description="获取配置备份列表，支持分页",
)
async def get_config_backups(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    current_user: User = Depends(RequireSystemConfig),
):
    """获取配置备份列表"""
    try:
        # 计算偏移量
        offset = (page - 1) * size

        # 查询备份列表
        backups_query = (
            ConfigBackup.all().prefetch_related("created_by").order_by("-created_at")
        )
        total = await backups_query.count()
        backups = await backups_query.offset(offset).limit(size)

        # 构建响应数据
        backup_list = []
        for backup in backups:
            backup_info = ConfigBackupInfo(
                id=backup.id,
                comment=backup.comment,
                file_hash=backup.file_hash,
                created_by_username=backup.created_by.username,
                created_at=backup.created_at,
            )
            backup_list.append(backup_info)

        return ConfigBackupListResponse(
            backups=backup_list,
            total=total,
            page=page,
            size=size,
            pages=(total + size - 1) // size,
        )

    except Exception as e:
        logger.error(f"获取配置备份列表失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取配置备份列表失败: {str(e)}")


@router.post(
    "/config/restore/{backup_id}", summary="恢复配置备份", description="从备份恢复配置"
)
async def restore_config_backup(
    backup_id: int, request: Request, current_user: User = Depends(RequireSystemConfig)
):
    """恢复配置备份"""
    try:
        success = await config_service.restore_backup(backup_id, current_user.id)

        if success:
            # 记录操作日志
            await config_service.log_config_change(
                user_id=current_user.id,
                action="restore_backup",
                comment=f"恢复配置备份 #{backup_id}",
                ip_address=get_client_ip(request),
                user_agent=get_user_agent(request),
            )

            # 标记需要重启
            config_service._restart_required_keys.add("system.restore")

            logger.info(f"配置恢复成功: 备份#{backup_id}")

            return {
                "status": "success",
                "message": "配置恢复成功",
                "data": {"backup_id": backup_id, "restart_required": True},
            }
        else:
            raise HTTPException(status_code=500, detail="配置恢复失败")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"恢复配置备份失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"恢复配置备份失败: {str(e)}")


@router.post("/config/reset", summary="重置配置", description="重置指定配置项到默认值")
async def reset_config_to_default(
    request_data: ConfigResetRequest,
    request: Request,
    current_user: User = Depends(RequireSystemConfig),
):
    """重置配置到默认值"""
    try:
        # 这里需要定义默认配置值，暂时返回错误
        # 在实际实现中，应该有一个默认配置文件或配置字典
        raise HTTPException(
            status_code=501, detail="配置重置功能暂未实现，请手动修改配置文件"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重置配置失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"重置配置失败: {str(e)}")


@router.get(
    "/config/history",
    response_model=ConfigHistoryResponse,
    summary="获取配置变更历史",
    description="获取配置变更历史记录",
)
async def get_config_history(
    config_key: Optional[str] = Query(None, description="配置键筛选"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页大小"),
    current_user: User = Depends(RequireSystemConfig),
):
    """获取配置变更历史"""
    try:
        # 构建查询条件
        query = ConfigChangeLog.all().prefetch_related("user").order_by("-created_at")

        if config_key:
            query = query.filter(config_key=config_key)
        if start_date:
            query = query.filter(created_at__gte=start_date)
        if end_date:
            query = query.filter(created_at__lte=end_date)

        # 分页查询
        total = await query.count()
        offset = (page - 1) * size
        logs = await query.offset(offset).limit(size)

        # 构建响应数据
        log_list = []
        for log in logs:
            log_info = ConfigChangeLogInfo(
                id=log.id,
                username=log.user.username,
                action=log.action,
                config_key=log.config_key,
                old_value=log.old_value,
                new_value=log.new_value,
                comment=log.comment,
                created_at=log.created_at,
            )
            log_list.append(log_info)

        return ConfigHistoryResponse(
            logs=log_list,
            total=total,
            page=page,
            size=size,
            pages=(total + size - 1) // size,
        )

    except Exception as e:
        logger.error(f"获取配置变更历史失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取配置变更历史失败: {str(e)}")


@router.get(
    "/config/diff/{history_id}",
    summary="获取配置变更差异",
    description="获取指定变更记录的差异对比",
)
async def get_config_diff(
    history_id: int, current_user: User = Depends(RequireSystemConfig)
):
    """获取配置变更差异"""
    try:
        log = await ConfigChangeLog.get_or_none(id=history_id)
        if not log:
            raise HTTPException(status_code=404, detail="变更记录不存在")

        diff_response = ConfigDiffResponse(
            config_key=log.config_key or "unknown",
            old_value=log.old_value,
            new_value=log.new_value,
            change_type="modified",  # 简化处理，实际可以根据old_value和new_value判断
            timestamp=log.created_at,
        )

        return {"status": "success", "data": diff_response}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取配置差异失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取配置差异失败: {str(e)}")


@router.get(
    "/config/categories", summary="获取配置分类", description="获取所有配置分类列表"
)
async def get_config_categories(current_user: User = Depends(RequireSystemConfig)):
    """获取配置分类"""
    try:
        categories = []
        for cat_name, cat_info in config_service.CONFIG_CATEGORIES.items():
            category = {
                "name": cat_name,
                "display_name": cat_info["display_name"],
                "description": cat_info["description"],
                "icon": cat_info.get("icon"),
            }
            categories.append(category)

        return {
            "status": "success",
            "data": {"categories": categories, "total": len(categories)},
        }

    except Exception as e:
        logger.error(f"获取配置分类失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取配置分类失败: {str(e)}")


@router.get(
    "/config/restart-status",
    response_model=RestartStatusResponse,
    summary="获取重启状态",
    description="获取系统重启状态和待生效的配置项",
)
async def get_restart_status(current_user: User = Depends(RequireSystemConfig)):
    """获取重启状态"""
    try:
        restart_required = bool(config_service._restart_required_keys)
        pending_changes = list(config_service._restart_required_keys)

        # 简化处理，实际可以根据配置项判断受影响的服务
        affected_services = []
        if restart_required:
            affected_services = ["api", "celery", "database"]

        return RestartStatusResponse(
            restart_required=restart_required,
            pending_changes=pending_changes,
            affected_services=affected_services,
            last_restart_time=None,  # 实际实现中可以记录上次重启时间
        )

    except Exception as e:
        logger.error(f"获取重启状态失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取重启状态失败: {str(e)}")


@router.post(
    "/config/clear-restart-flag",
    summary="清除重启标志",
    description="清除重启标志，表示已经重启完成",
)
async def clear_restart_flag(
    request: Request, current_user: User = Depends(RequireSystemConfig)
):
    """清除重启标志"""
    try:
        config_service._restart_required_keys.clear()

        # 记录操作日志
        await config_service.log_config_change(
            user_id=current_user.id,
            action="clear_restart_flag",
            comment="清除重启标志",
            ip_address=get_client_ip(request),
            user_agent=get_user_agent(request),
        )

        logger.info("重启标志已清除")

        return {"status": "success", "message": "重启标志已清除"}

    except Exception as e:
        logger.error(f"清除重启标志失败: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"清除重启标志失败: {str(e)}")
