"""
SEO相关工具函数，用于为资源页面生成元数据。
"""

from typing import Dict
from app.models.resource import PanResource
from app.models.enums import PanType


def _get_pan_type_name(pan_type: int) -> str:
    """
    内部辅助函数，将网盘类型的枚举值转换为用户可读的字符串名称。

    Args:
        pan_type: 网盘类型的整数值。

    Returns:
        对应的网盘名称字符串，如"百度网盘"。
    """
    # 使用 .get() 方法避免因未定义类型而引发KeyError
    pan_map = {
        PanType.BAIDU.value: "百度网盘",
        PanType.QUARK.value: "夸克网盘",
        PanType.ALIYUN.value: "阿里云盘",
        PanType.XUNLEI.value: "迅雷网盘",
    }
    return pan_map.get(pan_type, "")


def generate_seo_metadata(
    resource: PanResource, site_name: str = "97盘搜"
) -> Dict[str, str]:
    """
    为给定的资源对象生成SEO优化的标题(title)和描述(description)。
    优化点：
    1. title 更丰富，包含类型、作者、年份等关键信息。
    2. description 更长且唯一，优先用 text_content、标签、作者、上传时间、访问量等。
    3. 保证 description 不重复，且长度在 120-160 字符。
    """
    title = resource.title or "未知资源"
    pan_type_name = _get_pan_type_name(resource.pan_type)
    intent_word = "下载"  # 可根据 file_type 设定不同意图词

    # 1. 构建SEO标题 (格式: [资源标题] [类型] [作者] [年份] [网盘][意图] - [网站名])
    year = resource.created_at.strftime("%Y") if resource.created_at else ""
    author = resource.author or ""
    file_type = resource.file_type or ""
    seo_title_parts = [title]
    if file_type:
        seo_title_parts.append(file_type)
    if author:
        seo_title_parts.append(author)
    if year:
        seo_title_parts.append(year)
    seo_title_parts.append(f"{pan_type_name}{intent_word}")
    seo_title = " ".join([p for p in seo_title_parts if p]) + f" - {site_name}"

    # 2. 构建Meta描述 (更丰富，唯一性强)
    desc_parts = [f"《{title}》"]
    # 优先用 text_content 作为简介
    if resource.text_content:
        desc_parts.append(resource.text_content[:40])  # 截取前40字
    # 文件类型映射
    file_type_map = {
        "video": "视频",
        "audio": "音频",
        "image": "图片",
        "document": "文档",
        "archive": "压缩包",
        "application": "应用",
    }
    human_readable_type = (
        file_type_map.get(resource.file_type, resource.file_type)
        if resource.file_type
        else ""
    )
    if human_readable_type == "视频":
        desc_parts.append("电影,电视剧,短剧")
    elif human_readable_type == "音频":
        desc_parts.append("音乐,有声书,广播剧")
    elif human_readable_type == "图片":
        desc_parts.append("壁纸,头像,表情包")
    elif human_readable_type == "文档":
        desc_parts.append("小说,文档")
    elif human_readable_type == "压缩包":
        desc_parts.append("软件,游戏,资源")
    elif human_readable_type == "应用":
        desc_parts.append("软件,游戏,apk")
    elif human_readable_type:
        desc_parts.append(human_readable_type)
    # 文件大小
    if resource.file_size:
        desc_parts.append(f"文件大小为{resource.file_size}。")
    # 作者
    if author:
        desc_parts.append(f"由{author}分享。")
    # 上传时间
    if resource.created_at:
        desc_parts.append(f"上传于{resource.created_at.strftime('%Y-%m-%d')}。")
    # 访问量
    if resource.access_count:
        desc_parts.append(f"累计访问{resource.access_count}次。")
    # 网盘类型
    if pan_type_name:
        desc_parts.append(f"{pan_type_name}")
    # 资源高速下载
    desc_parts.append("资源高速下载,网盘资源下载.")

    # 合成描述
    seo_description = "".join(desc_parts)

    # 裁剪描述长度以符合SEO最佳实践 (通常在160字符以内)
    if len(seo_description) > 160:
        seo_description = seo_description[:157] + "..."
    # 保证描述唯一性（拼接资源ID）
    seo_description += f"(ID:{resource.id})"

    return {"seo_title": seo_title, "seo_description": seo_description}
