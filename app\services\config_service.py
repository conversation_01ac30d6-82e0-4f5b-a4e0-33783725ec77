"""
配置管理服务
"""

import yaml
import hashlib
import re
import os
from typing import Any, Dict
from datetime import datetime
from pathlib import Path
import logging
from fastapi import HTTPException

from app.models.config_models import ConfigBackup, ConfigChangeLog, ValidationResult
from app.utils.config import settings

logger = logging.getLogger("config-service")


class ConfigService:
    """配置管理服务"""

    CONFIG_FILE_PATH = "app/config.yaml"
    BACKUP_DIR = "backups/config"

    # 配置分类定义
    CONFIG_CATEGORIES = {
        "app": {
            "display_name": "应用基础配置",
            "description": "应用程序基本信息配置",
            "icon": "📱",
        },
        "database": {
            "display_name": "数据库配置",
            "description": "数据库连接和相关配置",
            "icon": "🗄️",
        },
        "auth": {
            "display_name": "认证配置",
            "description": "JWT认证和安全相关配置",
            "icon": "🔐",
        },
        "email": {
            "display_name": "邮箱服务配置",
            "description": "SMTP邮箱服务配置",
            "icon": "📧",
        },
        "security": {
            "display_name": "安全配置",
            "description": "密码策略和安全设置",
            "icon": "🛡️",
        },
        "registration": {
            "display_name": "用户注册配置",
            "description": "用户注册相关设置",
            "icon": "👤",
        },
        "logging": {
            "display_name": "日志配置",
            "description": "日志级别和格式配置",
            "icon": "📝",
        },
        "wx_push": {
            "display_name": "企业微信推送配置",
            "description": "企业微信消息推送配置",
            "icon": "💬",
        },
        "api": {
            "display_name": "API服务配置",
            "description": "API服务监听和CORS配置",
            "icon": "🌐",
        },
        "pan_service": {
            "display_name": "网盘服务配置",
            "description": "网盘服务超时和重试配置",
            "icon": "☁️",
        },
        "cache": {
            "display_name": "缓存配置",
            "description": "缓存大小和过期时间配置",
            "icon": "⚡",
        },
        "redis": {
            "display_name": "Redis配置",
            "description": "Redis连接配置",
            "icon": "🔴",
        },
        "celery": {
            "display_name": "Celery配置",
            "description": "Celery任务队列配置",
            "icon": "🔄",
        },
        "meilisearch": {
            "display_name": "搜索配置",
            "description": "Meilisearch搜索引擎配置",
            "icon": "🔍",
        },
        "seo": {
            "display_name": "SEO配置",
            "description": "搜索引擎优化配置",
            "icon": "📊",
        },
    }

    # 敏感配置项
    SENSITIVE_KEYS = {
        "database.url",
        "database.credentials.password",
        "auth.jwt_secret_key",
        "email.password",
        "wx_push.corp_secret",
        "redis.password",
    }

    # 需要重启的配置项
    RESTART_REQUIRED_KEYS = {
        "app.debug",
        "api.host",
        "api.port",
        "database.url",
        "redis.url",
        "celery.broker_url",
        "celery.result_backend",
    }

    def __init__(self):
        """初始化配置服务"""
        self._ensure_backup_dir()
        self._restart_required_keys = set()

    def _ensure_backup_dir(self):
        """确保备份目录存在"""
        Path(self.BACKUP_DIR).mkdir(parents=True, exist_ok=True)

    async def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.CONFIG_FILE_PATH, "r", encoding="utf-8") as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"配置文件不存在: {self.CONFIG_FILE_PATH}")
            raise HTTPException(status_code=500, detail="配置文件不存在")
        except yaml.YAMLError as e:
            logger.error(f"配置文件格式错误: {e}")
            raise HTTPException(status_code=500, detail="配置文件格式错误")

    async def save_config(self, config: Dict[str, Any]) -> bool:
        """保存配置文件"""
        try:
            # 创建临时文件
            temp_file = f"{self.CONFIG_FILE_PATH}.tmp"

            with open(temp_file, "w", encoding="utf-8") as f:
                yaml.dump(
                    config, f, default_flow_style=False, allow_unicode=True, indent=2
                )

            # 原子性替换
            os.replace(temp_file, self.CONFIG_FILE_PATH)

            # 重新加载settings
            settings.load_config(self.CONFIG_FILE_PATH)

            return True
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            # 清理临时文件
            if os.path.exists(temp_file):
                os.remove(temp_file)
            raise HTTPException(status_code=500, detail=f"保存配置文件失败: {str(e)}")

    def calculate_file_hash(self, config: Dict[str, Any]) -> str:
        """计算配置文件哈希值"""
        config_str = yaml.dump(config, default_flow_style=False, allow_unicode=True)
        return hashlib.sha256(config_str.encode("utf-8")).hexdigest()

    def get_config_value(self, config: Dict[str, Any], key: str) -> Any:
        """获取配置值"""
        try:
            keys = key.split(".")
            value = config
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return None

    def set_config_value(
        self, config: Dict[str, Any], key: str, value: Any
    ) -> Dict[str, Any]:
        """设置配置值"""
        keys = key.split(".")
        current = config

        # 导航到父级
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]

        # 设置值
        current[keys[-1]] = value
        return config

    def is_sensitive_config(self, key: str) -> bool:
        """检查是否为敏感配置"""
        return key in self.SENSITIVE_KEYS or any(
            sensitive_key in key
            for sensitive_key in ["password", "secret", "token", "key", "cookie"]
        )

    def mask_sensitive_value(self, key: str, value: Any) -> Any:
        """遮蔽敏感配置值"""
        if not self.is_sensitive_config(key):
            return value

        if isinstance(value, str) and len(value) > 8:
            return value[:4] + "*" * (len(value) - 8) + value[-4:]
        elif isinstance(value, str):
            return "***"
        else:
            return "***"

    def is_restart_required(self, key: str) -> bool:
        """检查配置项是否需要重启"""
        return key in self.RESTART_REQUIRED_KEYS

    def get_effect_type(self, key: str) -> str:
        """获取配置项生效方式"""
        if self.is_restart_required(key):
            return "restart_required"
        elif key.startswith("logging."):
            return "reload_required"
        else:
            return "immediate"

    async def apply_config_change(self, config_key: str, new_value: Any) -> bool:
        """应用配置变更，实现热更新"""
        effect_type = self.get_effect_type(config_key)

        try:
            if effect_type == "immediate":
                # 立即生效的配置，尝试热更新
                return await self._apply_immediate_config(config_key, new_value)
            elif effect_type == "reload_required":
                # 需要重新加载的配置
                return await self._apply_reload_config(config_key, new_value)
            elif effect_type == "restart_required":
                # 需要重启的配置，只标记状态
                self._restart_required_keys.add(config_key)
                return True

            return True
        except Exception as e:
            logger.error(f"应用配置变更失败 {config_key}: {e}")
            return False

    async def _apply_immediate_config(self, config_key: str, new_value: Any) -> bool:
        """应用立即生效的配置"""
        try:
            # 缓存配置热更新
            if config_key.startswith("cache."):
                await self._update_cache_config(config_key, new_value)

            # 并发限制配置热更新
            elif config_key.startswith("concurrency_limiter."):
                await self._update_concurrency_config(config_key, new_value)

            # 安全配置热更新
            elif config_key == "security.blocked_keywords":
                await self._update_security_config(config_key, new_value)

            # 爬虫配置热更新
            elif "_crawler." in config_key:
                await self._update_crawler_config(config_key, new_value)

            logger.info(f"立即生效配置已更新: {config_key}")
            return True

        except Exception as e:
            logger.error(f"应用立即生效配置失败 {config_key}: {e}")
            return False

    async def _apply_reload_config(self, config_key: str, new_value: Any) -> bool:
        """应用需要重新加载的配置"""
        try:
            # 日志配置热更新
            if config_key.startswith("logging."):
                await self._update_logging_config(config_key, new_value)

            logger.info(f"重新加载配置已更新: {config_key}")
            return True

        except Exception as e:
            logger.error(f"应用重新加载配置失败 {config_key}: {e}")
            return False

    async def _update_cache_config(self, config_key: str, new_value: Any):
        """更新缓存配置"""
        # 这里可以实现缓存配置的热更新
        # 例如：更新缓存大小、TTL等
        logger.info(f"缓存配置热更新: {config_key} = {new_value}")

    async def _update_concurrency_config(self, config_key: str, new_value: Any):
        """更新并发限制配置"""
        try:
            # 动态更新并发限制
            if config_key == "concurrency_limiter.times":
                # 这里需要更新中间件中的并发限制
                from app.middleware.concurrency import update_max_concurrent_requests

                update_max_concurrent_requests(new_value)
                logger.info(f"并发限制已更新: {new_value}")
        except ImportError:
            logger.warning("并发限制中间件不支持热更新")

    async def _update_security_config(self, config_key: str, new_value: Any):
        """更新安全配置"""
        if config_key == "security.blocked_keywords":
            # 这里可以通知搜索模块更新屏蔽词列表
            logger.info(f"安全配置已更新: {config_key}")

    async def _update_crawler_config(self, config_key: str, new_value: Any):
        """更新爬虫配置"""
        # 爬虫配置的热更新
        logger.info(f"爬虫配置已更新: {config_key} = {new_value}")

    async def _update_logging_config(self, config_key: str, new_value: Any):
        """更新日志配置"""
        try:
            import logging

            if config_key == "logging.level":
                # 动态更新日志级别
                log_level = getattr(logging, new_value.upper(), logging.INFO)
                root_logger = logging.getLogger()
                root_logger.setLevel(log_level)

                # 更新所有处理器的级别
                for handler in root_logger.handlers:
                    handler.setLevel(log_level)

                logger.info(f"日志级别已更新为: {new_value}")

            elif config_key == "logging.format":
                # 动态更新日志格式
                root_logger = logging.getLogger()
                formatter = logging.Formatter(new_value)

                for handler in root_logger.handlers:
                    handler.setFormatter(formatter)

                logger.info(f"日志格式已更新")

        except Exception as e:
            logger.error(f"更新日志配置失败: {e}")
            raise

    async def create_backup(self, user_id: int, comment: str = None) -> ConfigBackup:
        """创建配置备份"""
        config = await self.load_config()
        file_hash = self.calculate_file_hash(config)

        backup = await ConfigBackup.create(
            config_content=config,
            file_hash=file_hash,
            comment=comment,
            created_by_id=user_id,
        )

        # 保存备份文件
        backup_filename = (
            f"config_backup_{backup.id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"
        )
        backup_path = os.path.join(self.BACKUP_DIR, backup_filename)

        with open(backup_path, "w", encoding="utf-8") as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)

        logger.info(f"配置备份创建成功: {backup_path}")
        return backup

    async def restore_backup(self, backup_id: int, user_id: int) -> bool:
        """恢复配置备份"""
        backup = await ConfigBackup.get_or_none(id=backup_id)
        if not backup:
            raise HTTPException(status_code=404, detail="备份不存在")

        # 创建当前配置的备份
        await self.create_backup(user_id, f"恢复前自动备份 - 恢复到备份#{backup_id}")

        # 恢复配置
        success = await self.save_config(backup.config_content)

        if success:
            # 记录操作日志
            await self.log_config_change(
                user_id=user_id,
                action="restore_backup",
                comment=f"恢复到备份#{backup_id}",
            )
            logger.info(f"配置恢复成功: 备份#{backup_id}")

        return success

    async def log_config_change(
        self,
        user_id: int,
        action: str,
        config_key: str = None,
        old_value: Any = None,
        new_value: Any = None,
        comment: str = None,
        ip_address: str = None,
        user_agent: str = None,
    ):
        """记录配置变更日志"""
        await ConfigChangeLog.create(
            user_id=user_id,
            action=action,
            config_key=config_key,
            old_value=(
                self.mask_sensitive_value(config_key, old_value)
                if config_key
                else old_value
            ),
            new_value=(
                self.mask_sensitive_value(config_key, new_value)
                if config_key
                else new_value
            ),
            comment=comment,
            ip_address=ip_address,
            user_agent=user_agent,
        )


class ConfigValidator:
    """配置验证器"""

    # 验证规则定义
    VALIDATION_RULES = {
        "app.name": {
            "type": "string",
            "required": True,
            "min_length": 1,
            "max_length": 50,
            "pattern": r"^[a-zA-Z0-9-_]+$",
            "description": "应用名称，只能包含字母、数字、连字符和下划线",
        },
        "app.version": {
            "type": "string",
            "required": True,
            "pattern": r"^\d+\.\d+\.\d+$",
            "description": "版本号，格式为 x.y.z",
        },
        "app.debug": {
            "type": "boolean",
            "required": True,
            "description": "调试模式开关",
        },
        "api.port": {
            "type": "integer",
            "required": True,
            "min_value": 1024,
            "max_value": 65535,
            "description": "API服务端口，范围 1024-65535",
        },
        "api.host": {
            "type": "string",
            "required": True,
            "pattern": r"^(\d{1,3}\.){3}\d{1,3}$|^localhost$|^0\.0\.0\.0$",
            "description": "API服务监听地址",
        },
        "database.url": {
            "type": "string",
            "required": True,
            "pattern": r"^postgresql://.*",
            "description": "PostgreSQL数据库连接URL",
        },
        "auth.jwt_secret_key": {
            "type": "string",
            "required": True,
            "min_length": 32,
            "sensitive": True,
            "description": "JWT密钥，长度至少32个字符",
        },
        "auth.access_token_expire_minutes": {
            "type": "integer",
            "required": True,
            "min_value": 1,
            "max_value": 1440,
            "description": "访问令牌过期时间（分钟），范围 1-1440",
        },
        "email.smtp_port": {
            "type": "integer",
            "required": True,
            "min_value": 1,
            "max_value": 65535,
            "description": "SMTP端口号",
        },
        "redis.port": {
            "type": "integer",
            "required": True,
            "min_value": 1,
            "max_value": 65535,
            "description": "Redis端口号",
        },
    }

    def validate_type(self, value: Any, expected_type: str) -> bool:
        """验证数据类型"""
        type_mapping = {
            "string": str,
            "integer": int,
            "float": float,
            "boolean": bool,
            "list": list,
            "dict": dict,
        }

        expected_python_type = type_mapping.get(expected_type)
        if not expected_python_type:
            return True  # 未知类型，跳过验证

        return isinstance(value, expected_python_type)

    def validate_config_value(self, key: str, value: Any) -> ValidationResult:
        """验证配置项值"""
        rules = self.VALIDATION_RULES.get(key, {})

        # 类型验证
        expected_type = rules.get("type")
        if expected_type and not self.validate_type(value, expected_type):
            return ValidationResult(
                valid=False,
                message=f"类型错误: 期望 {expected_type}，实际 {type(value).__name__}",
            )

        # 必填验证
        if rules.get("required") and (value is None or value == ""):
            return ValidationResult(valid=False, message="此配置项为必填项")

        # 字符串长度验证
        if isinstance(value, str):
            min_length = rules.get("min_length")
            max_length = rules.get("max_length")

            if min_length and len(value) < min_length:
                return ValidationResult(
                    valid=False, message=f"长度不能少于 {min_length} 个字符"
                )

            if max_length and len(value) > max_length:
                return ValidationResult(
                    valid=False, message=f"长度不能超过 {max_length} 个字符"
                )

        # 数值范围验证
        if isinstance(value, (int, float)):
            min_value = rules.get("min_value")
            max_value = rules.get("max_value")

            if min_value is not None and value < min_value:
                return ValidationResult(valid=False, message=f"值不能小于 {min_value}")

            if max_value is not None and value > max_value:
                return ValidationResult(valid=False, message=f"值不能大于 {max_value}")

        # 正则表达式验证
        pattern = rules.get("pattern")
        if pattern and isinstance(value, str):
            if not re.match(pattern, value):
                return ValidationResult(
                    valid=False,
                    message=f"格式不正确: {rules.get('description', '请检查格式')}",
                )

        return ValidationResult(valid=True, message="验证通过")


# 创建全局实例
config_service = ConfigService()
config_validator = ConfigValidator()
