#!/bin/bash

# --- Start of Configuration ---

# 数据库连接参数
DB_USER="pan_so_user"
DB_NAME="pan_so_db"
DB_HOST="localhost" # 或者您的数据库服务器IP

# 本地备份目录 (请确保该目录存在且有写入权限)
LOCAL_BACKUP_DIR="/root/pan-so-database-back/postgres"

# 备份文件保留天数 (本地)
RETENTION_DAYS=7

# 火山引擎 TOS 配置 (用于异地备份)
# 请先安装并配置好 tosutil: https://www.volcengine.com/docs/6349/74854
# Bucket 在 cn-shanghai
TOS_BUCKET_URL="tos://pan-so-db-back/db-backups"

# !!!重要!!! 请将这里替换为 tosutil 的绝对路径
TOSUTIL_CMD="/root/tosutil" 

# --- End of Configuration ---


# --- Script Logic ---

# 设置退出机制：
#   set -e: 任何命令失败则立即退出
#   set -o pipefail: 管道中任何一个命令失败，整个管道都失败
set -e
set -o pipefail

# 检查本地备份目录是否存在
mkdir -p "$LOCAL_BACKUP_DIR"

# 生成带时间戳的文件名
DATE_TAG=$(date +"%Y-%m-%d_%H-%M-%S")
BACKUP_FILENAME="db_${DB_NAME}_${DATE_TAG}.sql.gz"
LOCAL_BACKUP_FILEPATH="${LOCAL_BACKUP_DIR}/${BACKUP_FILENAME}"

echo "开始备份数据库: ${DB_NAME} ..."

# !!!重要!!! 请将 'your_db_password' 替换为真实的数据库密码
# PGPASSWORD 环境变量用于临时传递密码，更推荐使用 .pgpass 文件
# 如果您配置了 .pgpass，可以移除 PGPASSWORD="your_db_password" 部分
export PGPASSWORD="Wsk1998107."
pg_dump -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" --no-password | gzip > "$LOCAL_BACKUP_FILEPATH"
unset PGPASSWORD

echo "数据库备份成功，文件保存在: ${LOCAL_BACKUP_FILEPATH}"

# 上传到火山引擎 TOS
if command -v "$TOSUTIL_CMD" &> /dev/null && [ -n "$TOS_BUCKET_URL" ]; then
    echo "正在上传备份文件到火山引擎 TOS ..."
    "$TOSUTIL_CMD" cp "$LOCAL_BACKUP_FILEPATH" "${TOS_BUCKET_URL}/${BACKUP_FILENAME}"
    echo "上传成功."
else
    echo "警告: 未找到 tosutil 命令 (路径: ${TOSUTIL_CMD}) 或未配置 TOS_BUCKET_URL，跳过上传步骤。"
fi

# 清理本地旧的备份文件
echo "清理 ${RETENTION_DAYS} 天前的本地备份文件..."
find "$LOCAL_BACKUP_DIR" -type f -name "*.sql.gz" -mtime +${RETENTION_DAYS} -delete
echo "清理完成。"

echo "所有备份任务执行完毕！"