from pydantic import BaseModel, Field, constr
from typing import List, Optional, Any, Dict
from datetime import datetime
from uuid import UUID
from app.models.enums import (
    BatchStatus,
    TaskStatus,
)  # Assuming enums are in this path


class SearchResult(BaseModel):
    resource_id: str
    file_name: str
    file_size: str
    file_type: str
    pan_type: int
    thumbnail: Optional[str] = None
    created_at: Optional[Any] = None  # 支持datetime或字符串
    updated_at: Optional[Any] = None  # 支持datetime或字符串
    update_time: Optional[Any] = None  # 统一的更新时间字段，用于前端显示
    text_content: Optional[str] = None  # 资源预览内容
    relevance_score: Optional[float] = None  # 相关性得分


class SearchResponse(BaseModel):
    status: str
    message: str
    total: int
    results: List[SearchResult]


class ShareLinkRequest(BaseModel):
    detail_url: str
    platform: str = "auto"


class ShareLinkResponse(BaseModel):
    status: str
    message: str
    share_url: Optional[str] = None
    share_pwd: Optional[str] = None
    title: Optional[str] = None
    file_type: Optional[str] = None
    expiry_days: Optional[int] = None
    is_deleted: bool = False


class ResourceInvalidFeedbackRequest(BaseModel):
    resource_id: str
    pan_type: int
    invalid_type: int
    description: Optional[str] = None
    contact_info: Optional[str] = None


class ResourceInvalidFeedbackResponse(BaseModel):
    status: str
    message: str
    is_deleted: bool = False


class ResourceUrlItem(BaseModel):
    """单个提交的URL"""

    url: constr(min_length=10, max_length=2048)  # type: ignore


class ResourceSubmissionRequest(BaseModel):
    """资源批量提交请求模型"""

    urls: List[ResourceUrlItem] = Field(..., max_items=500, min_items=1)
    is_mine: Optional[bool] = Field(False, description="是否为管理员提交 (True表示是)")

    # 新增字段（可选，保持向后兼容）
    is_parsed: Optional[bool] = Field(
        True, description="是否标记为已解析（控制返回原始链接还是转存链接）"
    )
    admin_submit: Optional[bool] = Field(False, description="管理员提交标识")


class SubmittedResourceInfo(BaseModel):
    """单个URL提交后的初步处理信息"""

    url: str
    status: str  # 修改为 str 以支持自定义状态
    resource_id: Optional[str] = None  # PanResource.resource_key if applicable
    task_id: Optional[UUID] = None  # PanResource.resource_key if applicable
    message: Optional[str] = None
    notes: Optional[str] = None  # 用于提供额外信息，例如"任务已存在"


class BatchSubmissionResponse(BaseModel):
    """资源批量提交后的响应模型"""

    batch_id: UUID
    message: str
    total_submitted: int
    accepted_for_processing: int  # Number of URLs for which tasks were created
    initial_results: List[SubmittedResourceInfo]


class IndividualTaskStatus(BaseModel):
    """单个任务的详细状态"""

    task_id: UUID
    original_url: str
    status: TaskStatus
    resource_title: Optional[str] = None  # Populated if PanResource is processed
    pan_resource_id: Optional[int] = None  # PanResource.id if linked
    error_message: Optional[str] = None
    updated_at: datetime
    batch_id: Optional[UUID] = None  # 关联的批次ID，方便溯源


class BatchStatusResponse(BaseModel):
    """批处理任务状态查询响应模型"""

    batch_id: UUID
    overall_status: BatchStatus
    created_at: datetime
    updated_at: datetime
    total_urls_submitted: int
    tasks_created: int
    successful_tasks: int
    failed_tasks: int
    tasks_details: List[IndividualTaskStatus]


# --- 新增的请求模型 ---
class QuerySubmissionStatusRequest(BaseModel):
    """按URL查询提交状态的请求模型"""

    urls: List[constr(min_length=10, max_length=2048)] = Field(  # type: ignore
        ..., max_items=100, min_items=1, description="要查询状态的原始URL列表"
    )


# ==================================================
# ===         资源详情页API相关模型            ===
# ==================================================


class ResourceDetailResponse(BaseModel):
    """
    获取单个资源详情的API响应模型。
    包含了资源的所有主要字段以及为SEO生成的元数据。
    """

    id: int
    resource_key: str
    pan_type: int
    original_url: str
    title: Optional[str]
    is_parsed: bool
    author: Optional[str]
    author_avatar: Optional[str]
    is_mine: bool
    verified_status: Optional[str]
    share_url: Optional[str]
    share_pwd: Optional[str]
    created_at: Optional[datetime]
    updated_at: Optional[datetime]
    expiry_date: Optional[datetime]
    file_type: Optional[str]
    file_size: Optional[str]
    access_count: int
    text_content: Optional[str]

    # 新增的SEO字段
    seo_title: Optional[str] = Field(None, description="为SEO生成的页面标题")
    seo_description: Optional[str] = Field(None, description="为SEO生成的页面描述")

    class Config:
        # 启用 orm_mode 模式 (Pydantic V1)，允许模型直接从ORM对象创建
        from_attributes = True


class ResourceDetailErrorResponse(BaseModel):
    """
    获取资源详情失败时的错误响应模型。
    """

    status: str
    message: str


class CrawledResource(BaseModel):
    """从爬虫或监控器返回的标准化资源数据模型"""

    title: str
    original_url: str  # 原始网盘URL
    resource_key: Optional[str] = None  # 新增：用于存储从URL解析出的唯一资源键
    text_content: Optional[str] = None
    source: str  # 来源，爬虫名称
    pan_type: Optional[int] = None  # 网盘类型枚举值
    file_count: Optional[int] = None  # 文件数量
    updated_at: datetime  # 更新时间，使用 aware datetime
    file_size: Optional[str] = None  # 文件大小
    file_type: Optional[str] = None  # 文件类型
    password: Optional[str] = Field(None, description="提取码")
    description: Optional[str] = Field(None, description="资源描述")
    tags: List[str] = Field([], description="标签列表")
    post_date: Optional[datetime] = Field(None, description="原始发布时间")

    # 保留原始数据，方便调试和后续扩展
    raw_data: Dict[str, Any] = Field({}, description="爬虫抓取的原始数据结构")
