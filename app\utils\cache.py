from cachetools import TTLCache, LRUCache
from functools import wraps
import logging
import time
import hashlib
import json
from typing import Any, Callable, Dict, Optional, Tuple, List
from collections import deque

logger = logging.getLogger(__name__)

# 数据库操作队列，用于批量保存
db_queue = deque(maxlen=1000)  # 最多存储1000条记录

# 全局缓存实例
# 短期缓存 - 10分钟过期，最多存储100项
SHORT_CACHE = TTLCache(maxsize=100, ttl=600)
# 长期缓存 - 1小时过期，最多存储500项
LONG_CACHE = TTLCache(maxsize=500, ttl=3600)
# 静态缓存 - 不过期，最多存储1000项
STATIC_CACHE = LRUCache(maxsize=1000)


def cache_key(func_name: str, *args, **kwargs) -> str:
    """生成缓存键"""
    # 将位置参数和关键字参数序列化为字符串
    key_parts = [func_name]
    
    if args:
        key_parts.append(str(args))
    
    if kwargs:
        # 对关键字参数排序以确保一致性
        sorted_kwargs = sorted(kwargs.items())
        key_parts.append(str(sorted_kwargs))
    
    # 连接所有部分并创建MD5哈希作为键
    key_str = "_".join(key_parts)
    return hashlib.md5(key_str.encode()).hexdigest()


def ttl_cache(ttl: int = 3600, maxsize: int = 1000):
    """
    具有过期时间的缓存装饰器
    
    Args:
        ttl: 缓存过期时间（秒）
        maxsize: 缓存最大项数
    """
    cache_dict = TTLCache(maxsize=maxsize, ttl=ttl)
    
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            key = cache_key(func.__name__, *args, **kwargs)
            
            # 尝试从缓存获取
            if key in cache_dict:
                logger.debug(f"缓存命中: {func.__name__}")
                return cache_dict[key]
            
            # 执行原始函数
            start_time = time.time()
            result = await func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # 存储结果到缓存
            cache_dict[key] = result
            logger.debug(f"缓存存储: {func.__name__}, 执行时间: {execution_time:.4f}秒")
            
            return result
        
        # 添加清除缓存的方法
        wrapper.clear_cache = cache_dict.clear
        return wrapper
    
    return decorator


def get_cache(key: str, cache_type: str = "short") -> Tuple[bool, Any]:
    """
    从指定的缓存中获取值
    
    Args:
        key: 缓存键
        cache_type: 缓存类型 ("short", "long", "static")
    
    Returns:
        (是否命中, 缓存值)
    """
    cache = {
        "short": SHORT_CACHE,
        "long": LONG_CACHE,
        "static": STATIC_CACHE,
    }.get(cache_type, SHORT_CACHE)
    
    if key in cache:
        return True, cache[key]
    
    return False, None


def set_cache(key: str, value: Any, cache_type: str = "short") -> bool:
    """
    设置缓存值
    
    Args:
        key: 缓存键
        value: 要缓存的值
        cache_type: 缓存类型 ("short", "long", "static")
    
    Returns:
        是否成功设置
    """
    try:
        cache = {
            "short": SHORT_CACHE,
            "long": LONG_CACHE,
            "static": STATIC_CACHE,
        }.get(cache_type, SHORT_CACHE)
        
        cache[key] = value
        return True
    except Exception as e:
        logger.error(f"设置缓存失败: {str(e)}")
        return False


def clear_cache(cache_type: Optional[str] = None) -> None:
    """
    清除指定类型或所有缓存
    
    Args:
        cache_type: 要清除的缓存类型，None表示清除所有缓存
    """
    if cache_type == "short" or cache_type is None:
        SHORT_CACHE.clear()
    
    if cache_type == "long" or cache_type is None:
        LONG_CACHE.clear()
    
    if cache_type == "static" or cache_type is None:
        STATIC_CACHE.clear()
    
    logger.info(f"已清除缓存: {cache_type or '所有'}") 