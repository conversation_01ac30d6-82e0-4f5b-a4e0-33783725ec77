#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
用于PM2定时触发学霸盘爬虫任务的独立脚本。
"""
import os
import sys

# 将项目根目录添加到 Python 路径中
# 这对于在命令行或通过cron直接运行此脚本至关重要
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

from app.tasks.crawler_tasks import run_xuebapans_crawl

if __name__ == "__main__":
    try:
        print("开始触发学霸盘爬虫任务...")
        run_xuebapans_crawl.delay()
        print("学霸盘爬虫任务已成功放入队列。")
    except Exception as e:
        print(f"触发学霸盘爬虫任务失败: {e}")
