import asyncio
import glob
import os
import sys
from datetime import datetime

import pandas as pd
from tortoise import Tortoise

# 将项目根目录添加到Python路径中
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(ROOT_DIR)

from app.models.resource import PanResource
from app.utils.pan_url_parser import parse_pan_url
from app.core.tortoise_config import TORTOISE_ORM


async def init_db():
    """初始化数据库连接"""
    print("Initializing database...")
    await Tortoise.init(config=TORTOISE_ORM)
    await Tortoise.generate_schemas()
    print("Database initialized.")


async def process_xlsx_file(file_path):
    """处理单个XLSX文件"""
    print(f"--- Processing file: {os.path.basename(file_path)} ---")
    try:
        df = pd.read_excel(file_path, engine="openpyxl")
    except Exception as e:
        print(f"Error reading file {file_path}: {e}")
        return 0, 0

    # 规范化列名，去除首尾空格
    df.columns = [col.strip() for col in df.columns]

    # 根据列名确定文件类型和列映射
    if "文件名" in df.columns and "链接" in df.columns:
        title_col = "文件名"
        url_col = "链接"
        size_col = "文件大小" if "文件大小" in df.columns else None
    elif "文件名称" in df.columns and "链接" in df.columns:  # 兼容另一种可能的列名
        title_col = "文件名称"
        url_col = "链接"
        size_col = "文件大小" if "文件大小" in df.columns else None
    else:
        print(
            f"Skipping file {file_path}: Required columns ('文件名'/'文件名称' and '链接') not found."
        )
        return 0, 0

    success_count = 0
    skipped_count = 0

    for index, row in df.iterrows():
        title = row[title_col]
        original_url = row[url_col]
        file_size = row[size_col] if size_col and pd.notna(row[size_col]) else None

        if not isinstance(original_url, str) or not original_url.strip():
            print(f"Skipping row {index+2}: URL is empty.")
            skipped_count += 1
            continue

        parsed_info = parse_pan_url(original_url)
        if not parsed_info or not parsed_info.get("resource_key"):
            print(
                f"Skipping row {index+2}: Failed to parse URL or get resource_key for '{original_url}'"
            )
            skipped_count += 1
            continue

        resource_key = parsed_info["resource_key"]
        share_pwd = parsed_info.get("share_pwd")
        now = datetime.now()

        defaults = {
            "pan_type": 2,  # 固定为夸克网盘
            "original_url": original_url,
            "title": str(title),
            "file_size": str(file_size) if file_size else None,
            "share_pwd": share_pwd,
            "updated_at": now,
            "created_at": now,  # 如果是新记录，创建时间也更新
        }

        try:
            obj, created = await PanResource.update_or_create(
                resource_key=resource_key, defaults=defaults
            )
            action = "Created" if created else "Updated"
            # print(f"{action}: {obj.title}")
            success_count += 1
        except Exception as e:
            print(f"Error processing row {index+2} for key {resource_key}: {e}")
            skipped_count += 1

    print(f"Successfully processed {success_count} records.")
    print(f"Skipped {skipped_count} records.")
    return success_count, skipped_count


async def main():
    """主函数"""
    await init_db()

    data_dir = os.path.join(ROOT_DIR, "data")
    xlsx_files = glob.glob(os.path.join(data_dir, "*.xlsx"))

    if not xlsx_files:
        print("No .xlsx files found in the 'data' directory.")
        return

    total_success = 0
    total_skipped = 0

    for file_path in xlsx_files:
        success, skipped = await process_xlsx_file(file_path)
        total_success += success
        total_skipped += skipped

    print("\n--- Migration Complete ---")
    print(f"Total records created/updated: {total_success}")
    print(f"Total records skipped: {total_skipped}")

    await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(main())
