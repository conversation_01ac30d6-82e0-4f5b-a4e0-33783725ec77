import logging
from uuid import UUID
from tortoise.transactions import in_transaction
from app.models.resource import PanResource
from app.models.submission import SubmissionTask, SubmissionBatch
from app.models.enums import TaskStatus, BatchStatus, PanType
from typing import Dict, Any

# 模拟各网盘服务，您需要将这些替换为实际的服务调用
from app.services.baidu_pan_service import baidu_pan_service
from app.services.quark_pan_service import quark_pan_service
from app.services.xunlei_pan_service import xunlei_pan_service

from app.services.aliyun_service import aliyun_pan_service  # 如果有阿里云盘服务

logger = logging.getLogger(__name__)


async def get_pan_service(pan_type: PanType):
    """根据网盘类型获取对应的服务实例"""
    if pan_type == PanType.BAIDU:
        return baidu_pan_service
    elif pan_type == PanType.QUARK:
        return quark_pan_service
    elif pan_type == PanType.XUNLEI:
        return xunlei_pan_service
    elif pan_type == PanType.ALIYUN:
        return aliyun_pan_service
    else:
        logger.warning(f"Unsupported pan_type: {pan_type} in get_pan_service")
        return None


async def fetch_and_update_resource_details_from_service(
    service: any, resource: PanResource
) -> Dict[str, Any]:
    """
    调用特定网盘服务的详情获取和更新方法。
    返回一个包含成功状态和可选错误信息的字典。
    成功: {"success": True}
    失败: {"success": False, "error": "错误详情"}
    """
    if hasattr(service, "fetch_and_update_resource_details"):
        try:
            # 从资源中获取is_parsed标志，传递给网盘服务
            is_parsed = resource.is_parsed if resource.is_parsed is not None else True
            logger.info(
                f"调用网盘服务解析资源 {resource.resource_key}，is_parsed={is_parsed}"
            )

            # 调用底层服务，传递is_parsed参数
            result = await service.fetch_and_update_resource_details(
                resource=resource, is_parsed=is_parsed
            )

            # --- 防御性编程：确保返回的是字典 ---
            if isinstance(result, bool):
                if result:  # 如果是 True
                    return {"success": True}
                else:  # 如果是 False
                    return {
                        "success": False,
                        "error": "服务返回了失败状态但未提供具体错误信息。",
                    }

            # 如果已经是字典，直接返回
            if isinstance(result, dict):
                return result

            # 对于其他意外的返回类型
            logger.warning(
                f"服务 {service} 返回了意外的类型: {type(result)}. 将其视为失败。"
            )
            return {"success": False, "error": f"服务返回了意外的类型: {type(result)}"}
            # --- 防御性编程结束 ---

        except Exception as e:
            logger.error(
                f"Error calling fetch_and_update_resource_details for resource ID {resource.id}: {e}",
                exc_info=True,
            )
            # 更新资源状态
            resource.is_parsed = False
            resource.verified_status = "invalid"  # 或其他适当的状态
            await resource.save(
                update_fields=["is_parsed", "verified_status", "updated_at"]
            )
            return {"success": False, "error": f"调用服务时发生内部错误: {e}"}
    else:
        error_msg = f"Service {service} does not have 'fetch_and_update_resource_details' method."
        logger.error(error_msg)
        return {"success": False, "error": error_msg}


async def process_submitted_resource_task(task_id: UUID):
    """
    后台任务，用于处理单个提交的资源URL。
    1. 获取 SubmissionTask。
    2. 如果有关联的 PanResource，获取它。
    3. 调用相应的网盘服务获取资源详情。
    4. 更新 PanResource 和 SubmissionTask 的状态。
    5. 更新 SubmissionBatch 的统计信息。
    注意：此函数应在一个已经建立的数据库事务中调用。
    """
    task = None  # 在try块外部初始化，以便在except块中可用
    try:
        # 事务已由上层 (Celery task) 管理，此处不再需要 in_transaction
        task = await SubmissionTask.get_or_none(id=task_id).prefetch_related(
            "batch", "resource"
        )
        if not task:
            logger.error(f"SubmissionTask with id {task_id} not found.")
            return

        # 允许对 PENDING, ACCEPTED, 甚至 PROCESSING 状态的任务进行处理。
        # 如果一个任务在 PROCESSING 状态下 worker 崩溃，acks_late=True 会使其被重新调度。
        # 我们需要能够重新拾起这个任务并完成它。
        if task.status not in [
            TaskStatus.ACCEPTED,
            TaskStatus.PROCESSING,
        ]:
            logger.info(
                f"Task {task_id} is in a final state ({task.status}). Skipping."
            )
            return

        # 即使任务已经是 PROCESSING 状态，我们依然继续执行，以防是上一次执行中断导致的。
        # 我们首先更新状态和尝试次数。
        task.status = TaskStatus.PROCESSING
        task.attempts += 1
        await task.save(update_fields=["status", "attempts", "updated_at"])

        resource = task.resource

        if not resource:
            task.status = TaskStatus.FAILED_FETCH_DETAILS
            task.error_message = "No PanResource associated with this task."
            await task.save(update_fields=["status", "error_message", "updated_at"])
            await _update_batch_stats(task.batch_id)
            return

        # 对于已解析成功的资源，直接标记任务成功
        if resource.is_parsed and resource.verified_status == "valid":
            task.status = TaskStatus.SUCCESS
            task.error_message = None  # 成功时清除错误信息
            await task.save(update_fields=["status", "error_message", "updated_at"])
            await _update_batch_stats(task.batch_id)
            return

        # 即使资源标记为已解析，但如果状态不是valid，也尝试重新解析
        # 这包括处理那些可能在之前解析失败（verified_status = "invalid"）的资源
        if resource.is_parsed and resource.verified_status != "valid":
            logger.debug(
                f"Resource ID {resource.id} (Task {task_id}) is marked as parsed but status is {resource.verified_status}. Attempting to reparse."
            )
            # 继续执行以下解析逻辑，不提前返回

        # 处理未解析的资源或需要重新解析的资源
        pan_type_enum = (
            PanType(resource.pan_type)
            if resource.pan_type is not None
            else PanType.UNKNOWN
        )
        pan_service = await get_pan_service(pan_type_enum)

        if not pan_service:
            task.status = TaskStatus.FAILED_FETCH_DETAILS
            task.error_message = (
                f"No service available for pan_type: {pan_type_enum.name}"
            )
            logger.warning(task.error_message)

            # 直接删除无法处理的资源记录
            await resource.delete()
        else:
            # 重置资源状态为待解析，以防之前已被标记为无效
            if resource.verified_status == "invalid":
                resource.verified_status = "pending_parse"
                await resource.save(update_fields=["verified_status", "updated_at"])

            # 调用服务获取详情并更新resource对象
            result = await fetch_and_update_resource_details_from_service(
                pan_service, resource
            )

            # fetch_and_update_resource_details_from_service 现在直接返回服务层的结果
            # 服务层保证成功时 is_parsed=True, verified_status="valid"
            if result.get("success"):
                task.status = TaskStatus.SUCCESS
                task.error_message = None  # 成功时清除错误信息
            else:
                # 处理失败的情况
                task.status = TaskStatus.FAILED_FETCH_DETAILS
                task.error_message = result.get("error", "处理时发生未知错误")
                logger.warning(
                    f"Failed to process resource ID {resource.id} (Task {task_id}): {task.error_message}"
                )
                # 删除处理失败的资源记录
                await resource.delete()

        await task.save(update_fields=["status", "error_message", "updated_at"])
        await _update_batch_stats(task.batch_id)

    except Exception as e:
        logger.error(
            f"Exception in process_submitted_resource_task for task_id {task_id}: {e}",
            exc_info=True,
        )
        try:
            # 尝试标记任务失败，如果还能获取到task对象
            task = await SubmissionTask.get_or_none(id=task_id)
            if task:
                task.status = TaskStatus.FAILED_FETCH_DETAILS
                task.error_message = str(e)
                await task.save(update_fields=["status", "error_message", "updated_at"])

                # 更新关联资源的状态 -> 改为直接删除
                if task.resource_id:
                    resource_to_delete = await PanResource.get_or_none(
                        id=task.resource_id
                    )
                    if resource_to_delete:
                        logger.warning(
                            f"Task {task_id} failed with exception, deleting associated resource {resource_to_delete.id}"
                        )
                        await resource_to_delete.delete()

                await _update_batch_stats(task.batch_id)
        except Exception as inner_e:
            logger.error(
                f"Failed to update task status after exception for task_id {task_id}: {inner_e}",
                exc_info=True,
            )


async def _update_batch_stats(batch_id: UUID):
    """更新批处理的统计数据和状态。
    注意：此函数应在一个已经建立的数据库事务中调用。
    """
    try:
        # 事务已由上层管理，此处不再需要 in_transaction
        batch = await SubmissionBatch.get_or_none(id=batch_id)
        if not batch:
            logger.error(
                f"SubmissionBatch with id {batch_id} not found for stat update."
            )
            return

        tasks_in_batch = await SubmissionTask.filter(batch_id=batch_id).all()

        batch.successful_tasks = sum(
            1 for t in tasks_in_batch if t.status == TaskStatus.SUCCESS
        )
        # 定义哪些状态算作失败
        failed_statuses = {
            TaskStatus.FAILED_FETCH_DETAILS,
            TaskStatus.FAILED_PARSE_URL,
            TaskStatus.INVALID_URL,
            TaskStatus.FAILED_DUPLICATE,
        }
        batch.failed_tasks = sum(
            1 for t in tasks_in_batch if t.status in failed_statuses
        )

        # 任务创建数 tasks_created 应该在创建任务时就确定了，这里不修改
        # total_urls_submitted 也是在批次创建时确定

        processed_count = (
            batch.successful_tasks + batch.failed_tasks + batch.skipped_tasks
        )
        # 可以考虑更细致的"已处理"定义，例如包括所有非 PENDING/PROCESSING 状态

        # 使用 total_urls_submitted 作为判断基准
        if processed_count >= batch.total_urls_submitted:
            # 如果已处理的总数（成功+失败+跳过）等于或超过了最初提交的总数
            if (
                batch.failed_tasks == 0
                and batch.successful_tasks + batch.skipped_tasks
                == batch.total_urls_submitted
            ):
                batch.status = BatchStatus.COMPLETED
            elif batch.successful_tasks > 0:
                batch.status = BatchStatus.PARTIAL_FAILURE
            elif (
                batch.failed_tasks + batch.skipped_tasks == batch.total_urls_submitted
                and batch.total_urls_submitted > 0
            ):
                batch.status = BatchStatus.FAILED
            else:
                batch.status = BatchStatus.COMPLETED
        else:
            batch.status = BatchStatus.PROCESSING

        await batch.save(
            update_fields=[
                "status",
                "successful_tasks",
                "failed_tasks",
                "updated_at",
            ]
        )
        logger.info(
            f"Updated stats for SubmissionBatch {batch_id}: Status={batch.status}, Success={batch.successful_tasks}, Failed={batch.failed_tasks}"
        )

    except Exception as e:
        logger.error(
            f"Error updating batch stats for batch_id {batch_id}: {e}", exc_info=True
        )
