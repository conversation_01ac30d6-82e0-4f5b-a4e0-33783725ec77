#!/bin/bash

echo "🔄 重启Telegram监控服务..."

# 1. 停止服务
echo "📴 停止telegram-monitor服务..."
pm2 stop telegram-monitor

# 2. 删除服务（清除PM2缓存）
echo "🗑️ 删除服务以清除缓存..."
pm2 delete telegram-monitor

# 3. 清除Python缓存
echo "🧹 清除Python缓存文件..."
find /root/pan-so-backend -name "*.pyc" -delete
find /root/pan-so-backend -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# 4. 等待一下
echo "⏳ 等待2秒..."
sleep 2

# 5. 重新启动服务
echo "🚀 重新启动telegram-monitor服务..."
pm2 start pan_so_pm2.json --only telegram-monitor

# 6. 显示服务状态
echo "📊 服务状态:"
pm2 status telegram-monitor

# 7. 显示最新日志
echo "📝 最新日志 (等待5秒后显示):"
sleep 5
pm2 logs telegram-monitor --lines 20

echo "✅ 重启完成！"
echo "💡 使用 'pm2 logs telegram-monitor' 查看实时日志"
