import httpx
import datetime
from app.utils.config import settings
from app.crawlers.base_crawler import BaseCrawler
from app.utils.pan_url_parser import parse_pan_url
from app.models.resource import PanResource
from app.utils.common import BEIJING_TIMEZONE
from tortoise.exceptions import IntegrityError


class DuanjuCrawler(BaseCrawler):
    """
    duanju.click 短剧爬虫，实现短剧资源的自动采集与入库。
    """

    def __init__(self):
        # 读取配置
        self.config = settings.get("duanju_crawler", {})
        self.enabled = self.config.get("enabled", False)
        self.page_size = self.config.get("page_size", 100)
        self.base_url = self.config.get(
            "base_url", "https://www.duanju.click/api/play/quark/lates"
        )
        self.headers = self.config.get("headers", {})
        self.cookies = self.config.get("cookies", "")

    async def fetch_and_save(self):
        """
        主流程：拉取短剧数据并批量入库。
        """
        if not self.enabled:
            print("duanju.click爬虫未启用，跳过采集。")
            return
        params = {"pageSize": self.page_size}
        try:
            async with httpx.AsyncClient(timeout=15) as client:
                resp = await client.get(
                    self.base_url,
                    params=params,
                    headers=self.headers,
                    cookies=self._parse_cookies(self.cookies),
                )
                resp.raise_for_status()
                data = resp.json()
        except Exception as e:
            print(f"duanju.click爬虫请求失败: {e}")
            return
        if not data or data.get("code") != 200:
            print(f"duanju.click响应异常: {data}")
            return
        items = data.get("data", [])
        resources = []
        for item in items:
            link = item.get("link")
            name = item.get("name")
            time_str = item.get("time")
            # 解析网盘链接
            pan_info = parse_pan_url(link)
            if not pan_info:
                print(f"无法识别网盘链接: {link}")
                continue
            # 时间格式转换，确保使用北京时区
            try:
                # 解析时间字符串，假设为北京时间
                update_at = datetime.datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
                update_at = update_at.replace(tzinfo=BEIJING_TIMEZONE)
            except Exception:
                # 异常情况下使用当前北京时间
                update_at = datetime.datetime.now(BEIJING_TIMEZONE)
            resource = PanResource(
                resource_key=pan_info["resource_key"],
                pan_type=pan_info["pan_type_int"],
                original_url=link,
                title=name,
                share_pwd=pan_info.get("share_pwd"),
                updated_at=update_at,
                file_type="video",
            )
            resources.append(resource)
        # 批量入库
        if resources:
            try:
                await PanResource.bulk_create(resources, ignore_conflicts=True)
                print(f"duanju.click采集入库成功，共{len(resources)}条。")
            except IntegrityError as e:
                print(f"批量入库部分冲突: {e}")
        else:
            print("无可入库资源。")

    def _parse_cookies(self, cookie_str):
        """
        将cookie字符串转为字典
        """
        cookies = {}
        for kv in cookie_str.split(";"):
            if "=" in kv:
                k, v = kv.strip().split("=", 1)
                cookies[k] = v
        return cookies

    async def crawl(self):
        """占位方法，暂不实现"""
        pass

    async def start_monitoring(self):
        """占位方法，暂不实现"""
        pass
