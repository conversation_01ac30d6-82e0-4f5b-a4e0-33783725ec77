#!/usr/bin/env python3
"""
时间过滤功能部署脚本

这个脚本用于更新Meilisearch索引设置，添加时间过滤支持。
适用于测试环境和生产环境的安全部署。

使用方法:
python production_update_script.py
"""

import asyncio
import sys
import os
from datetime import datetime, timezone, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.meilisearch_service import meilisearch_service
from app.utils.config import settings


async def wait_for_task(task):
    """等待Meilisearch任务完成"""
    try:
        client = meilisearch_service.client
        
        # 处理不同的任务ID格式
        if hasattr(task, 'task_uid'):
            task_uid = task.task_uid
        elif hasattr(task, 'taskUid'):
            task_uid = task.taskUid
        elif isinstance(task, dict):
            task_uid = task.get('taskUid') or task.get('task_uid')
        else:
            task_uid = str(task)
        
        print(f"等待任务完成: {task_uid}")
        
        # 等待任务完成
        client.wait_for_task(str(task_uid))
        print(f"✅ 任务 {task_uid} 完成成功")
        return True
        
    except Exception as e:
        print(f"❌ 等待任务时出错: {e}")
        return False


async def check_meilisearch_connection():
    """检查Meilisearch连接"""
    print("检查Meilisearch连接...")
    
    try:
        health = meilisearch_service.client.health()
        print(f"✅ Meilisearch连接正常: {health}")
        return True
    except Exception as e:
        print(f"❌ 无法连接到Meilisearch: {e}")
        print("请确保Meilisearch服务正在运行")
        return False


async def check_current_settings():
    """检查当前索引设置"""
    print("\n检查当前Meilisearch索引设置...")
    
    try:
        settings = meilisearch_service.index.get_settings()
        filterable = settings.get('filterableAttributes', [])
        sortable = settings.get('sortableAttributes', [])
        
        print(f"当前可过滤属性: {filterable}")
        print(f"当前可排序属性: {sortable}")
        
        # 检查updated_at是否已经在可过滤属性中
        if 'updated_at' in filterable:
            print("✅ updated_at 已经在可过滤属性中")
            return True
        else:
            print("⚠️  updated_at 不在可过滤属性中，需要更新")
            return False
            
    except Exception as e:
        print(f"❌ 检查设置时出错: {e}")
        return False


async def update_index_settings():
    """更新索引设置，添加时间过滤支持"""
    print("\n开始更新Meilisearch索引设置...")
    
    try:
        # 新的设置（与sync_meilisearch.py保持一致）
        new_filterable_attributes = [
            "pan_type",
            "file_type", 
            "verified_status",
            "author",
            "updated_at",  # 时间过滤支持
        ]
        
        new_sortable_attributes = [
            "access_count", 
            "created_at", 
            "updated_at",  # 时间排序支持
            "title"
        ]
        
        print(f"准备设置可过滤属性: {new_filterable_attributes}")
        print(f"准备设置可排序属性: {new_sortable_attributes}")
        
        # 更新设置
        task = meilisearch_service.index.update_settings({
            "filterableAttributes": new_filterable_attributes,
            "sortableAttributes": new_sortable_attributes,
        })
        
        # 等待任务完成
        success = await wait_for_task(task)
        
        if success:
            print("✅ 索引设置更新成功！")
            return True
        else:
            print("❌ 索引设置更新失败")
            return False
            
    except Exception as e:
        print(f"❌ 更新索引设置时出错: {e}")
        return False


async def check_data_format():
    """检查现有数据的时间字段格式"""
    print("\n检查现有数据的时间字段格式...")
    
    try:
        # 获取一些示例数据
        result = meilisearch_service.index.search("", {"limit": 5})
        hits = result.get('hits', [])
        
        if not hits:
            print("⚠️  索引中没有数据")
            return True
        
        print("现有数据的时间字段格式:")
        for i, hit in enumerate(hits):
            title = hit.get('title', '未知标题')[:30]
            updated_at = hit.get('updated_at', '未知')
            created_at = hit.get('created_at', '未知')
            
            print(f"  {i+1}. {title}")
            print(f"     updated_at: {updated_at} (类型: {type(updated_at).__name__})")
            print(f"     created_at: {created_at}")
        
        # 检查时间格式是否为ISO字符串
        first_updated_at = hits[0].get('updated_at')
        if isinstance(first_updated_at, str) and 'T' in first_updated_at:
            print("✅ 数据时间格式正确（ISO字符串格式）")
            return True
        else:
            print("⚠️  数据时间格式可能需要检查")
            return True  # 不阻止部署，只是警告
            
    except Exception as e:
        print(f"❌ 检查数据格式时出错: {e}")
        return False


async def verify_time_filter():
    """验证时间过滤功能"""
    print("\n验证时间过滤功能...")
    
    try:
        # 测试简单的时间过滤查询
        now = datetime.now(timezone.utc)
        week_ago = now - timedelta(days=7)
        
        start_iso = week_ago.isoformat()
        end_iso = now.isoformat()
        
        test_filter = f"updated_at >= '{start_iso}' AND updated_at <= '{end_iso}'"
        print(f"测试过滤条件: {test_filter}")
        
        result = meilisearch_service.index.search("", {
            "filter": test_filter,
            "limit": 5
        })
        
        hits = result.get('hits', [])
        print(f"✅ 时间过滤测试成功！找到 {len(hits)} 条结果")
        
        if hits:
            print("示例结果:")
            for i, hit in enumerate(hits[:2]):
                title = hit.get('title', '未知标题')[:40]
                updated_at = hit.get('updated_at', '未知时间')
                print(f"  {i+1}. {title} (时间: {updated_at})")
        
        return True
        
    except Exception as e:
        print(f"❌ 时间过滤验证失败: {e}")
        return False


async def test_api_endpoints():
    """测试API接口的时间过滤功能"""
    print("\n测试API接口的时间过滤功能...")
    
    try:
        import requests
        
        # 测试search接口
        print("测试 /api/search 接口...")
        response = requests.get(
            "http://localhost:8000/api/search",
            params={
                "keyword": "test",
                "time_filter": "week",
                "limit": 3
            },
            headers={"accept": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ search接口测试成功: {data.get('total')} 条结果")
        else:
            print(f"⚠️  search接口测试失败: {response.status_code}")
        
        # 测试cached_resources接口
        print("测试 /api/cached_resources 接口...")
        response = requests.get(
            "http://localhost:8000/api/cached_resources",
            params={
                "title": "test",
                "time_filter": "week",
                "limit": 3
            },
            headers={"accept": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ cached_resources接口测试成功: {data.get('total')} 条结果")
        else:
            print(f"⚠️  cached_resources接口测试失败: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"⚠️  API接口测试跳过: {e}")
        print("（可能是因为API服务未启动，这不影响索引设置更新）")
        return True


async def main():
    """主函数"""
    print("时间过滤功能部署脚本")
    print("=" * 60)
    print(f"执行时间: {datetime.now()}")
    print()
    
    # 1. 检查Meilisearch连接
    if not await check_meilisearch_connection():
        return 1
    
    # 2. 检查当前设置
    current_ok = await check_current_settings()
    
    # 3. 检查现有数据格式
    data_ok = await check_data_format()
    
    # 4. 更新索引设置（如果需要）
    if not current_ok:
        settings_ok = await update_index_settings()
        if not settings_ok:
            return 1
        
        # 重新检查设置
        print("\n重新检查索引设置...")
        updated_settings = meilisearch_service.index.get_settings()
        updated_filterable = updated_settings.get('filterableAttributes', [])
        if 'updated_at' in updated_filterable:
            print("✅ 确认 updated_at 已添加到可过滤属性中")
        else:
            print("❌ updated_at 仍不在可过滤属性中")
            return 1
    else:
        print("\n索引设置已经是最新的，跳过更新")
        settings_ok = True
    
    # 5. 验证时间过滤功能
    if settings_ok:
        verify_ok = await verify_time_filter()
        if not verify_ok:
            print("\n⚠️  时间过滤验证失败，但索引设置已更新")
            print("请检查数据同步情况")
    
    # 6. 测试API接口（可选）
    await test_api_endpoints()
    
    # 7. 总结
    print("\n" + "=" * 60)
    print("部署总结:")
    print(f"  ✅ Meilisearch连接: 正常")
    print(f"  {'✅' if current_ok else '🔄'} 索引设置: {'已是最新' if current_ok else '已更新'}")
    print(f"  {'✅' if data_ok else '⚠️ '} 数据格式: {'正常' if data_ok else '需要检查'}")
    print(f"  {'✅' if verify_ok else '⚠️ '} 功能验证: {'成功' if verify_ok else '需要检查'}")
    
    if current_ok or settings_ok:
        print("\n🎉 时间过滤功能部署完成！")
        print("现在可以在API中使用time_filter参数了。")
        print("\n使用示例:")
        print("  GET /api/search?keyword=test&time_filter=week")
        print("  GET /api/cached_resources?title=test&time_filter=month")
        print("\n支持的时间过滤选项:")
        print("  - all: 全部时间")
        print("  - week: 最近一周")
        print("  - half_month: 最近半月")
        print("  - month: 最近一月")
        print("  - half_year: 最近半年")
        print("  - year: 最近一年")
        return 0
    else:
        print("\n❌ 部署失败，请检查错误信息")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
