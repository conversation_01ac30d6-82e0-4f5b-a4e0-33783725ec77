from fastapi import Request
from fastapi.responses import JSONResponse
import logging

# 全局变量监控并发请求
current_active_requests = 0
MAX_CONCURRENT_REQUESTS = 10  # 调整最大并发请求数，平衡资源利用率

logger = logging.getLogger("concurrency-middleware")


async def concurrency_limiter(request: Request, call_next):
    """
    限制并发请求数量的中间件
    超过最大并发数时返回429 Too Many Requests
    """
    global current_active_requests

    if current_active_requests >= MAX_CONCURRENT_REQUESTS:
        logger.warning(f"达到最大并发请求限制: {MAX_CONCURRENT_REQUESTS}，拒绝请求")
        return JSONResponse(
            status_code=429,
            content={"status": "error", "message": "服务器繁忙，请稍后重试"},
        )

    # 记录当前请求
    current_active_requests += 1
    logger.debug(f"当前活跃请求数: {current_active_requests}")
    
    try:
        # 处理请求
        response = await call_next(request)
        return response
    finally:
        # 请求完成，减少计数
        current_active_requests -= 1
        logger.debug(f"请求完成，当前活跃请求数: {current_active_requests}") 