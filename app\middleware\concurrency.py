from fastapi import Request
from fastapi.responses import JSONResponse
import logging
from app.utils.config import settings

# 全局变量监控并发请求
current_active_requests = 0
MAX_CONCURRENT_REQUESTS = settings.get(
    "concurrency_limiter.times", 10
)  # 从配置文件读取

logger = logging.getLogger("concurrency-middleware")


def update_max_concurrent_requests(new_limit: int):
    """动态更新最大并发请求数"""
    global MAX_CONCURRENT_REQUESTS
    MAX_CONCURRENT_REQUESTS = new_limit
    logger.info(f"最大并发请求数已更新为: {new_limit}")


def get_current_concurrent_status():
    """获取当前并发状态"""
    return {
        "current_active": current_active_requests,
        "max_allowed": MAX_CONCURRENT_REQUESTS,
        "usage_percentage": (
            (current_active_requests / MAX_CONCURRENT_REQUESTS) * 100
            if MAX_CONCURRENT_REQUESTS > 0
            else 0
        ),
    }


async def concurrency_limiter(request: Request, call_next):
    """
    限制并发请求数量的中间件
    超过最大并发数时返回429 Too Many Requests
    """
    global current_active_requests

    if current_active_requests >= MAX_CONCURRENT_REQUESTS:
        logger.warning(f"达到最大并发请求限制: {MAX_CONCURRENT_REQUESTS}，拒绝请求")
        return JSONResponse(
            status_code=429,
            content={"status": "error", "message": "服务器繁忙，请稍后重试"},
        )

    # 记录当前请求
    current_active_requests += 1
    logger.debug(f"当前活跃请求数: {current_active_requests}")

    try:
        # 处理请求
        response = await call_next(request)
        return response
    finally:
        # 请求完成，减少计数
        current_active_requests -= 1
        logger.debug(f"请求完成，当前活跃请求数: {current_active_requests}")
