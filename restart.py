import os
import sys
import signal
import subprocess
import time
import psutil

def find_process_by_cmdline(pattern):
    """查找命令行包含指定模式的进程"""
    matching_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info['cmdline']
            if cmdline and any(pattern in cmd for cmd in cmdline):
                matching_processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return matching_processes

def kill_processes(processes):
    """结束指定的进程列表"""
    for proc in processes:
        try:
            print(f"正在结束进程: PID={proc.pid}, CMD={' '.join(proc.info['cmdline'])}")
            proc.terminate()
        except Exception as e:
            print(f"结束进程 {proc.pid} 时出错: {str(e)}")
    
    # 等待进程结束
    gone, alive = psutil.wait_procs(processes, timeout=5)
    for proc in alive:
        try:
            print(f"进程未响应终止命令，强制结束: PID={proc.pid}")
            proc.kill()
        except Exception as e:
            print(f"强制结束进程 {proc.pid} 时出错: {str(e)}")

def main():
    """主函数"""
    # 查找并结束相关进程
    api_processes = find_process_by_cmdline("app.main:app")
    
    if api_processes:
        print(f"找到 {len(api_processes)} 个相关API进程，正在结束...")
        kill_processes(api_processes)
    else:
        print("未找到运行中的API进程")
    
    # 启动API服务
    print("正在启动API服务...")
    cmd = [sys.executable, "-m", "uvicorn", "app.main:app", "--host", "127.0.0.1", "--port", "9999"]
    
    try:
        # 使用subprocess.Popen启动进程
        process = subprocess.Popen(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NEW_CONSOLE  # 创建新控制台窗口
        )
        
        print(f"API服务已启动，PID: {process.pid}")
        print("请检查新窗口中的日志输出")
        
    except Exception as e:
        print(f"启动API服务时出错: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 