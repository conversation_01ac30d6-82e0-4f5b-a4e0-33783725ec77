# 基础认证和权限系统详细设计方案

基于V4版本规划和现有代码结构分析，本文档提供了一套完整的基础认证和权限系统设计方案，包括用户注册登录、邮箱验证、忘记密码、管理员后台和失效资源反馈管理等功能。

## 一、系统架构设计

### 1.1 技术栈选择
- **后端认证**: JWT Token + Session 双重认证
- **权限控制**: RBAC (基于角色的访问控制)
- **邮箱服务**: SMTP + 模板引擎
- **前端框架**: Vue 3 + Element Plus (推荐)
- **状态管理**: Pinia
- **HTTP客户端**: Axios

### 1.2 认证流程设计

#### 用户注册流程
```
用户 -> 前端 -> 认证API -> 邮箱服务 -> 数据库
1. 用户填写注册信息
2. 前端发送 POST /api/auth/register
3. 认证API发送验证邮件
4. 认证API创建待验证用户
5. 用户点击邮件验证链接
6. 认证API激活用户账户
```

#### 用户登录流程
```
用户 -> 前端 -> 认证API -> 数据库
1. 用户输入用户名密码
2. 前端发送 POST /api/auth/login
3. 认证API验证用户凭据
4. 认证API返回JWT Token
5. 前端存储Token到localStorage
```

## 二、数据库设计

### 2.1 核心表结构

#### 用户表 (users)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    nickname VARCHAR(100),
    avatar VARCHAR(512),
    phone VARCHAR(20),
    status VARCHAR(20) DEFAULT 'pending', -- pending/active/frozen/deleted
    role_id INT REFERENCES roles(id) DEFAULT 2, -- 默认普通用户
    email_verified BOOLEAN DEFAULT FALSE,
    email_verify_token VARCHAR(255),
    password_reset_token VARCHAR(255),
    password_reset_expires TIMESTAMPTZ,
    last_login_at TIMESTAMPTZ,
    login_attempts INT DEFAULT 0,
    locked_until TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

#### 角色表 (roles)
```sql
CREATE TABLE roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL, -- admin/user/guest
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    permissions JSONB NOT NULL DEFAULT '[]',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

#### 失效资源反馈表 (resource_feedback)
```sql
CREATE TABLE resource_feedback (
    id SERIAL PRIMARY KEY,
    resource_id INT REFERENCES pan_resources(id) ON DELETE CASCADE,
    user_id INT REFERENCES users(id) ON DELETE SET NULL,
    feedback_type VARCHAR(20) NOT NULL, -- expired/invalid/broken/other
    description TEXT,
    contact_info VARCHAR(255), -- 联系方式（可选）
    status VARCHAR(20) DEFAULT 'pending', -- pending/processing/resolved/rejected
    admin_notes TEXT, -- 管理员处理备注
    processed_by INT REFERENCES users(id) ON DELETE SET NULL,
    processed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

#### 用户会话表 (user_sessions)
```sql
CREATE TABLE user_sessions (
    id SERIAL PRIMARY KEY,
    user_id INT REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    refresh_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    user_agent TEXT,
    ip_address INET,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
```

### 2.2 权限设计

#### 预定义角色和权限
```python
DEFAULT_ROLES = [
    {
        'name': 'admin',
        'display_name': '管理员',
        'permissions': [
            'user.manage',           # 用户管理
            'resource.manage',       # 资源管理
            'feedback.manage',       # 反馈管理
            'system.config',         # 系统配置
            'resource.submit',       # 资源提交
        ]
    },
    {
        'name': 'user',
        'display_name': '注册用户',
        'permissions': [
            'resource.submit',       # 资源提交
            'feedback.create',       # 创建反馈
            'profile.manage',        # 个人资料管理
        ]
    },
    {
        'name': 'guest',
        'display_name': '游客',
        'permissions': [
            'resource.view',         # 查看资源
        ]
    }
]
```

## 三、API接口设计

### 3.1 认证相关接口

```python
# 用户认证模块
POST   /api/auth/register              # 用户注册
GET    /api/auth/verify-email          # 邮箱验证
POST   /api/auth/login                 # 用户登录
POST   /api/auth/logout                # 用户登出
POST   /api/auth/refresh               # 刷新Token
POST   /api/auth/forgot-password       # 忘记密码
POST   /api/auth/reset-password        # 重置密码
GET    /api/auth/profile               # 获取用户信息
PUT    /api/auth/profile               # 更新用户信息
POST   /api/auth/change-password       # 修改密码
POST   /api/auth/resend-verification   # 重发验证邮件
```

### 3.2 管理员用户管理接口

```python
# 管理员用户管理
GET    /api/admin/users                # 用户列表（分页、搜索、筛选）
GET    /api/admin/users/{id}           # 用户详情
PUT    /api/admin/users/{id}           # 更新用户信息
DELETE /api/admin/users/{id}           # 删除用户
POST   /api/admin/users/{id}/freeze    # 冻结用户
POST   /api/admin/users/{id}/unfreeze  # 解冻用户
POST   /api/admin/users/{id}/reset-password  # 重置用户密码
GET    /api/admin/users/{id}/sessions  # 用户会话列表
DELETE /api/admin/users/{id}/sessions/{session_id}  # 强制下线
```

### 3.3 管理员资源管理接口

```python
# 管理员资源管理
GET    /api/admin/resources            # 资源列表（支持高级搜索）
GET    /api/admin/resources/{id}       # 资源详情
DELETE /api/admin/resources/{id}       # 删除单个资源
POST   /api/admin/resources/batch-delete  # 批量删除资源
PUT    /api/admin/resources/{id}/status    # 更新资源状态
GET    /api/admin/resources/stats      # 资源统计信息
```

### 3.4 失效资源反馈管理接口

```python
# 失效资源反馈
POST   /api/feedback/report            # 提交失效资源反馈
GET    /api/feedback/my                # 我的反馈记录

# 管理员反馈管理
GET    /api/admin/feedback             # 反馈列表（分页、筛选）
GET    /api/admin/feedback/{id}        # 反馈详情
PUT    /api/admin/feedback/{id}        # 处理反馈
POST   /api/admin/feedback/{id}/resolve    # 标记为已解决
POST   /api/admin/feedback/{id}/reject     # 拒绝反馈
GET    /api/admin/feedback/stats       # 反馈统计
```

## 四、安全设计

### 4.1 密码安全
- 使用 bcrypt 进行密码哈希
- 密码强度要求：至少8位，包含大小写字母、数字
- 登录失败锁定机制：5次失败锁定30分钟

### 4.2 JWT Token 设计
```python
JWT_CONFIG = {
    'algorithm': 'HS256',
    'access_token_expire_minutes': 30,    # 访问令牌30分钟
    'refresh_token_expire_days': 7,       # 刷新令牌7天
    'secret_key': os.getenv('JWT_SECRET_KEY'),
    'issuer': 'pan-so-backend',
}

# Token Payload 结构
{
    'user_id': 123,
    'username': 'user123',
    'role': 'user',
    'permissions': ['resource.submit', 'feedback.create'],
    'exp': 1640995200,
    'iat': 1640991600,
    'iss': 'pan-so-backend'
}
```

### 4.3 邮箱验证设计
- 注册时发送验证邮件，24小时内有效
- 验证链接包含加密token
- 支持重发验证邮件（限制频率）

## 五、前端界面设计

### 5.1 用户认证界面

#### 登录页面 (/login)
- 用户名/邮箱输入框
- 密码输入框
- 记住我选项
- 忘记密码链接
- 登录按钮
- 注册链接

#### 注册页面 (/register)
- 用户名输入框
- 邮箱地址输入框
- 密码输入框
- 确认密码输入框
- 用户协议和隐私政策同意选项
- 注册按钮
- 登录链接

#### 忘记密码页面 (/forgot-password)
- 邮箱输入框
- 发送重置邮件按钮
- 返回登录链接

#### 重置密码页面 (/reset-password)
- 新密码输入框
- 确认新密码输入框
- 重置密码按钮

### 5.2 管理员后台界面

#### 管理员仪表板 (/admin/dashboard)
- 系统概览统计卡片
- 用户增长趋势图表
- 资源统计图表
- 反馈处理状态统计
- 最近活动日志

#### 用户管理页面 (/admin/users)
- 用户搜索和筛选功能
- 用户列表表格（用户名、邮箱、角色、状态、注册时间）
- 用户操作（查看、编辑、冻结/解冻、删除、重置密码）
- 分页功能
- 新增用户功能

#### 资源管理页面 (/admin/resources)
- 资源搜索和高级筛选
- 资源列表表格（标题、类型、来源、状态、创建时间）
- 资源操作（查看详情、删除、批量删除）
- 分页功能
- 资源统计信息

#### 失效资源反馈管理页面 (/admin/feedback)
- 反馈统计卡片（待处理、处理中、已解决、总计）
- 反馈筛选功能（类型、状态、时间范围）
- 反馈列表表格（资源信息、反馈类型、问题描述、反馈用户、状态、时间）
- 反馈操作（查看详情、处理、批量处理）
- 分页功能
- 导出数据功能

### 5.3 前端界面代码示例

#### 登录页面组件 (LoginPage.vue)
```vue
<template>
  <div class="login-container">
    <el-card class="login-card">
      <h2>用户登录</h2>
      <el-form :model="loginForm" :rules="loginRules" ref="loginFormRef">
        <el-form-item prop="username">
          <el-input v-model="loginForm.username" placeholder="用户名/邮箱">
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="loginForm.password" type="password" placeholder="密码">
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="loginForm.remember">记住我</el-checkbox>
          <el-link type="primary" @click="$router.push('/forgot-password')">
            忘记密码？
          </el-link>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleLogin" :loading="loading" style="width: 100%">
            登录
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-link @click="$router.push('/register')">
            还没有账户？立即注册
          </el-link>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const router = useRouter()
const authStore = useAuthStore()
const loading = ref(false)
const loginFormRef = ref()

const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名或邮箱', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  if (!loginFormRef.value) return

  await loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        await authStore.login(loginForm)
        ElMessage.success('登录成功')
        router.push('/')
      } catch (error) {
        ElMessage.error(error.message || '登录失败')
      } finally {
        loading.value = false
      }
    }
  })
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
  width: 400px;
  padding: 20px;
}
</style>
```

#### 注册页面组件 (RegisterPage.vue)
```vue
<template>
  <div class="register-container">
    <el-card class="register-card">
      <h2>用户注册</h2>
      <el-form :model="registerForm" :rules="registerRules" ref="registerFormRef">
        <el-form-item prop="username">
          <el-input v-model="registerForm.username" placeholder="用户名">
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="email">
          <el-input v-model="registerForm.email" placeholder="邮箱地址">
            <template #prefix>
              <el-icon><Message /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input v-model="registerForm.password" type="password" placeholder="密码">
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="confirmPassword">
          <el-input v-model="registerForm.confirmPassword" type="password" placeholder="确认密码">
            <template #prefix>
              <el-icon><Lock /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-checkbox v-model="registerForm.agree">
            我已阅读并同意<el-link type="primary">用户协议</el-link>和<el-link type="primary">隐私政策</el-link>
          </el-checkbox>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleRegister" :loading="loading" style="width: 100%">
            注册
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-link @click="$router.push('/login')">
            已有账户？立即登录
          </el-link>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>
```

#### 管理员用户管理页面 (AdminUsers.vue)
```vue
<template>
  <div class="admin-users">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>用户管理</span>
          <el-button type="primary" @click="showCreateDialog">新增用户</el-button>
        </div>
      </template>

      <!-- 搜索筛选区域 -->
      <el-form :model="searchForm" inline>
        <el-form-item label="搜索">
          <el-input v-model="searchForm.keyword" placeholder="用户名/邮箱" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="全部状态" clearable>
            <el-option label="活跃" value="active" />
            <el-option label="冻结" value="frozen" />
            <el-option label="待验证" value="pending" />
          </el-select>
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="searchForm.role" placeholder="全部角色" clearable>
            <el-option label="管理员" value="admin" />
            <el-option label="普通用户" value="user" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 用户列表表格 -->
      <el-table :data="userList" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="username" label="用户名" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="role.display_name" label="角色" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="last_login_at" label="最后登录" />
        <el-table-column prop="created_at" label="注册时间" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="viewUser(row)">查看</el-button>
            <el-button size="small" type="warning" @click="editUser(row)">编辑</el-button>
            <el-dropdown @command="handleUserAction">
              <el-button size="small">
                更多<el-icon><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{action: 'freeze', user: row}" v-if="row.status === 'active'">
                    冻结
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'unfreeze', user: row}" v-if="row.status === 'frozen'">
                    解冻
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'resetPassword', user: row}">
                    重置密码
                  </el-dropdown-item>
                  <el-dropdown-item :command="{action: 'delete', user: row}" divided>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>
```

#### 失效资源反馈管理页面 (AdminFeedback.vue)
```vue
<template>
  <div class="admin-feedback">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>失效资源反馈管理</span>
          <el-button-group>
            <el-button @click="handleBatchProcess" :disabled="!selectedFeedback.length">
              批量处理
            </el-button>
            <el-button @click="exportFeedback">导出数据</el-button>
          </el-button-group>
        </div>
      </template>

      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ stats.pending }}</div>
              <div class="stat-label">待处理</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ stats.processing }}</div>
              <div class="stat-label">处理中</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ stats.resolved }}</div>
              <div class="stat-label">已解决</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总计</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 筛选区域 -->
      <el-form :model="filterForm" inline>
        <el-form-item label="反馈类型">
          <el-select v-model="filterForm.type" placeholder="全部类型" clearable>
            <el-option label="链接失效" value="expired" />
            <el-option label="资源无效" value="invalid" />
            <el-option label="链接损坏" value="broken" />
            <el-option label="其他问题" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="处理状态">
          <el-select v-model="filterForm.status" placeholder="全部状态" clearable>
            <el-option label="待处理" value="pending" />
            <el-option label="处理中" value="processing" />
            <el-option label="已解决" value="resolved" />
            <el-option label="已拒绝" value="rejected" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">筛选</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 反馈列表 -->
      <el-table
        :data="feedbackList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="资源信息" width="300">
          <template #default="{ row }">
            <div class="resource-info">
              <div class="resource-title">{{ row.resource.title }}</div>
              <div class="resource-url">{{ row.resource.original_url }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="feedback_type" label="反馈类型">
          <template #default="{ row }">
            <el-tag :type="getFeedbackTypeColor(row.feedback_type)">
              {{ getFeedbackTypeText(row.feedback_type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="问题描述" show-overflow-tooltip />
        <el-table-column prop="user.username" label="反馈用户" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="反馈时间" />
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button size="small" @click="viewFeedback(row)">查看</el-button>
            <el-button size="small" type="primary" @click="processFeedback(row)"
                       v-if="row.status === 'pending'">
              处理
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
  </div>
</template>
```

## 六、实施计划

### 6.1 第一阶段：数据库模型和基础认证 (5-7天)
1. 创建用户、角色、会话、反馈相关数据库模型
2. 实现JWT认证服务和中间件
3. 开发用户注册、登录、登出API
4. 集成邮箱服务实现邮箱验证

### 6.2 第二阶段：权限系统和管理员功能 (4-6天)
1. 实现RBAC权限控制系统
2. 开发管理员用户管理API
3. 开发管理员资源管理API
4. 实现失效资源反馈系统

### 6.3 第三阶段：前端界面开发 (8-10天)
1. 开发用户认证界面（登录、注册、忘记密码）
2. 开发管理员后台界面
3. 实现响应式设计和移动端适配
4. 集成前后端认证流程

### 6.4 第四阶段：测试和优化 (3-5天)
1. 功能测试和安全测试
2. 性能优化和缓存策略
3. 部署配置和文档编写

## 七、技术实现细节

### 7.1 JWT认证服务实现

#### JWT配置 (app/core/auth.py)
```python
import os
import jwt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any

JWT_CONFIG = {
    'algorithm': 'HS256',
    'access_token_expire_minutes': 30,
    'refresh_token_expire_days': 7,
    'secret_key': os.getenv('JWT_SECRET_KEY', 'your-secret-key'),
    'issuer': 'pan-so-backend',
}

class JWTService:
    @staticmethod
    def create_access_token(data: Dict[str, Any]) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(minutes=JWT_CONFIG['access_token_expire_minutes'])
        to_encode.update({
            'exp': expire,
            'iat': datetime.utcnow(),
            'iss': JWT_CONFIG['issuer'],
            'type': 'access'
        })
        return jwt.encode(to_encode, JWT_CONFIG['secret_key'], algorithm=JWT_CONFIG['algorithm'])

    @staticmethod
    def create_refresh_token(data: Dict[str, Any]) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=JWT_CONFIG['refresh_token_expire_days'])
        to_encode.update({
            'exp': expire,
            'iat': datetime.utcnow(),
            'iss': JWT_CONFIG['issuer'],
            'type': 'refresh'
        })
        return jwt.encode(to_encode, JWT_CONFIG['secret_key'], algorithm=JWT_CONFIG['algorithm'])

    @staticmethod
    def verify_token(token: str) -> Optional[Dict[str, Any]]:
        """验证令牌"""
        try:
            payload = jwt.decode(
                token,
                JWT_CONFIG['secret_key'],
                algorithms=[JWT_CONFIG['algorithm']],
                issuer=JWT_CONFIG['issuer']
            )
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None
```

#### 权限装饰器 (app/core/permissions.py)
```python
from functools import wraps
from fastapi import HTTPException, status, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from app.models.user import User
from app.core.auth import JWTService

security = HTTPBearer()

def require_permission(permission: str):
    """权限验证装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取current_user
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未认证用户"
                )

            # 检查用户权限
            user_permissions = await get_user_permissions(current_user.id)
            if permission not in user_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="权限不足"
                )

            return await func(*args, **kwargs)
        return wrapper
    return decorator

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """获取当前用户"""
    token = credentials.credentials
    payload = JWTService.verify_token(token)

    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌"
        )

    user_id = payload.get('user_id')
    user = await User.get_or_none(id=user_id, status='active')

    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在或已被禁用"
        )

    return user

async def get_user_permissions(user_id: int) -> list:
    """获取用户权限列表"""
    user = await User.get(id=user_id).prefetch_related('role')
    return user.role.permissions if user.role else []
```

### 7.2 邮箱服务实现

#### 邮箱配置 (app/core/email.py)
```python
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from jinja2 import Environment, FileSystemLoader
from app.utils.config import settings

class EmailService:
    def __init__(self):
        self.smtp_server = settings.get('email.smtp_server')
        self.smtp_port = settings.get('email.smtp_port', 587)
        self.username = settings.get('email.username')
        self.password = settings.get('email.password')
        self.from_email = settings.get('email.from_email')

        # 初始化模板引擎
        self.template_env = Environment(
            loader=FileSystemLoader('app/templates/email')
        )

    async def send_verification_email(self, to_email: str, username: str, verify_token: str):
        """发送邮箱验证邮件"""
        template = self.template_env.get_template('verification.html')
        verify_url = f"{settings.get('app.frontend_url')}/verify-email?token={verify_token}"

        html_content = template.render(
            username=username,
            verify_url=verify_url,
            app_name=settings.get('app.name', 'Pan-So')
        )

        await self._send_email(
            to_email=to_email,
            subject='邮箱验证 - Pan-So',
            html_content=html_content
        )

    async def send_password_reset_email(self, to_email: str, username: str, reset_token: str):
        """发送密码重置邮件"""
        template = self.template_env.get_template('password_reset.html')
        reset_url = f"{settings.get('app.frontend_url')}/reset-password?token={reset_token}"

        html_content = template.render(
            username=username,
            reset_url=reset_url,
            app_name=settings.get('app.name', 'Pan-So')
        )

        await self._send_email(
            to_email=to_email,
            subject='密码重置 - Pan-So',
            html_content=html_content
        )

    async def _send_email(self, to_email: str, subject: str, html_content: str):
        """发送邮件的通用方法"""
        msg = MIMEMultipart('alternative')
        msg['Subject'] = subject
        msg['From'] = self.from_email
        msg['To'] = to_email

        html_part = MIMEText(html_content, 'html', 'utf-8')
        msg.attach(html_part)

        try:
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)
        except Exception as e:
            raise Exception(f"邮件发送失败: {str(e)}")

# 全局邮箱服务实例
email_service = EmailService()
```

### 7.3 配置文件扩展

#### 邮箱和认证配置 (app/config.yaml)
```yaml
# 应用程序基本信息配置
app:
  name: pan-so-backend
  version: 4.0.0
  debug: false
  frontend_url: "http://localhost:3000"  # 前端应用URL

# JWT认证配置
auth:
  jwt_secret_key: "${JWT_SECRET_KEY}"  # 从环境变量获取
  access_token_expire_minutes: 30
  refresh_token_expire_days: 7
  password_reset_expire_hours: 24
  email_verify_expire_hours: 24
  max_login_attempts: 5
  lockout_duration_minutes: 30

# 邮箱服务配置
email:
  smtp_server: "smtp.gmail.com"
  smtp_port: 587
  username: "${EMAIL_USERNAME}"  # 从环境变量获取
  password: "${EMAIL_PASSWORD}"  # 从环境变量获取
  from_email: "<EMAIL>"
  from_name: "Pan-So Team"

# 安全配置
security:
  password_min_length: 8
  password_require_uppercase: true
  password_require_lowercase: true
  password_require_numbers: true
  password_require_symbols: false
  session_timeout_minutes: 30
  max_sessions_per_user: 5

# 用户注册配置
registration:
  enabled: true
  require_email_verification: true
  default_role: "user"
  auto_approve: false  # 是否需要管理员审核
```

## 八、安全考虑

### 8.1 输入验证
- 所有用户输入进行严格验证和过滤
- 防止SQL注入、XSS攻击
- 文件上传安全检查
- 使用Pydantic模型进行数据验证

### 8.2 会话管理
- JWT Token 定期轮换
- 支持强制下线功能
- 异常登录检测和通知
- 限制每用户最大会话数

### 8.3 数据保护
- 敏感数据加密存储
- 操作日志记录
- 数据备份和恢复机制
- HTTPS强制使用

### 8.4 防护措施
- 登录失败锁定机制
- 验证码防暴力破解
- IP白名单和黑名单
- 请求频率限制

## 九、部署配置

### 9.1 环境变量配置
```bash
# .env 文件
JWT_SECRET_KEY=your-super-secret-jwt-key-here
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-email-app-password
DATABASE_URL=postgres://user:password@localhost:5432/pan_so_db
REDIS_URL=redis://localhost:6379/0
```

### 9.2 Docker配置
```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 9999

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "9999"]
```

### 9.3 Nginx配置
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location /api/ {
        proxy_pass http://localhost:9999;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location / {
        root /var/www/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
}
```

---

*本设计方案将作为基础认证和权限系统开发的指导性文件，具体实施过程中可根据实际情况进行调整优化。*
