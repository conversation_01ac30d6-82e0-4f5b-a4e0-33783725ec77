<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>邮箱验证 - {{ app_name }}</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            line-height: 1.6; 
            color: #333; 
            margin: 0; 
            padding: 0; 
            background-color: #f4f4f4; 
        }
        .container { 
            max-width: 600px; 
            margin: 20px auto; 
            background: white; 
            border-radius: 8px; 
            overflow: hidden; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
        }
        .header { 
            background: linear-gradient(135deg, #007bff, #0056b3); 
            color: white; 
            padding: 30px 20px; 
            text-align: center; 
        }
        .header h1 { 
            margin: 0; 
            font-size: 28px; 
            font-weight: 300; 
        }
        .content { 
            padding: 40px 30px; 
            background: #fff; 
        }
        .content h2 { 
            color: #333; 
            margin-top: 0; 
            font-size: 24px; 
        }
        .content p { 
            margin: 16px 0; 
            font-size: 16px; 
            line-height: 1.6; 
        }
        .button { 
            display: inline-block; 
            padding: 15px 30px; 
            background: linear-gradient(135deg, #007bff, #0056b3); 
            color: white; 
            text-decoration: none; 
            border-radius: 6px; 
            font-weight: 500; 
            font-size: 16px; 
            margin: 20px 0; 
            transition: all 0.3s ease; 
        }
        .button:hover { 
            background: linear-gradient(135deg, #0056b3, #004085); 
            transform: translateY(-2px); 
        }
        .footer { 
            padding: 30px; 
            text-align: center; 
            color: #666; 
            font-size: 14px; 
            background: #f8f9fa; 
            border-top: 1px solid #e9ecef; 
        }
        .footer p { 
            margin: 8px 0; 
        }
        .divider { 
            height: 1px; 
            background: linear-gradient(to right, transparent, #ddd, transparent); 
            margin: 30px 0; 
        }
        .highlight { 
            background: #f8f9fa; 
            padding: 20px; 
            border-left: 4px solid #007bff; 
            margin: 20px 0; 
            border-radius: 0 4px 4px 0; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ app_name }}</h1>
        </div>
        <div class="content">
            <h2>欢迎注册 {{ app_name }}！</h2>
            <p>亲爱的 <strong>{{ username }}</strong>，</p>
            <p>感谢您注册 {{ app_name }}！为了确保您的账户安全，请点击下面的按钮验证您的邮箱地址：</p>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ verify_url }}" class="button">验证邮箱地址</a>
            </div>
            
            <div class="highlight">
                <p><strong>重要提示：</strong></p>
                <ul>
                    <li>此验证链接将在 24 小时后过期</li>
                    <li>如果按钮无法点击，请复制以下链接到浏览器地址栏：</li>
                </ul>
                <p style="word-break: break-all; color: #007bff; font-family: monospace; font-size: 14px;">
                    {{ verify_url }}
                </p>
            </div>
            
            <div class="divider"></div>
            
            <p>如果您没有注册 {{ app_name }} 账户，请忽略此邮件。</p>
            <p>如有任何问题，请联系我们的客服团队。</p>
        </div>
        <div class="footer">
            <p>此邮件由 {{ app_name }} 系统自动发送，请勿直接回复。</p>
            <p>&copy; 2025 {{ app_name }}. 保留所有权利。</p>
        </div>
    </div>
</body>
</html>
