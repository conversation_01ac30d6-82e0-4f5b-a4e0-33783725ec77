import logging
import jieba
from tortoise.expressions import Q
from app.models.resource import PanResource
from app.utils.common import get_normalized_file_type, get_time_filter_range
from app.models.enums import TimeFilter
from typing import Union
from datetime import datetime, timezone
from functools import reduce
from operator import or_
from app.utils.relevance_ranking import calculate_relevance_scores, sort_by_relevance
from app.services.meilisearch_service import meilisearch_service

logger = logging.getLogger("pan-service")


class LocalSearchService:
    def _extract_search_keywords(self, keyword: str):
        """从搜索词提取关键词和权重"""
        stopwords = {"的", "了", "和", "与", "或", "等", "是", "在", "以", "及"}
        processed_keywords = []
        keyword_weights = {}

        # 1. 添加完整的原始关键词，这是最高优先级的
        if len(keyword) >= 2:
            processed_keywords.append(keyword)
            keyword_weights[keyword] = 10.0

        # 2. 添加分词后的关键词
        keywords_list = jieba.cut_for_search(keyword)
        for word in keywords_list:
            if word in stopwords:
                continue
            if len(word) >= 2:
                if word not in processed_keywords:  # 避免重复
                    processed_keywords.append(word)
                    keyword_weights[word] = min(1.0 + len(word) * 0.5, 5.0)

        # 3. 兜底策略，如果没有任何可搜索的词，则使用原始关键词
        if not processed_keywords and keyword:
            processed_keywords.append(keyword)
            # 如果是单个字符，也加入
            if len(keyword) < 2:
                keyword_weights[keyword] = 1.0

        return {
            "keywords": processed_keywords,
            "keyword_weights": keyword_weights,
        }

    async def search_local(
        self,
        keyword: str,
        pan_type: int = None,
        file_type: str = None,
        exact: bool = False,
        sort_by: str = "relevance",
        sort_order: str = "desc",
        valid_only: bool = False,
        user: str = None,
        limit: int = 2000,
        time_filter: Union[str, TimeFilter] = "all",
    ):
        """
        完全依赖Meilisearch进行资源检索，
        然后使用系统内权重打分系统对结果进行排序。
        """
        # --- 调试：打印所有实际用到的参数 ---
        logger.info(
            f"[DEBUG] 查询参数: keyword={keyword}, pan_type={pan_type}, file_type={file_type}, exact={exact}, sort_by={sort_by}, sort_order={sort_order}, valid_only={valid_only}, user={user}, time_filter={time_filter}"
        )

        if not keyword:
            # 允许无关键词检索，直接用 filter 查最近的资源
            try:
                meili_filters = []
                if pan_type and pan_type != 0:
                    meili_filters.append(f"pan_type = {pan_type}")

                if file_type:
                    normalized_type = get_normalized_file_type(file_type)
                    if normalized_type != "other":
                        meili_filters.append(f"file_type = '{normalized_type}'")

                if valid_only:
                    meili_filters.append("verified_status = 'valid'")

                # 只在 user 有值时加 author 过滤条件
                if user:
                    meili_filters.append(f"author = '{user}'")

                # 添加时间过滤条件
                time_filter_str = (
                    time_filter.value
                    if isinstance(time_filter, TimeFilter)
                    else time_filter
                )
                if time_filter_str != "all":
                    start_time, end_time = get_time_filter_range(time_filter_str)
                    if start_time and end_time:
                        # 转换为UTC时区以匹配Meilisearch中存储的UTC时间格式
                        start_utc = start_time.astimezone(timezone.utc)
                        end_utc = end_time.astimezone(timezone.utc)
                        # 去除微秒精度以避免查询匹配问题
                        start_iso = start_utc.replace(microsecond=0).isoformat()
                        end_iso = end_utc.replace(microsecond=0).isoformat()
                        meili_filters.append(
                            f"updated_at >= '{start_iso}' AND updated_at <= '{end_iso}'"
                        )

                search_params = {"limit": min(limit, 2000)}
                if meili_filters:
                    search_params["filter"] = " AND ".join(meili_filters)
                # 排序
                if sort_by:
                    search_params["sort"] = [f"{sort_by}:{sort_order}"]

                logger.info(f"[DEBUG] meili_filters: {meili_filters}")
                logger.info(f"[DEBUG] search_params: {search_params}")

                meili_results = meilisearch_service.index.search("", search_params)
                results_list = meili_results.get("hits", [])
                search_info = meili_results
            except Exception as e:
                logger.error(f"Meilisearch 检索异常: {e}", exc_info=True)
                results_list = []
                search_info = {"error": str(e)}

            # 直接返回
            return {
                "is_local_search": False,
                "results": results_list,
                "totals": search_info.get("estimatedTotalHits", len(results_list)),
                "search_info": search_info,
            }

        results_list = []
        search_info = {}
        search_term = f'"{keyword}"' if exact else keyword

        try:
            # 1. 构建Meilisearch的过滤和搜索参数
            meili_filters = []
            if pan_type and pan_type != 0:
                meili_filters.append(f"pan_type = {pan_type}")

            if file_type:
                normalized_type = get_normalized_file_type(file_type)
                if normalized_type != "other":
                    meili_filters.append(f"file_type = '{normalized_type}'")

            if valid_only:
                meili_filters.append("verified_status = 'valid'")

            if user is not None:
                # 兼容空字符串查匿名分享和按用户名查找
                meili_filters.append(
                    "author NOT EXISTS" if user == "" else f"author = '{user}'"
                )

            # 添加时间过滤条件
            time_filter_str = (
                time_filter.value
                if isinstance(time_filter, TimeFilter)
                else time_filter
            )
            if time_filter_str != "all":
                start_time, end_time = get_time_filter_range(time_filter_str)
                if start_time and end_time:
                    # 转换为UTC时区以匹配Meilisearch中存储的UTC时间格式
                    start_utc = start_time.astimezone(timezone.utc)
                    end_utc = end_time.astimezone(timezone.utc)
                    # 去除微秒精度以避免查询匹配问题
                    start_iso = start_utc.replace(microsecond=0).isoformat()
                    end_iso = end_utc.replace(microsecond=0).isoformat()
                    meili_filters.append(
                        f"updated_at >= '{start_iso}' AND updated_at <= '{end_iso}'"
                    )

            search_params = {"limit": min(limit, 2000)}
            if meili_filters:
                search_params["filter"] = " AND ".join(meili_filters)

            # 2. 用Meilisearch进行检索
            meili_results = meilisearch_service.index.search(search_term, search_params)
            search_info = meili_results
            results_list = meili_results.get("hits", [])

            logger.info(
                f"[调试] 本次检索数据来源：Meilisearch，命中{len(results_list)}条 (查询: '{search_term}', 参数: {search_params})"
            )

        except Exception as e:
            logger.error(f"Meilisearch 检索异常: {e}", exc_info=True)
            # 优雅地失败，返回空列表
            results_list = []
            search_info = {"error": str(e)}

        # 3. 提取关键词用于后续打分
        search_meta = self._extract_search_keywords(keyword)

        # 4. 权重打分与排序
        scored_results = calculate_relevance_scores(
            results_list,
            search_term=keyword,
            keywords=search_meta["keywords"],
            keyword_weights=search_meta["keyword_weights"],
        )
        final_results = sort_by_relevance(scored_results, sort_by, sort_order)

        # 为所有本地结果添加来源标记
        for res in final_results:
            res["is_local"] = True

        logger.info(f"本地搜索完成: 找到 {len(final_results)} 条结果")

        return {
            "is_local_search": False,
            "results": final_results,
            "totals": search_info.get("estimatedTotalHits", len(final_results)),
            "search_info": search_info,
        }


local_search_service = LocalSearchService()
