# Telegram爬虫配置示例 - 包含消息遗漏处理机制
telegram_crawler:
  # 基本配置
  api_id: "your_api_id"
  api_hash: "your_api_hash"
  session_name: "telegram_session"
  
  # 监控的频道列表
  channels:
    - "@channel_username1"
    - "@channel_username2"
    - "https://t.me/channel_username3"
    - "-1001234567890"  # 频道ID
  
  # 消息遗漏处理配置
  enable_catch_up: true                    # 是否启用catch_up机制
  periodic_check_interval: 300             # 定期检查间隔(秒)，默认5分钟
  connection_check_interval: 30            # 连接检查间隔(秒)，默认30秒
  max_gap_recovery_attempts: 3             # 最大间隙恢复尝试次数
  
  # 代理配置（可选）
  proxy:
    enabled: false
    type: "socks5"  # socks5, http, mtproxy
    host: "127.0.0.1"
    port: 1080
    username: ""  # 可选
    password: ""  # 可选
    secret: ""    # MTProto代理专用
