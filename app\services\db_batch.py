import asyncio
import logging
from typing import List, Dict, Any
from datetime import datetime, timezone, timedelta

from app.models.resource import PanResource

logger = logging.getLogger(__name__)

# 定义北京时间时区（UTC+8）
BEIJING_TIMEZONE = timezone(timedelta(hours=8))

# 数据库批量操作队列，使用asyncio.Queue实现并发安全
db_queue = asyncio.Queue()
db_queue_max_size = 30  # 队列大小，减少数据库写入频率


async def batch_save_to_db(force: bool = False):
    """
    批量保存资源到数据库，如果资源已存在但数据不一致，则更新。
    Args:
        force: 是否强制执行批量保存，即使队列未满
    """
    items_from_queue = []
    try:
        current_batch_size = 0
        # 从队列中获取项目
        # 如果强制执行，则获取所有项目；否则，获取最多 db_queue_max_size 个项目
        while not db_queue.empty():
            if not force and current_batch_size >= db_queue_max_size:
                break
            items_from_queue.append(await db_queue.get())
            current_batch_size += 1

    except Exception as e:
        logger.error(f"从队列获取数据失败: {str(e)}")
        # 如果获取失败，将已取出的项目放回队列以备后续处理
        for item_to_requeue in reversed(items_from_queue):
            await db_queue.put(item_to_requeue)
        return

    if not items_from_queue:
        return

    logger.info(f"批量处理数据库操作，从队列中取出: {len(items_from_queue)} 条数据")

    try:
        # 1. 聚合具有相同 resource_key 的项目，后来的项目会覆盖同一批次中较早的项目
        unique_item_data_map = {}  # {resource_key: item_dict_from_queue}
        for item in items_from_queue:
            resource_key = item.get("resource_key")
            if resource_key:
                unique_item_data_map[resource_key] = item
            else:
                logger.warning(f"项目缺少resource_key，已跳过: {item}")

        if not unique_item_data_map:
            logger.info("没有有效的资源数据进行处理。")
            return

        all_keys_to_process = list(unique_item_data_map.keys())

        # 2. 根据这些 key 从数据库中获取已存在的记录
        existing_db_records_map = {}  # {resource_key: PanResource_instance}
        if all_keys_to_process:
            try:
                db_records = await PanResource.filter(
                    resource_key__in=all_keys_to_process
                )
                for rec in db_records:
                    existing_db_records_map[rec.resource_key] = rec
                logger.info(
                    f"查询到 {len(existing_db_records_map)} 条匹配待处理key的已存在数据库记录"
                )
            except Exception as e:
                logger.error(f"批量查询已存在资源失败: {str(e)}")
                # 关键错误，将项目重新入队并返回，以避免部分处理
                for item_to_requeue in items_from_queue:
                    await db_queue.put(item_to_requeue)
                # 可以选择性地重新引发错误或直接返回，具体取决于工作进程所需的重试策略
                return

        resources_to_create = []
        resources_to_update = []  # 需要更新的 PanResource 对象列表

        # 3. 遍历去重后的项目，决定创建或更新
        for resource_key, item_data in unique_item_data_map.items():
            # item_data中的时间戳来自 search.py 中的 create_db_item, 已经是 datetime 对象
            source_publish_time = ensure_aware(item_data.get("updated_at"))
            crawler_process_time = ensure_aware(item_data.get("created_at"))

            db_resource = existing_db_records_map.get(resource_key)

            if db_resource:  # 资源已存在: 比较并更新
                changed_data_fields = False

                fields_to_check_and_update = {
                    "pan_type": item_data.get("pan_type"),
                    "original_url": item_data.get("original_url"),
                    "title": item_data.get("title"),
                    "file_type": item_data.get("file_type"),
                    "file_size": item_data.get("file_size"),
                    "share_url": item_data.get("share_url"),
                    "text_content": item_data.get("text_content"),  # 此字段之前已添加
                }

                for field_name, new_value in fields_to_check_and_update.items():
                    # 仅当 new_value存在 (不是None) 且与当前值不同时才更新
                    current_value = getattr(db_resource, field_name, None)
                    if new_value is not None and new_value != current_value:
                        setattr(db_resource, field_name, new_value)
                        changed_data_fields = True

                if changed_data_fields:
                    resources_to_update.append(db_resource)

            else:  # 新资源: 创建
                # 对于新记录, created_at 是爬虫处理它的时间
                # updated_at 是源的发布时间，如果不可用，则与 created_at 相同
                record_created_at = crawler_process_time or datetime.now(
                    BEIJING_TIMEZONE
                )
                record_updated_at = source_publish_time or record_created_at

                try:
                    new_pan_resource = PanResource(
                        resource_key=resource_key,
                        pan_type=item_data.get("pan_type"),
                        original_url=item_data.get("original_url", ""),
                        title=item_data.get("title"),
                        file_type=item_data.get("file_type"),
                        file_size=item_data.get("file_size"),
                        text_content=item_data.get("text_content"),
                        created_at=record_created_at,
                        updated_at=record_updated_at,
                    )
                    resources_to_create.append(new_pan_resource)
                except Exception as e:
                    logger.error(
                        f"创建PanResource对象失败 {resource_key}: {str(e)}, item_data: {item_data}"
                    )

        # 4. 执行数据库操作
        if resources_to_create:
            try:
                await PanResource.bulk_create(resources_to_create)
                logger.info(f"成功批量创建 {len(resources_to_create)} 条新资源记录。")
            except Exception as e:
                logger.error(f"批量创建新记录失败: {str(e)}")
                # 可以考虑只重新排队那些属于 resources_to_create 的项目
                # 为简单起见，这里我们不重新排队失败的子批次。

        if resources_to_update:
            updated_count = 0
            for res_to_save in resources_to_update:
                try:
                    await res_to_save.save()  # Tortoise ORM的 save() 处理更新
                    updated_count += 1
                except Exception as e:
                    logger.error(f"更新资源 {res_to_save.resource_key} 失败: {str(e)}")
            if updated_count > 0:
                logger.info(f"成功更新 {updated_count} 条资源记录。")

    except Exception as e:
        logger.error(f"批量保存到数据库主逻辑发生严重错误: {str(e)}")
        # 如果在单个项目处理之前发生主要错误，则重新排队开始时获取的所有项目
        # 这是一个通用的捕获机制；特定的错误理想情况下应更有选择性地重新排队。
        if items_from_queue:
            logger.info(
                f"由于处理中发生错误，将 {len(items_from_queue)} 条项目重新加入队列。"
            )
            for item_to_requeue in items_from_queue:
                await db_queue.put(item_to_requeue)


def ensure_aware(dt):
    """确保 datetime 对象为 aware（带有 UTC 时区）"""
    if dt is None:
        return None
    if dt.tzinfo is None:
        return dt.replace(tzinfo=timezone.utc)
    return dt


def parse_datetime(date_string):
    """解析各种格式的日期时间字符串，返回带时区的datetime（UTC）"""
    if not date_string or not isinstance(date_string, str):
        return None

    date_formats = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
        "%Y-%m-%d",
        "%Y/%m/%d %H:%M",
        "%Y年%m月%d日",
    ]

    for fmt in date_formats:
        try:
            dt = datetime.strptime(date_string, fmt)
            return dt.replace(tzinfo=timezone.utc)
        except ValueError:
            continue

    return None


async def add_to_db_queue(items):
    """
    支持单条或批量添加项目到数据库队列
    Args:
        items: 要添加的资源项目（dict 或 list[dict]）
    """
    if isinstance(items, dict):
        items = [items]

    for item in items:
        if item and "resource_key" in item:
            try:
                await db_queue.put(item)
            except Exception as e:
                logger.error(f"添加资源到队列失败: {str(e)}")
        else:
            logger.warning(f"尝试添加无效资源到队列: {item}")

    if db_queue.qsize() >= db_queue_max_size:
        logger.info("队列达到阈值，触发批量保存")
        await batch_save_to_db()


async def start_db_batch_worker():
    """启动数据库批处理工作器"""
    logger.info("数据库批处理工作器已启动")
    while True:
        try:
            # 每隔一段时间强制执行一次批量保存
            await asyncio.sleep(60)  # 每分钟执行一次
            await batch_save_to_db(force=True)
        except Exception as e:
            logger.error(f"数据库批处理工作器发生错误: {str(e)}")
            await asyncio.sleep(5)  # 出错后暂停再继续
