import re
from typing import Dict, Optional, Pattern
from app.models.enums import PanType  # Assuming PanType enum is here

# Regex patterns for different pan services (examples, needs refinement)
# These patterns are basic and might need to be more comprehensive
# and handle various URL formats (e.g., with/without 's/', different domains)
BAIDU_PATTERN = re.compile(
    r"pan\.baidu\.com/s/1([a-zA-Z0-9_-]{22,23})(?:.*?pwd=([a-zA-Z0-9]{4}))?|(?:baidu\.com/s/1|eyun\.baidu\.com/s/1)([a-zA-Z0-9_-]{8,})"
)
QUARK_PATTERN = re.compile(r"pan\.quark\.cn/s/([a-zA-Z0-9]+)(?:.*?pwd=([a-zA-Z0-9]+))?")
XUNLEI_PATTERN = re.compile(
    r"pan\.xunlei\.com/s/([a-zA-Z0-9_\-]+)(?:.*?pwd=([a-zA-Z0-9]+))?#?([a-zA-Z0-9_\-]+)?"
)  # Xunlei might have path-based keys too
ALIYUN_PATTERN = re.compile(
    r"(?:www\.)?(?:aliyundrive\.com|alipan\.com)/s/([a-zA-Z0-9]+)(?:.*?pwd=([a-zA-Z0-9]+))?"
)

# More specific patterns can be added, e.g. for short links or different domain aliases

SUPPORTED_PAN_PARSERS: Dict[PanType, Pattern] = {
    PanType.BAIDU: BAIDU_PATTERN,
    PanType.QUARK: QUARK_PATTERN,
    PanType.XUNLEI: XUNLEI_PATTERN,
    PanType.ALIYUN: ALIYUN_PATTERN,  # Assuming Aliyun follows a similar pattern
}


def parse_pan_url(url: str) -> Optional[Dict[str, any]]:
    """
    解析网盘分享链接，提取网盘类型、资源key和提取码。

    参数:
        url: 网盘分享链接字符串。

    返回:
        包含 'pan_type'（PanType 枚举）、'resource_key' 和可选的 'share_pwd'（提取码）的字典。
        如果无法识别或解析该URL，则返回 None。
    """
    if not url or not isinstance(url, str):
        return None

    for pan_type, pattern in SUPPORTED_PAN_PARSERS.items():
        match = pattern.search(url)
        if match:
            groups = match.groups()
            resource_key = None
            share_pwd = None

            if pan_type == PanType.BAIDU:
                # Baidu has multiple capturing groups in the example regex
                if groups[0]:  # s/1xxxxxxxxxxxxxxxxxxxxxxx
                    resource_key = "1" + groups[0]
                    if len(groups) > 1 and groups[1]:
                        share_pwd = groups[1]
                elif groups[2]:  # s/1xxxxxxx or eyun.baidu.com/s/1xxxxxxx
                    resource_key = (
                        "1" + groups[2]
                    )  # typically short form, may need prefix '1'
            elif pan_type == PanType.XUNLEI:
                resource_key = (
                    groups[0] or groups[2]
                )  # key might be in first or third group depending on format
                if len(groups) > 1 and groups[1]:
                    share_pwd = groups[1]
            else:  # Quark, Aliyun (assuming similar structure from example regex)
                resource_key = groups[0]
                if len(groups) > 1 and groups[1]:
                    share_pwd = groups[1]

            if resource_key:
                return {
                    "pan_type_enum": pan_type,
                    "pan_type_int": pan_type.value,
                    "resource_key": resource_key,
                    "share_pwd": share_pwd if share_pwd else None,
                }
    return None


# Example Usage (for testing purposes):
if __name__ == "__main__":
    test_urls = [
        "https://pan.baidu.com/s/1s4nlIYDIK1x0Unor0yj8Vw?pwd=wqKD",
        "https://pan.baidu.com/s/1abc123def456ghi789jkl0",
        "http://pan.quark.cn/s/fedcba9876543210?pwd=xyzw",
        "http://pan.quark.cn/s/fedcba9876543210",
        "https://pan.xunlei.com/s/abcdefghijklmnopqrstuvwxyz123456?pwd=test",
        "https://pan.xunlei.com/s/VNmsdlDS Adsflkald#提取码：test",  # Example with Chinese characters in URL path
        "https://www.aliyundrive.com/s/xyz789abc?pwd=pass",
        "https://www.aliyundrive.com/s/xyz789abc",
        "https://www.alipan.com/s/FsfgHhmzJ8P",
        "invalid_url",
        "http://example.com/notapanlink",
    ]
    for test_url in test_urls:
        parsed_info = parse_pan_url(test_url)
        if parsed_info:
            print(
                f"URL: {test_url}\n  Type: {parsed_info['pan_type_enum'].name}, Key: {parsed_info['resource_key']}, Pwd: {parsed_info['share_pwd']}\n  pan_type_int: {parsed_info['pan_type_int']}"
            )
        else:
            print(f"URL: {test_url}\n  Failed to parse.\n")
