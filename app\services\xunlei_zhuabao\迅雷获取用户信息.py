import requests

url = "https://xluser-ssl.xunlei.com/v1/user/me"

payload={}
headers = {
   'authorization': 'Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
   'x-captcha-token': 'ck0.CDVO81Qgn6sA12c1ya3X5IQeZg8t49Com6obTYu0hdYjXW3ZIsgUIQEzoTQ6PwWd8dsXMQykHZoIb0OvcMSYJVCLIo8tVC4SapFGUYJn4d17emf2rG4TQBhYvg2EH9lEr87ACsEI72jCjEPSRtQum3Ld2OE3UAGU39JO6wJu1e8VDRRLKkL0IYKTSVfS0cWbbCjaqbfUU0hcJE4P08-Idiz4TbQ_oVmOYSL85seGsSEHf8IpVbRWHRx5YNMusb6215yi82sjisfkB2TRpRH3KCJIoN4m5Ri8YoAc_7X_Tx3Y8PSBRmgylKIpcgoF_U_bMKyP93gIWkOSzezrNyHT6hInnsLHjYuEWANnwV43fioaBgsZp4A2G9-x6jcDlmLoPxS46p5O2VapluqYoCLAJNSTBq3ew_DZnNiu55whPIo.ClQI7O7O9O0yEhBYcXAwa0pCWFdod2FUcEI2GgcxLjkxLjIwIg5wYW4ueHVubGVpLmNvbSogODI4ODQ3OWM0ZmI5ZWJlZDA5YWQ2YmI4ZTI2MDVjYWUSgAGhOLvRlVvYXXID5qTGfvu1f78A8rEkP45pR78xn-DoQ1j-VdNHbQTG70sXnjf9Ju4uz12F5z3EZL88o5IpqI-cTZq6E7rS0PYI9hRakiWEUsw7fRkW5xFR2uziBlmkivvuglW1U_2zEZHhK4AvSjsahtbYpzYMV9KmUe7Kwwauyw',
   'x-client-id': 'Xqp0kJBXWhwaTpB6',
   'x-device-id': '8288479c4fb9ebed09ad6bb8e2605cae',
   'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
   'Accept': '*/*',
   'Host': 'xluser-ssl.xunlei.com',
   'Connection': 'keep-alive'
}

response = requests.request("GET", url, headers=headers, data=payload)

print(response.text)



"""响应
{
    "sub": "630012855",
    "name": "十八",
    "picture": "https://xfile2.a.88cdn.com/file/k/Fo3U4kr6TBIupg4KAhOfUTTJHAJz",
    "phone_number": "+86 132***659",
    "providers": [
        {
            "id": "u",
            "provider_user_id": "679040034"
        },
        {
            "id": "qq.com",
            "provider_user_id": "UID_3C6D518AE6AD6A53BEA16FC32FCED1EA"
        }
    ],
    "status": "ACTIVE",
    "gender": "male",
    "group": [
        {
            "id": "vip_0_0_3",
            "expires_at": "2019-09-12T16:00:00Z"
        }
    ],
    "meta": {
        "idcardno_status": "0",
        "truename_status": "0"
    },
    "created_at": "2017-02-06T02:46:53Z",
    "password_updated_at": "2017-02-06T02:46:54Z",
    "id": "630012855",
    "vips": [
        {
            "id": "vip2_0_0_3_2_0",
            "expires_at": "2019-09-12T16:00:00Z"
        }
    ],
    "vip_info": [
        {
            "register": "20190910",
            "autodeduct": "0",
            "daily": "-10",
            "expire": "20190913",
            "grow": "0",
            "is_vip": "0",
            "last_pay": "**********",
            "level": "0",
            "pay_id": "0",
            "remind": "0",
            "is_year": "0",
            "user_vas": "2",
            "vas_type": "3",
            "vip_icon": {
                "general": "https://xluser-ssl.xunlei.com/v1/file/icon/level/vip/deactivate_a/vip_level1_deactivate.png",
                "small": "https://xluser-ssl.xunlei.com/v1/file/icon/level/vip/deactivate_b/vip_lever1_deactivate.png"
            },
            "expire_time": "2019-09-13T23:45:20+08:00"
        },
        {
            "register": "19700101",
            "autodeduct": "0",
            "daily": "0",
            "expire": "0",
            "grow": "0",
            "is_vip": "0",
            "last_pay": "0",
            "level": "0",
            "pay_id": "0",
            "remind": "0",
            "is_year": "0",
            "user_vas": "306",
            "vas_type": "0",
            "vip_icon": {}
        }
    ]
}
"""