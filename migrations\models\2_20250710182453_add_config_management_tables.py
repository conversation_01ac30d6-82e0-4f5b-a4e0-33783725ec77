from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "config_backups" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "config_content" JSONB NOT NULL,
    "file_hash" VARCHAR(64) NOT NULL,
    "comment" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "created_by_id" INT NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "config_backups"."config_content" IS '配置内容';
COMMENT ON COLUMN "config_backups"."file_hash" IS '配置文件哈希值';
COMMENT ON COLUMN "config_backups"."comment" IS '备份说明';
COMMENT ON COLUMN "config_backups"."created_at" IS '创建时间';
COMMENT ON COLUMN "config_backups"."created_by_id" IS '创建者';
COMMENT ON TABLE "config_backups" IS '配置备份表';
        CREATE TABLE IF NOT EXISTS "config_change_logs" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "action" VARCHAR(50) NOT NULL,
    "config_key" VARCHAR(255),
    "old_value" JSONB,
    "new_value" JSONB,
    "comment" TEXT,
    "ip_address" VARCHAR(45),
    "user_agent" TEXT,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "user_id" INT NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE
);
COMMENT ON COLUMN "config_change_logs"."action" IS '操作类型';
COMMENT ON COLUMN "config_change_logs"."config_key" IS '配置键';
COMMENT ON COLUMN "config_change_logs"."old_value" IS '旧值';
COMMENT ON COLUMN "config_change_logs"."new_value" IS '新值';
COMMENT ON COLUMN "config_change_logs"."comment" IS '变更说明';
COMMENT ON COLUMN "config_change_logs"."ip_address" IS 'IP地址';
COMMENT ON COLUMN "config_change_logs"."user_agent" IS '用户代理';
COMMENT ON COLUMN "config_change_logs"."created_at" IS '创建时间';
COMMENT ON COLUMN "config_change_logs"."user_id" IS '操作用户';
COMMENT ON TABLE "config_change_logs" IS '配置变更日志表';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP TABLE IF EXISTS "config_backups";
        DROP TABLE IF EXISTS "config_change_logs";"""
