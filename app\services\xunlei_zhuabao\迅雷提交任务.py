'''
Author: dkon <EMAIL>
Date: 2025-05-17 22:51:15
LastEditors: dkon <EMAIL>
LastEditTime: 2025-05-17 22:51:47
FilePath: \pan-so-backend\app\services\迅雷提交任务.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
import http.client

conn = http.client.HTTPSConnection("api-pan.xunlei.com")
payload = ''
headers = {
   'authorization': 'Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
   'x-captcha-token': 'ck0.8WMx_F7PXDk4BIHdNDJ6mCUrwqrz_nvN5PsGBsUQXP3AsN29pP8b4zLsTs5PBPgTH489Igc5dkbS0b40kLqPZtNB2gaPn9-f4qA_kD2ce1GxsiN3kN9opt5uvaST816TJo4DILX8Gxagjnvd6xmmeVrLUF8EbPTfgnFIKvdqdjFVvbKKW0LTSXhoCsGYAQ3FnFKMybYL6Iz8OGh4seu0-dD3tb-aMehXmdZwr30uozj7gidAhoiKBBLpitE5-K43PyMyY_3OZF2fL68HC057CHZRBwvP0e91g2lrhuRcnpWc4XUtAHFdookp9OeqKOpwycD-uiCOnk1oVC1xYPcSWZdFThbo9mHSNtpvLlEz2506czciZ8TJMMoNeGWQg_davjkM4ZhpOmraHC1k7PuXDSXOnp_Lv_k0Ke5z30qaguA.ClQIl6P87u0yEhBYcXAwa0pCWFdod2FUcEI2GgcxLjkxLjIwIg5wYW4ueHVubGVpLmNvbSogODI4ODQ3OWM0ZmI5ZWJlZDA5YWQ2YmI4ZTI2MDVjYWUSgAGF83neLeF-91YeSIUFX3csvYgu08vRHyjusn9u2K8GtUThDlSO4M567JDJJWOcO-GAzu3PtVEoNRebOfAXAla8CIDS3OclosTmLRfIFREMCyRT6U1_yN2ip79Ob3yrGjGluxO5cFbAI21-IeP2Ha-qrZA3n44n70lo1GVKxbUO5Q',
   'x-client-id': 'Xqp0kJBXWhwaTpB6',
   'x-device-id': '8288479c4fb9ebed09ad6bb8e2605cae',
   'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
   'Accept': '*/*',
   'Host': 'api-pan.xunlei.com',
   'Connection': 'keep-alive'
}
conn.request("GET", "/drive/v1/tasks/VOQStgjqwJkMnreMhQzfPT3hA1", payload, headers)
res = conn.getresponse()
data = res.read()
print(data.decode("utf-8"))


"""响应
{
    "kind": "drive#task",
    "id": "VOQStgjqwJkMnreMhQzfPT3hA1",
    "name": "restore",
    "type": "restore",
    "user_id": "630012855",
    "statuses": [],
    "status_size": 0,
    "params": {
        "notify_restore_reward": "VOQStgnm5mjP4r4-01c7bsGmA1",
        "notify_restore_skin": "VOQStgnU5mjP4r4-01c7bsGYA1",
        "share_id": "VNztG8KQlOceTFiFQLJDhZ_yA1"
    },
    "file_id": "",
    "file_name": "",
    "file_size": "0",
    "message": "完成",
    "created_time": "2025-05-17T19:00:12.406+08:00",
    "updated_time": "2025-05-17T19:00:12.660+08:00",
    "third_task_id": "",
    "phase": "PHASE_TYPE_COMPLETE",
    "progress": 100,
    "icon_link": "https://backstage-img-ssl.a.88cdn.com/05e4f2d4a751f1895746a15da2d391105418a66d",
    "callback": "",
    "reference_resource": null,
    "space": ""
}
"""