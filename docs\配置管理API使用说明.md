# 配置管理API使用说明

## 概述

配置管理模块提供了完整的系统配置管理功能，包括配置查询、更新、备份、恢复、验证等核心功能。该模块基于 `app/config.yaml` 文件，提供安全、便捷的配置管理界面。

## 功能特性

### 🔧 核心功能
- **配置查询**: 支持分类查询和搜索
- **配置更新**: 单个和批量配置更新
- **配置验证**: 类型和格式验证
- **配置备份**: 自动和手动备份
- **配置恢复**: 从备份恢复配置
- **变更历史**: 完整的操作审计日志

### 🛡️ 安全特性
- **权限控制**: 分级权限管理
- **敏感信息遮蔽**: 自动遮蔽敏感配置
- **操作审计**: 记录所有配置变更
- **备份机制**: 重要变更前自动备份

### ⚡ 高级特性
- **配置分类**: 按功能模块分类管理
- **生效机制**: 支持热更新和重启标记
- **批量操作**: 支持批量配置更新
- **差异对比**: 配置变更差异查看

## API端点

### 配置查询

#### 获取配置列表
```http
GET /api/admin/config
```

**参数:**
- `category` (可选): 配置分类筛选
- `search` (可选): 搜索配置项
- `show_sensitive` (可选): 是否显示敏感信息

**响应:**
```json
{
  "categories": [
    {
      "name": "app",
      "display_name": "应用基础配置",
      "description": "应用程序基本信息配置",
      "icon": "📱",
      "configs": [
        {
          "key": "app.name",
          "display_name": "应用名称",
          "value": "pan-so-backend",
          "type": "string",
          "required": true,
          "sensitive": false,
          "description": "应用程序名称",
          "effect_type": "immediate"
        }
      ]
    }
  ],
  "total_count": 50,
  "restart_required": false
}
```

#### 获取配置详情
```http
GET /api/admin/config/{config_key}
```

### 配置更新

#### 更新单个配置
```http
PUT /api/admin/config/{config_key}
```

**请求体:**
```json
{
  "value": "new_value",
  "comment": "更新原因说明"
}
```

#### 批量更新配置
```http
POST /api/admin/config/batch-update
```

**请求体:**
```json
{
  "updates": [
    {
      "key": "app.debug",
      "value": false,
      "comment": "关闭调试模式"
    }
  ],
  "comment": "批量配置更新"
}
```

### 配置验证

#### 验证配置值
```http
POST /api/admin/config/validate
```

**请求体:**
```json
{
  "key": "api.port",
  "value": 8080
}
```

**响应:**
```json
{
  "valid": true,
  "message": "验证通过",
  "suggestions": []
}
```

### 配置备份

#### 创建备份
```http
POST /api/admin/config/backup
```

**请求体:**
```json
{
  "comment": "手动备份"
}
```

#### 获取备份列表
```http
GET /api/admin/config/backups?page=1&size=20
```

#### 恢复备份
```http
POST /api/admin/config/restore/{backup_id}
```

### 配置历史

#### 获取变更历史
```http
GET /api/admin/config/history?page=1&size=20
```

**参数:**
- `config_key` (可选): 配置键筛选
- `start_date` (可选): 开始日期
- `end_date` (可选): 结束日期

#### 获取变更差异
```http
GET /api/admin/config/diff/{history_id}
```

### 系统状态

#### 获取重启状态
```http
GET /api/admin/config/restart-status
```

**响应:**
```json
{
  "restart_required": true,
  "pending_changes": ["api.port", "database.url"],
  "affected_services": ["api", "celery", "database"],
  "last_restart_time": null
}
```

#### 清除重启标志
```http
POST /api/admin/config/clear-restart-flag
```

## 配置分类

系统将配置项按功能分为以下类别：

| 分类 | 名称 | 描述 |
|------|------|------|
| 📱 app | 应用基础配置 | 应用程序基本信息配置 |
| 🗄️ database | 数据库配置 | 数据库连接和相关配置 |
| 🔐 auth | 认证配置 | JWT认证和安全相关配置 |
| 📧 email | 邮箱服务配置 | SMTP邮箱服务配置 |
| 🛡️ security | 安全配置 | 密码策略和安全设置 |
| 👤 registration | 用户注册配置 | 用户注册相关设置 |
| 📝 logging | 日志配置 | 日志级别和格式配置 |
| 💬 wx_push | 企业微信推送配置 | 企业微信消息推送配置 |
| 🌐 api | API服务配置 | API服务监听和CORS配置 |
| ☁️ pan_service | 网盘服务配置 | 网盘服务超时和重试配置 |
| ⚡ cache | 缓存配置 | 缓存大小和过期时间配置 |
| 🔴 redis | Redis配置 | Redis连接配置 |
| 🔄 celery | Celery配置 | Celery任务队列配置 |
| 🔍 meilisearch | 搜索配置 | Meilisearch搜索引擎配置 |
| 📊 seo | SEO配置 | 搜索引擎优化配置 |

## 权限控制

配置管理功能需要以下权限：

- `system.config`: 系统配置管理权限（主权限）
- `config.view`: 配置查看权限
- `config.edit`: 配置编辑权限
- `config.backup`: 配置备份权限
- `config.sensitive`: 敏感配置查看权限

## 安全考虑

### 敏感配置保护

以下类型的配置被视为敏感信息：
- 数据库密码
- JWT密钥
- 邮箱密码
- API密钥和Token
- 网盘账号Cookie
- 包含 "password", "secret", "token", "key", "cookie" 的配置项

### 操作审计

所有配置变更都会记录以下信息：
- 操作用户
- 操作类型
- 配置键
- 变更前后的值（敏感信息会被遮蔽）
- 变更说明
- IP地址和用户代理
- 操作时间

### 备份机制

- 重要配置变更前会自动创建备份
- 支持手动创建备份
- 备份包含完整的配置文件内容
- 支持从备份恢复配置

## 配置生效机制

配置项根据生效方式分为三类：

1. **立即生效** (`immediate`): 配置更新后立即生效
   - 缓存配置
   - 并发限制配置
   - 安全策略配置

2. **重新加载** (`reload_required`): 需要重新加载配置模块
   - 日志配置
   - JWT密钥

3. **需要重启** (`restart_required`): 需要重启服务才能生效
   - 应用调试模式
   - API监听地址和端口
   - 数据库连接
   - Redis连接
   - Celery配置

## 使用示例

### 前端集成示例

```javascript
// 获取配置列表
const response = await fetch('/api/admin/config?category=app', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
const configData = await response.json();

// 更新配置
await fetch('/api/admin/config/app.debug', {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    value: false,
    comment: '生产环境关闭调试模式'
  })
});
```

### 错误处理

API返回标准的HTTP状态码：
- `200`: 操作成功
- `400`: 请求参数错误或验证失败
- `401`: 未认证
- `403`: 权限不足
- `404`: 配置项或备份不存在
- `500`: 服务器内部错误

## 注意事项

1. **权限要求**: 所有配置管理操作都需要相应的管理员权限
2. **敏感信息**: 敏感配置在界面中会被自动遮蔽
3. **备份建议**: 重要配置变更前建议手动创建备份
4. **重启提醒**: 某些配置变更需要重启服务才能生效
5. **操作审计**: 所有操作都会被记录，请谨慎操作

## 故障排除

### 常见问题

1. **配置文件格式错误**: 检查YAML语法是否正确
2. **权限不足**: 确认用户具有相应的配置管理权限
3. **验证失败**: 检查配置值是否符合验证规则
4. **备份失败**: 检查备份目录权限和磁盘空间

### 日志查看

配置管理相关的日志会记录在应用日志中，可以通过以下方式查看：
- 搜索 "config-service" 关键字
- 查看 "config-api" 相关日志
- 检查配置变更历史记录
