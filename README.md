# 盘搜后端服务 (pan-so-backend)

本后端服务现已升级为基于 **FastAPI + Celery + PostgreSQL** 的现代化、高并发架构，旨在提供一个功能强大且高度可扩展的网盘资源聚合与转存服务。它能从多个第三方资源站聚合搜索结果，并通过异步任务队列处理耗时的资源转存和入库操作，确保API接口的毫秒级响应。

## 核心功能

- **多源聚合搜索**: 支持从多个爬虫源并行抓取和聚合搜索结果。
- **智能排序与过滤**: 对搜索结果进行相关性评分排序，并支持按多种条件筛选。
- **全自动资源转存**: 支持将百度网盘、夸克网盘、迅雷网盘的公开分享资源，一键转存到系统配置的私有网盘账户中。
- **动态生成分享链接**: 资源成功转存后，会自动生成有时效性的新分享链接。
- **异步后台任务**: 采用 `Celery` 和 `Redis` 实现分布式任务队列，将所有耗时操作（如资源提交、解析、入库）移入后台工作进程(Worker)执行，彻底解决API服务阻塞问题。
- **高性能数据库**: 数据库已从 `SQLite` 升级为 `PostgreSQL`，提供强大的并发处理能力和数据一致性，完美支持Web服务与多个Celery Worker的同时读写。
- **本地资源缓存**: 搜索到的资源元数据会存入本地数据库，加快后续访问速度。
- **灵活的配置**: 所有关键服务（数据库、CORS、网盘账户、Redis、Celery）均通过 `config.yaml` 进行配置。
- **API访问控制**: 支持通过来源域名验证和并发数限制来保护API服务。

## 系统架构 (v2.0)

![新版系统架构图](https://mermaid.ink/svg/eyJjb2RlIjoiZ3JhcGggVERcbiAgICBzdWJncmFwaCBcIuWIo-S4iuWuiSAoQnJvd3NlcilcIjtcbiAgICAgICAgQVtVc2VyXVxuICAgIGVuZFxuXG4gICAgc3ViZ3JhcGggXCJBUEkg5pyN5YqhIChGYXN0QVBJIFByb2Nlc3MpXCI7XG4gICAgICAgIEIoRmFzdEFQSSBBcHApXG4gICAgICAgIEIxW1wiUE9TVCAvYXBpL3N1Ym1pdF9yZXNvdXJjZXNcIl1cbiAgICBlbmRcbiAgICBcbiAgICBzdWJncmFwaCBcIuaCqOWFs-iDveS_neOAguacquaJueWAguWAkuuHgFwiO1xuICAgICAgICBDW1JlZGlzXVxuICAgIGVuZFxuXG4gICAgc3ViZ3JhcGggXCLlkKbovrnnnI3liqHojrflj5EgKENlbGVyeSBXb3JrZXIgUHJvY2VzcylcIjtcbiAgICAgICAgRFtDZWxlcnkgV29ya2VyXVxuICAgICAgICBFW1wi5omT6KGo6YCj5pe25Lu75YqoPGJyLz7vvIjluIjnvZHovp3vvIjEQS_vvIjgr5HvvIldXG4gICAgZW5kXG5cbiAgICBzdWJncmFwaCBcIuaek-S4iuS_oOWJjei_kCAoRGF0YWJhc2UgU2VydmVyKVwiO1xuICAgICAgICBGW1Bvc3RncmVTUUxdXG4gICAgZW5kXG5cbiAgICBBIC0tIFwiMS4g5oSP5LqO5YqoVVJMXCIgLS0-IEIxXG4gICAgQjEgLS0gXCIyLiDliJvlu7rlpKflsZDliqnpophcIiAtLT4gRlxuICAgIEIxIC0tIFwiMy4g5Y-R6YCBVGFzayBJRFwiIC0tPiBDXG4gICAgQjEgLS0gXCI0LiDnrJHlkKvmhI_liqhCYXRjaCBJRFwiIC0tPiBBXG4gICAgXG4gICAgRCAtLSBcIjUuIOWNoeePkeS_neOAguacquaJXCIgLS0-IEMgXG4gICAgRCAtLT4gRVxuICAgIEUgLS0gXCI2LiDov5TlnKjmnpPluIlcIiAtLT4gRlxuICAgIEUgLS0gXCI3LiDmnKzmgLvkv57jgILmmZrnooZcIiAtLT4gQ1xuIiwibWVybWFpZCI6e30sInVwZGF0ZXIiOnsiY29uZmlnIjp7InRoZW1lIjoiZGVmYXVsdCJ9LCJlZGl0b3IiOmZhbHNlfSwiYXV0b1N5bmMiOnRydWUsInVwZGF0ZURpYWdyYW0iOmZhbHNlfQ)

项目的核心模块保持不变，但交互流程发生了变化：
- **`app/tasks`**: 新增模块，用于定义所有 Celery 后台任务。
- **`app/core/celery_app.py`**: Celery 应用的实例化和配置中心。
- **数据库**: 后端数据库已由 `SQLite` 替换为 `PostgreSQL`。
- **消息队列**: 引入 `Redis` 作为 Celery 的消息代理 (Broker) 和结果后端 (Result Backend)。


## 业务流程

### 1. 提交资源 (异步)
1.  用户通过 `/api/submit_resources` 接口提交一个或多个资源URL。
2.  `API` 服务接收请求，进行基本验证，然后为这批提交创建一个 `SubmissionBatch` 记录，并为每个URL创建 `SubmissionTask` 记录，状态为 `ACCEPTED`。
3.  `API` 服务将每个 `SubmissionTask` 的ID作为任务发布到 **Redis** 消息队列。
4.  `API` 服务**立即**向用户返回一个包含 `batch_id` 的响应，整个请求耗时在毫秒级。
5.  一个或多个独立的 **Celery Worker** 进程监听 **Redis** 队列，获取任务。
6.  `Worker` 根据 `task_id` 从 **PostgreSQL** 数据库中查询任务详情，并执行所有耗时的操作（访问URL、获取元数据、转存文件、更新数据库等）。
7.  `Worker` 将任务的最终状态（成功/失败）更新回数据库。

## 安装与运行

### 1. 环境准备
- Python 3.8+
- **PostgreSQL Server**: 推荐 12.0 或更高版本。
- **Redis Server**: 推荐 5.0 或更高版本。
- Poetry (可选)

### 2. 安装依赖
确保你的系统中已安装 `postgresql-devel` (或 `libpq-dev` for Debian/Ubuntu) 以便编译 `psycopg2`。
```bash
pip install -r requirements.txt
```

### 3. 配置服务
1.  复制 `app/config.example.yaml` 并重命名为 `app/config.yaml`。
2.  打开 `app/config.yaml` 并填写 **所有** 配置：
    - `database.url`: **（核心）** 配置你的 PostgreSQL 连接字符串。例如: `postgresql+asyncpg://user:password@host:port/dbname`
    - `redis`: **（核心）** 配置你的 Redis 连接信息。
    - `celery`: **（核心）** 配置 Celery 的 Broker 和 Backend URL，通常指向 Redis 的不同数据库。
    - `allowed_origins`: 允许访问API的前端域名列表。
    - `pan_service` & `*_accounts`: **（核心）** 填入你的百度、夸克、迅雷网盘账号信息。

### 4. 数据库迁移 (首次运行)
本项目使用 `Tortoise-ORM`。你可以让应用在启动时自动创建表结构，或者使用迁移工具（如 `aerich`）来管理。
对于生产环境，强烈建议使用 `aerich`。
```bash
# 安装 aerich
pip install aerich

# 初始化 (首次)
aerich init -t app.core.tortoise_config.TORTOISE_ORM

# 初始化数据库 (在数据库中创建aerich元数据表)
aerich init-db

# 如果模型有变动，生成迁移文件
# aerich migrate

# 应用迁移
# aerich upgrade
```

### 5. 启动服务 (需要启动三个进程)

你需要打开 **3个** 终端窗口来分别启动 Web 服务、Celery Worker 和 Celery Beat (如果未来使用定时任务)。

**终端 1: 启动 FastAPI Web 服务**
```bash
uvicorn app.main:app --host 0.0.0.0 --port 9999 --reload
```

**终端 2: 启动 Celery Worker**
```bash
# 在项目根目录运行
# 在Windows上，为保证最大兼容性和稳定性，推荐使用 solo 池
celery -A app.core.celery_app.celery_app worker --loglevel=info -P solo
# -A: 指定Celery应用实例
# -P solo: 使用单线程执行任务，避免Windows下的多进程问题
# --loglevel: 日志级别
```

服务启动后，API文档位于 `http://localhost:9999/docs`。 


后续版本架构图:
![后续版本架构图](./v3版本架构图实现.jpg)







