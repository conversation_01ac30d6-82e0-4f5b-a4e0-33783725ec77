import asyncio
import logging
from app.models.resource import PanResource
from app.services.meilisearch_service import meilisearch_service
from tortoise import Tortoise
from app.utils.config import settings
from datetime import datetime, timezone
import os
from tortoise.expressions import Q


def serialize_all(obj):
    """
    递归处理dict，将datetime转为字符串，id/uid/resource_key转为字符串
    """
    if isinstance(obj, dict):
        new_obj = {}
        for k, v in obj.items():
            if k in ("id", "uid", "resource_key") and v is not None:
                new_obj[k] = str(v)
            elif isinstance(v, datetime):
                # 确保datetime对象有时区信息
                if v.tzinfo is None:
                    # 数据库中的naive datetime假设为UTC时间
                    v = v.replace(tzinfo=timezone.utc)
                new_obj[k] = v.isoformat()
            else:
                new_obj[k] = v
        return new_obj
    return obj


async def _wait_for_task(task_info):
    """
    一个健壮的内部函数，用于等待Meilisearch任务完成。
    它可以处理不同的返回格式（对象或字典），并从中提取task_uid。
    """
    try:
        task_uid = None

        # 尝试多种方式获取task_uid
        if hasattr(task_info, "task_uid"):
            task_uid = task_info.task_uid
        elif hasattr(task_info, "uid"):
            task_uid = task_info.uid
        elif isinstance(task_info, dict):
            task_uid = (
                task_info.get("taskUid")
                or task_info.get("uid")
                or task_info.get("task_uid")
            )

        if task_uid is not None:
            # 确保task_uid是字符串格式
            task_uid_str = str(task_uid)
            # 简化等待逻辑，不使用wait_for_task避免版本兼容问题
            import time

            time.sleep(0.1)  # 简单延迟，让任务有时间完成
            logging.info(f"任务 {task_uid_str} 已提交。")
        else:
            logging.warning(f"无法从任务对象中提取task_uid: {task_info}")
    except Exception as e:
        # 记录可能出现的任何错误，但不会让整个同步过程崩溃
        logging.error(f"等待Meilisearch任务时出错: {e}", exc_info=True)


MAX_CONCURRENT_TASKS = 4  # 并发写入任务数，可根据CPU核数调整
LAST_SYNC_FILE = "last_meili_sync.txt"


def get_last_sync_point():
    if os.path.exists(LAST_SYNC_FILE):
        with open(LAST_SYNC_FILE, "r") as f:
            line = f.read().strip()
            if line:
                t, i = line.split(",")
                # 将字符串时间转换为datetime对象
                try:
                    sync_time = datetime.fromisoformat(t)
                    return sync_time, int(i)
                except ValueError:
                    # 如果解析失败，返回None重新开始同步
                    return None, 0
    return None, 0


def set_last_sync_point(sync_time, sync_id):
    with open(LAST_SYNC_FILE, "w") as f:
        # 确保时间以ISO格式保存
        if isinstance(sync_time, datetime):
            sync_time_str = sync_time.isoformat()
        else:
            sync_time_str = str(sync_time)
        f.write(f"{sync_time_str},{sync_id}")


async def sync_all_resources_to_meilisearch():
    """
    从数据库读取所有资源，并分批同步到Meilisearch，以优化内存使用。
    """
    print("开始同步数据库资源到Meilisearch...")
    db_url = settings.get("database", {}).get("url")
    if not db_url:
        print("错误：未在配置中找到数据库URL。")
        return

    try:
        await Tortoise.init(
            db_url=db_url,
            modules={"models": ["app.models.resource", "app.models.submission"]},
        )
        await Tortoise.generate_schemas()

        client = meilisearch_service.client
        index_name = meilisearch_service.index_name
        primary_key = "resource_key"

        # --- 优化索引检查和创建逻辑 ---
        try:
            index = client.get_index(index_name)
            if index.primary_key != primary_key:
                print(
                    f"索引 '{index_name}' 的主键为 '{index.primary_key}'，而不是期望的 '{primary_key}'。正在更新..."
                )
                # 更新主键
                task = client.update_index(index_name, {"primaryKey": primary_key})
                await _wait_for_task(task)
                print(f"主键已更新为 '{primary_key}'。")
        except Exception:
            print(f"索引 '{index_name}' 不存在，正在创建...")
            task = client.create_index(index_name, {"primaryKey": primary_key})
            await _wait_for_task(task)
            print(f"索引 '{index_name}' 已创建，主键为 '{primary_key}'。")

        # --- 新增：配置索引的可筛选和可排序属性 ---
        print("正在配置索引的可筛选属性...")
        filterable_attributes = [
            "pan_type",
            "file_type",
            "verified_status",
            "author",
            "updated_at",  # 添加时间过滤支持
        ]
        sortable_attributes = ["access_count", "created_at", "updated_at", "title"]
        settings_task = meilisearch_service.index.update_settings(
            {
                "filterableAttributes": filterable_attributes,
                "sortableAttributes": sortable_attributes,
            }
        )
        await _wait_for_task(settings_task)
        print(f"已成功配置可筛选属性: {filterable_attributes}")

        # --- 增量同步逻辑 ---
        last_sync_time, last_sync_id = get_last_sync_point()
        print(f"上次同步断点: created_at={last_sync_time}, id={last_sync_id}")

        batch_size = 1000
        total_synced = 0
        max_created_at = last_sync_time
        max_id = last_sync_id
        offset = 0
        while True:
            query = PanResource.all()
            if last_sync_time:
                query = query.filter(
                    Q(created_at__gt=last_sync_time)
                    | Q(created_at=last_sync_time, id__gt=last_sync_id)
                )
            resources = (
                await query.order_by("created_at", "id")
                .offset(offset)
                .limit(batch_size)
                .values()
            )
            if not resources:
                print("已读取完所有需要同步的资源。")
                break

            batch = [serialize_all(item) for item in resources]
            # 关键修复：add_documents_in_batches返回一个任务列表
            tasks = meilisearch_service.index.add_documents_in_batches(
                batch, batch_size=batch_size
            )
            for task in tasks:
                await _wait_for_task(task)

            # 记录本批最大created_at和id
            for item in resources:
                ctime = item.get("created_at")
                cid = item.get("id")
                if ctime:
                    if (
                        (not max_created_at)
                        or (ctime > max_created_at)
                        or (ctime == max_created_at and cid > max_id)
                    ):
                        max_created_at = ctime
                        max_id = cid

            total_synced += len(resources)
            print(
                f"已提交 {len(resources)} 条资源的写入任务。累计提交总数: {total_synced}"
            )
            offset += batch_size

        if max_created_at:
            set_last_sync_point(max_created_at, max_id)
            print(f"本次同步后已更新断点: created_at={max_created_at}, id={max_id}")

    except Exception as e:
        print(f"同步过程中发生错误: {e}")
    finally:
        await Tortoise.close_connections()
        print("数据库连接已关闭。")


if __name__ == "__main__":
    asyncio.run(sync_all_resources_to_meilisearch())
