"""
配置管理相关的数据模型
"""
from pydantic import BaseModel, Field
from typing import Any, Optional, List, Dict, Union
from datetime import datetime
from tortoise import fields, models


class ConfigBackup(models.Model):
    """配置备份表"""
    
    id = fields.IntField(pk=True)
    config_content = fields.JSONField(description="配置内容")
    file_hash = fields.CharField(max_length=64, description="配置文件哈希值")
    comment = fields.TextField(null=True, description="备份说明")
    created_by = fields.ForeignKeyField(
        "models.User", 
        related_name="config_backups",
        description="创建者"
    )
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    
    class Meta:
        table = "config_backups"
        ordering = ["-created_at"]


class ConfigChangeLog(models.Model):
    """配置变更日志表"""
    
    id = fields.IntField(pk=True)
    user = fields.ForeignKeyField(
        "models.User",
        related_name="config_changes", 
        description="操作用户"
    )
    action = fields.CharField(max_length=50, description="操作类型")  # create/update/delete/backup/restore
    config_key = fields.CharField(max_length=255, null=True, description="配置键")
    old_value = fields.JSONField(null=True, description="旧值")
    new_value = fields.JSONField(null=True, description="新值")
    comment = fields.TextField(null=True, description="变更说明")
    ip_address = fields.CharField(max_length=45, null=True, description="IP地址")
    user_agent = fields.TextField(null=True, description="用户代理")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    
    class Meta:
        table = "config_change_logs"
        ordering = ["-created_at"]


# Pydantic 请求模型
class ConfigUpdateRequest(BaseModel):
    """配置更新请求"""
    value: Any = Field(..., description="新配置值")
    comment: Optional[str] = Field(None, description="更新说明")


class ConfigUpdateItem(BaseModel):
    """配置更新项"""
    key: str = Field(..., description="配置键")
    value: Any = Field(..., description="配置值")
    comment: Optional[str] = Field(None, description="更新说明")


class BatchConfigUpdateRequest(BaseModel):
    """批量配置更新请求"""
    updates: List[ConfigUpdateItem] = Field(..., description="更新项列表")
    comment: Optional[str] = Field(None, description="批量更新说明")


class ConfigBackupRequest(BaseModel):
    """配置备份请求"""
    comment: Optional[str] = Field(None, description="备份说明")


class ConfigResetRequest(BaseModel):
    """配置重置请求"""
    keys: List[str] = Field(..., description="要重置的配置键列表")
    comment: Optional[str] = Field(None, description="重置说明")


class ConfigValidationRequest(BaseModel):
    """配置验证请求"""
    key: str = Field(..., description="配置键")
    value: Any = Field(..., description="配置值")


# Pydantic 响应模型
class ValidationResult(BaseModel):
    """验证结果"""
    valid: bool = Field(..., description="是否有效")
    message: str = Field(..., description="验证消息")
    suggestions: Optional[List[str]] = Field(None, description="建议")


class ConfigItem(BaseModel):
    """配置项"""
    key: str = Field(..., description="配置键")
    display_name: str = Field(..., description="显示名称")
    value: Any = Field(..., description="配置值")
    type: str = Field(..., description="数据类型")
    required: bool = Field(False, description="是否必填")
    sensitive: bool = Field(False, description="是否敏感信息")
    description: Optional[str] = Field(None, description="配置描述")
    validation_rules: Optional[Dict] = Field(None, description="验证规则")
    effect_type: str = Field("immediate", description="生效方式")


class ConfigCategory(BaseModel):
    """配置分类"""
    name: str = Field(..., description="分类名称")
    display_name: str = Field(..., description="显示名称")
    description: Optional[str] = Field(None, description="分类描述")
    icon: Optional[str] = Field(None, description="分类图标")
    configs: List[ConfigItem] = Field([], description="配置项列表")


class ConfigListResponse(BaseModel):
    """配置列表响应"""
    categories: List[ConfigCategory] = Field(..., description="配置分类列表")
    total_count: int = Field(..., description="总配置项数量")
    restart_required: bool = Field(False, description="是否需要重启")


class ConfigBackupInfo(BaseModel):
    """配置备份信息"""
    id: int = Field(..., description="备份ID")
    comment: Optional[str] = Field(None, description="备份说明")
    file_hash: str = Field(..., description="文件哈希值")
    created_by_username: str = Field(..., description="创建者用户名")
    created_at: datetime = Field(..., description="创建时间")


class ConfigBackupListResponse(BaseModel):
    """配置备份列表响应"""
    backups: List[ConfigBackupInfo] = Field(..., description="备份列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")


class ConfigChangeLogInfo(BaseModel):
    """配置变更日志信息"""
    id: int = Field(..., description="日志ID")
    username: str = Field(..., description="操作用户")
    action: str = Field(..., description="操作类型")
    config_key: Optional[str] = Field(None, description="配置键")
    old_value: Any = Field(None, description="旧值")
    new_value: Any = Field(None, description="新值")
    comment: Optional[str] = Field(None, description="变更说明")
    created_at: datetime = Field(..., description="创建时间")


class ConfigHistoryResponse(BaseModel):
    """配置变更历史响应"""
    logs: List[ConfigChangeLogInfo] = Field(..., description="变更日志列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页")
    size: int = Field(..., description="每页大小")
    pages: int = Field(..., description="总页数")


class RestartStatusResponse(BaseModel):
    """重启状态响应"""
    restart_required: bool = Field(..., description="是否需要重启")
    pending_changes: List[str] = Field(..., description="待生效的配置项")
    affected_services: List[str] = Field(..., description="受影响的服务")
    last_restart_time: Optional[datetime] = Field(None, description="上次重启时间")


class ConfigDiffResponse(BaseModel):
    """配置差异对比响应"""
    config_key: str = Field(..., description="配置键")
    old_value: Any = Field(..., description="旧值")
    new_value: Any = Field(..., description="新值")
    change_type: str = Field(..., description="变更类型")  # added/modified/deleted
    timestamp: datetime = Field(..., description="变更时间")


class ApiResponse(BaseModel):
    """通用API响应"""
    status: str = Field(..., description="状态")
    message: str = Field(..., description="消息")
    data: Optional[Any] = Field(None, description="数据")
