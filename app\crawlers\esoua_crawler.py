import httpx
import logging
import asyncio
from typing import List, Dict, Any, Optional
import re
import time
import json
import random
import base64
import struct
from urllib.parse import urljoin, quote
from functools import wraps
from datetime import datetime, timezone
from cachetools import TTLCache
from app.utils.config import settings
from app.utils.common import parse_publish_time

try:
    from curl_cffi.aio import AsyncSession
except ImportError:
    AsyncSession = None

logger = logging.getLogger("esoua-crawler")
logger.setLevel(logging.INFO)

# 从配置文件获取爬虫配置
CRAWLER_CONF = settings.get("esoua_crawler", {})
BASE_URL = CRAWLER_CONF.get("base_url", "https://www.esoua.com")
TIMEOUT = CRAWLER_CONF.get("timeout", 10)  # 请求超时时间
MAX_RETRIES = CRAWLER_CONF.get("max_retries", 3)  # 最大重试次数
RETRY_DELAY = CRAWLER_CONF.get("retry_delay", 1.0)  # 重试延迟基础时间
CONCURRENT_LIMIT = CRAWLER_CONF.get("concurrent_limit", 6)  # 并发限制
ANTI_KEY = CRAWLER_CONF.get("anti_key", "ylLrtEfxzzD0sXc6yHRQ")  # 加密密钥
USE_CURL_CFFI = CRAWLER_CONF.get("use_curl_cffi", False)  # 是否使用curl_cffi
JS_REDIRECT_SUPPORT = CRAWLER_CONF.get("js_redirect_support", False)  # 是否支持JS重定向

# 缓存配置
CACHE_CONF = settings.get("cache", {})
SEARCH_CACHE_SIZE = CACHE_CONF.get("resource_cache", {}).get("maxsize", 1000)
SEARCH_CACHE_TTL = CACHE_CONF.get("resource_cache", {}).get("ttl", 3600)

# New constants for Bse algorithm
II = 19088743
FI = 4023233417
jI = 2562383102
I_DOLLAR = 271733878
NI = [
    3614090360,
    3905402710,
    606105819,
    3250441966,
    4118548399,
    1200080426,
    2821735955,
    4249261313,
    1770035416,
    2336552879,
    4294925233,
    2304563134,
    1804603682,
    4254626195,
    2792965006,
    1236535329,
    4129170786,
    3225465664,
    643717713,
    3921069994,
    3593408605,
    38016083,
    3634488961,
    3889429448,
    568446438,
    3275163606,
    4107603335,
    1163531501,
    2850285829,
    4243563512,
    1735328473,
    2368359562,
    4294588738,
    2272392833,
    1839030562,
    4259657740,
    2763975236,
    1272893353,
    4139469664,
    3200236656,
    681279174,
    3936430074,
    3572445317,
    76029189,
    3654602809,
    3873151461,
    530742520,
    3299628645,
    4096336452,
    1126891415,
    2878612391,
    4237533241,
    1700485571,
    2399980690,
    4293915773,
    2240044497,
    1873313359,
    4264355552,
    2734768916,
    1309151649,
    4149444226,
    3174756917,
    718787259,
    3951481745,
]
LI = [
    7,
    12,
    17,
    22,
    7,
    12,
    17,
    22,
    7,
    12,
    17,
    22,
    7,
    12,
    17,
    22,
    5,
    9,
    14,
    20,
    5,
    9,
    14,
    20,
    5,
    9,
    14,
    20,
    5,
    9,
    14,
    20,
    4,
    11,
    16,
    23,
    4,
    11,
    16,
    23,
    4,
    11,
    16,
    23,
    4,
    11,
    16,
    23,
    6,
    10,
    15,
    21,
    6,
    10,
    15,
    21,
    6,
    10,
    15,
    21,
    6,
    10,
    15,
    21,
]

# Custom Base64 alphabet for r4
X_BODY_SIGN_A4_DEFAULT = (
    "QWERTYUIOPASDFGHJKLZXCVBNMqwertyuiopasdfghjklzxcvbnm0123456789+/"
)


def timing_decorator(func):
    """装饰器：用于记录函数执行时间，仅在INFO级别记录"""

    @wraps(func)
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        result = await func(*args, **kwargs)
        end_time = time.time()
        elapsed_time = end_time - start_time
        logger.info(f"执行 {func.__name__} 用时: {elapsed_time:.4f}秒")
        return result

    return wrapper


class EsouaCrawler:
    """爬取esoua.com网站的网盘资源数据"""

    def __init__(self):
        self.base_url = BASE_URL
        self.api_path = "/v1/search/disk"
        self.api_url = f"{self.base_url}{self.api_path}"
        self.anti_key = ANTI_KEY

        # 增加对 curl_cffi 和 JS 重定向的配置
        self.use_curl_cffi = USE_CURL_CFFI
        self.js_redirect_support = JS_REDIRECT_SUPPORT

        # 默认请求头
        self.headers = {
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
            "accept-encoding": "gzip, deflate, br, zstd",
            "content-type": "application/json",
            "origin": self.base_url,
            "sec-ch-ua": '"Chromium";v="136", "Google Chrome";v="136", "Not-A.Brand";v="99"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"Windows"',
            "sec-fetch-site": "same-origin",
            "sec-fetch-mode": "cors",
            "sec-fetch-dest": "empty",
            "x-path": self.api_path,
            "referer": self.base_url,
        }

        # 缓存
        self.search_cache = TTLCache(maxsize=SEARCH_CACHE_SIZE, ttl=SEARCH_CACHE_TTL)

        # 并发控制
        self.request_semaphore = asyncio.Semaphore(CONCURRENT_LIMIT)

        # HTTP客户端
        self.client = None

        # Cache for Bse algorithm (MD5-like)
        self.G3_cache = {
            "": "d41d8cd98f00b204e9800998ecf8427e",
            0: "cfcd208495d565ef66e7dff9f98764da",
            "0": "cfcd208495d565ef66e7dff9f98764da",  # Added from common usage
            1: "c4ca4238a0b923820dcc509a6f75849b",
            "1": "c4ca4238a0b923820dcc509a6f75849b",  # Added from common usage
            "1234": "81dc9bdb52d04dc20036dbd8313ed055",
            "123456789": "25f9e794323b453885f5181f1b624d0b",
            "a": "0cc175b9c0f1b6a831c399e269772661",
            "root": "63a9f0ea7bb98050796b649e85481845",
            "admin": "21232f297a57a5a743894a0e4a801fc3",
            "password": "5f4dcc3b5aa765d61d8327deb882cf99",
            "你好": "7eca689f0d3389d9dea66ae112e5cfd7",
            "测试": "ce8ae9da5b7cd6c3df2929543a9af92d",
            " ": "7215ee9c7d9dc229d2921a40e899ec5f",
            "test": "098f6bcd4621d373cade4e832627b4f6",
            "123": "202cb962ac59075b964b07152d234b70",
        }

        logger.info(f"已初始化Esoua爬虫，API URL: {self.api_url}")
        if self.use_curl_cffi and AsyncSession is None:
            logger.warning(
                "配置为使用curl_cffi，但未安装。请执行 'pip install curl_cffi'。将回退到 httpx。"
            )
            self.use_curl_cffi = False

    async def initialize(self):
        """初始化HTTP客户端"""
        if self.client is None:
            # 根据配置选择客户端
            if self.use_curl_cffi and AsyncSession:
                # curl_cffi 不直接支持 http2=True, 但会尽可能使用
                self.client = AsyncSession(
                    timeout=TIMEOUT,
                    impersonate="chrome120",
                    allow_redirects=(
                        not self.js_redirect_support
                    ),  # 手动处理重定向时需关闭
                )
                logger.info("已初始化Esoua爬虫 (curl_cffi 客户端)")
            else:
                self.client = httpx.AsyncClient(
                    timeout=TIMEOUT,
                    follow_redirects=(
                        not self.js_redirect_support
                    ),  # 手动处理重定向时需关闭
                    limits=httpx.Limits(
                        max_keepalive_connections=10, max_connections=20
                    ),
                    http2=True,
                    verify=False,
                )
                logger.info("已初始化Esoua爬虫 (httpx 客户端)")

    async def close(self):
        """关闭HTTP客户端连接池"""
        if self.client:
            await self.client.aclose()
            self.client = None
            logger.info("已关闭Esoua爬虫HTTP客户端会话")

    def _s_v1(self, x):
        """复刻 I5.v1"""
        r = str(x)[:12]
        n = 0
        for o in range(len(r)):
            s = int(r[o])
            d = len(r) - o
            n += s * d
            n ^= (s + 3) << (o % 3)
            n = ((n * 13) + 7) % 10
            # 移除调试打印
            # print(f"n: {n}")
        c = time.localtime().tm_hour
        return (n + c) % 10

    def _s_v2(self, x):
        """复刻 I5.v2"""
        r = str(x)[:12]
        n = 0
        for c in range(len(r)):
            n += int(r[c])
        return n % 10

    def _s_v3(self, x):
        """复刻 I5.v3"""
        r = str(x)[:12]
        n = 0
        for c in range(len(r)):
            o = int(r[c])
            n += o * o
        return n % 10

    def _s_v4(self, e):
        """复刻网站的 I4.v4 函数"""
        r = str(e)[:12]
        n = r
        s = 0
        o = [2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37]
        for c in range(len(n)):
            d = int(n[c])
            s += d * o[c]
        return s % 10

    def _s(self, x, version="v4"):
        """复刻 s 函数，支持 v1, v2, v3, v4"""
        I5 = {"v1": self._s_v1, "v2": self._s_v2, "v3": self._s_v3, "v4": self._s_v4}
        if version not in I5:
            raise ValueError("Invalid version: choose v1, v2, v3, or v4")
        return I5[version](x)

    def _get_x_time(self):
        """生成X-Time签名值"""
        c = int(time.time() * 1000)  # 标准时间戳（毫秒）
        d = c // 10  # Math.floor(c / 10)
        u = self._s(c, "v1")  # 计算最后一位, updated to v3 from v1
        x_time = str(d * 10 + u)  # 组合为完整时间戳
        return x_time

    def _l6(self, e):
        """生成指定位数的随机数字字符串"""
        x = 10 ** (e - 1)  # 例如：e=4时，x=1000
        t = 10**e - 1  # 例如：e=4时，t=9999
        return str(random.randint(x, t))  # 返回随机数字字符串

    def _get_x_request_id(self):
        """生成请求ID"""
        return self._l6(4)

    def _de(self, e):
        """复刻网站的De函数，用于加密"""
        a = list(e.encode("utf-8"))
        # 第一次翻转
        for n in range(len(a) // 2):
            s = len(a) - 1 - n
            a[n], a[s] = a[s], a[n]
        # XOR操作
        r = ord("x")
        for n in range(len(a)):
            a[n] ^= r
        # 第二次翻转
        for n in range(len(a) // 2):
            s = len(a) - 1 - n
            a[n], a[s] = a[s], a[n]
        # 转回字符串并Base64编码
        result = "".join(chr(b) for b in a)
        return base64.b64encode(result.encode("utf-8")).decode("utf-8")

    def _he(self, e: str) -> str:
        """New encoding function, replaces _de."""
        a = list(e.encode("utf-8"))
        a = a[::-1]  # Reverse list
        a = [byte ^ 120 for byte in a]  # XOR operation
        a = a[::-1]  # Reverse list again
        return base64.b64encode(bytes(a)).decode("utf-8")

    def _left_rotate(self, value: int, shift: int) -> int:
        """Bitwise left rotate for _bse."""
        return ((value << shift) | (value >> (32 - shift))) & 0xFFFFFFFF

    def _rf(self, value: int) -> int:
        """Byte order conversion (little-endian to big-endian) for _bse."""
        return (
            ((value & 0xFF) << 24)
            | (((value >> 8) & 0xFF) << 16)
            | (((value >> 16) & 0xFF) << 8)
            | ((value >> 24) & 0xFF)
        ) & 0xFFFFFFFF

    def _bse(self, e: str) -> str:
        """MD5-like hashing function."""
        if e in self.G3_cache:
            return self.G3_cache[e]

        i = e.encode("utf-8")
        a = len(i)

        u = bytearray(i)
        u.append(0x80)

        while len(u) % 64 != 56:
            u.append(0)

        l_val = a * 8
        u.extend(struct.pack("<Q", l_val))

        h = II & 0xFFFFFFFF
        p = FI & 0xFFFFFFFF
        m = jI & 0xFFFFFFFF
        b = I_DOLLAR & 0xFFFFFFFF

        for g_offset in range(0, len(u), 64):
            block = u[g_offset : g_offset + 64]
            words = [struct.unpack("<I", block[j : j + 4])[0] for j in range(0, 64, 4)]

            A, B, C, D = h, p, m, b

            for i_round in range(64):
                if i_round < 16:
                    F = (B & C) | ((~B) & D)
                    g_index = i_round
                elif i_round < 32:
                    F = (D & B) | ((~D) & C)
                    g_index = (5 * i_round + 1) % 16
                elif i_round < 48:
                    F = B ^ C ^ D
                    g_index = (3 * i_round + 5) % 16
                else:
                    F = C ^ (B | (~D))
                    g_index = (7 * i_round) % 16

                temp = D
                D = C
                C = B
                sum_val = (A + F + NI[i_round] + words[g_index]) & 0xFFFFFFFF
                rotated = self._left_rotate(sum_val, LI[i_round])
                B = (B + rotated) & 0xFFFFFFFF
                A = temp

            h = (h + A) & 0xFFFFFFFF
            p = (p + B) & 0xFFFFFFFF
            m = (m + C) & 0xFFFFFFFF
            b = (b + D) & 0xFFFFFFFF

        result = "".join(
            [
                format(self._rf(h), "08x"),
                format(self._rf(p), "08x"),
                format(self._rf(m), "08x"),
                format(self._rf(b), "08x"),
            ]
        )

        # Store in cache (optional, consider cache size implications)
        # self.G3_cache[e] = result
        return result

    def _custom_b64encode(self, data: bytes, alphabet: str) -> str:
        """Base64 encode with a custom alphabet."""
        std_alphabet = (
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
        )
        b64_encoded_str = base64.b64encode(data).decode("utf-8")
        if alphabet == std_alphabet:
            return b64_encoded_str
        trans_table = str.maketrans(std_alphabet, alphabet)
        return b64_encoded_str.translate(trans_table)

    def _r4(self, e: str, alphabet: Optional[str] = None) -> str:
        """Encoding function using XOR and custom Base64."""
        if alphabet is None:
            alphabet = X_BODY_SIGN_A4_DEFAULT

        input_bytes = e.encode("utf-8")  # In JS, it was TextEncoder().encode(e)

        if not input_bytes:
            return ""

        r = bytearray(len(input_bytes))
        for o in range(len(input_bytes)):
            c = (o % 7) + 1
            r[o] = input_bytes[o] ^ c

        return self._custom_b64encode(r, alphabet)

    def _generate_signatures(self, body_str, x_time):
        """生成请求所需的所有签名 (Updated Algorithm 2025/6/2)"""
        he_anti_key = self._he(self.anti_key)

        # 生成X-Sign (请求头签名)
        # Salt from JS: path + "##reman##" + timestamp
        input_str_sign = f"{he_anti_key}{self.api_path}##reman##{x_time}"
        bse_output_sign = self._bse(input_str_sign)
        x_sign = self._r4(
            bse_output_sign
        )  # Uses X_BODY_SIGN_A4_DEFAULT by default in _r4

        # 生成X-Body-Sign (请求体签名)
        # Input from JS: he_anti_key + body_str
        input_str_body_sign = f"{he_anti_key}{body_str}"
        bse_output_body_sign = self._bse(input_str_body_sign)
        x_body_sign = self._r4(bse_output_body_sign)  # Uses X_BODY_SIGN_A4_DEFAULT

        # 生成X-Request-ID
        x_request_id = self._get_x_request_id()

        return {
            "x_sign": x_sign,
            "x_body_sign": x_body_sign,
            "x_request_id": x_request_id,
            "x_time": x_time,
        }

    async def _handle_js_redirect(
        self,
        method: str,
        url: str,
        headers: Dict[str, Any],
        body_str: Optional[str] = None,
        max_redirects: int = 5,
        depth: int = 0,
    ) -> Any:
        """处理JS重定向"""
        if depth >= max_redirects:
            logger.warning(f"达到最大重定向次数 ({max_redirects})，停止处理: {url}")
            return None

        current_url = url
        logger.info(f"[重定向 {depth+1}/{max_redirects}] 请求 URL: {current_url}")

        try:
            # 准备请求参数
            request_kwargs = {"headers": headers}
            if self.use_curl_cffi:
                if method.lower() == "post" and body_str:
                    request_kwargs["data"] = body_str.encode("utf-8")
            else:  # httpx
                if method.lower() == "post" and body_str:
                    request_kwargs["content"] = body_str.encode("utf-8")

            # 发送请求
            request_func = getattr(self.client, method.lower())
            response = await request_func(current_url, **request_kwargs)

            # 检查JS重定向
            html_content = response.text
            redirect_match = re.search(
                r"window\.location\.href=['\"]([^'\"]+)['\"]", html_content
            )

            if redirect_match:
                redirect_url = redirect_match.group(1)
                logger.info(f"检测到JavaScript重定向: {redirect_url}")

                # 拼接URL
                redirect_url = urljoin(current_url, redirect_url)

                # 递归处理，重定向后始终使用GET方法
                return await self._handle_js_redirect(
                    "get",
                    redirect_url,
                    headers,
                    max_redirects=max_redirects,
                    depth=depth + 1,
                )

            logger.info(f"无JS重定向，返回最终响应 (状态码: {response.status_code})")
            return response

        except Exception as e:
            logger.error(f"处理JS重定向时出错: {e}", exc_info=True)
            raise  # 重新抛出异常，由外层重试逻辑捕获

    async def _make_request(self, method, url, body_str, headers):
        """统一处理HTTP请求，使用信号量限制并发"""
        async with self.request_semaphore:
            start_time = time.time()

            # 确保客户端已初始化
            if self.client is None:
                await self.initialize()

            # 添加当前时间到请求头
            headers["Date"] = datetime.now(timezone.utc).strftime(
                "%a, %d %b %Y %H:%M:%S GMT"
            )

            # 准备请求参数
            kwargs = {"headers": headers, "content": body_str.encode("utf-8")}

            # 记录请求信息
            logger.info(f"开始请求: {url}")
            # logger.info(f"请求头: {headers}")
            # logger.info(f"请求体: {body_str}")

            # 重试逻辑
            for attempt in range(MAX_RETRIES + 1):
                try:
                    # 如果启用了JS重定向支持，则使用重定向处理器
                    if self.js_redirect_support:
                        response = await self._handle_js_redirect(
                            method, url, headers, body_str
                        )
                        if response is None:
                            raise Exception("JS重定向失败或达到最大次数")
                    else:
                        # 否则，执行常规请求
                        request_kwargs = {"headers": headers}
                        if self.use_curl_cffi:
                            if method.lower() == "post" and body_str:
                                request_kwargs["data"] = body_str.encode("utf-8")
                        else:  # httpx
                            if method.lower() == "post" and body_str:
                                request_kwargs["content"] = body_str.encode("utf-8")

                        request_func = getattr(self.client, method.lower())
                        response = await request_func(url, **request_kwargs)

                    # 记录响应信息
                    elapsed = time.time() - start_time
                    logger.info(f"请求完成: {url}")
                    logger.info(f"状态码: {response.status_code}")
                    logger.info(f"请求耗时: {elapsed:.2f}秒")

                    # 返回响应
                    return response
                except Exception as e:
                    if attempt < MAX_RETRIES:
                        delay = RETRY_DELAY * (attempt + 1)  # 指数退避
                        logger.warning(
                            f"请求失败，{delay}秒后重试 ({attempt+1}/{MAX_RETRIES}): {url}, 错误: {str(e)}"
                        )
                        await asyncio.sleep(delay)
                        continue
                    logger.error(f"请求失败，已达最大重试次数: {url}, 错误: {str(e)}")
                    raise

    @timing_decorator
    async def search(
        self,
        keyword: str,
        pan_type: Optional[str] = None,
        page: int = 1,
        limit: int = 30,
        user_agent: Optional[str] = None,
        headers: Optional[Dict[str, str]] = None,
        file_type: Optional[str] = None,
        exact: bool = False,
    ) -> Dict[str, Any]:
        """搜索网盘资源

        参数:
            keyword: 搜索关键词，对应请求体中的q参数
            pan_type: 网盘类型 (BDY=百度网盘, QUARK=夸克网盘, ALY=阿里云盘, XUNLEI=迅雷网盘)，默认为全部
            page: 页码，默认为1，对应请求体中的page参数
            limit: 每页结果数，默认为30，对应请求体中的size参数
            user_agent: 用户的User-Agent(向后兼容)
            headers: 用户的完整请求头，优先级高于user_agent
            file_type: 文件类型筛选，如"video", "audio", "document"等
            exact: 是否精准搜索，默认为False

        返回:
            包含以下字段的搜索结果:
            - status: 请求状态
            - message: 状态消息
            - page: 当前页码
            - results: 资源列表，每个资源包含
              - title: 资源标题
              - pan_type: 网盘类型 (1=百度网盘, 2=夸克网盘, 3=阿里云盘, 4=迅雷网盘)
              - detail_url: 网盘链接
              - resource_id: 资源ID
              - updated_at: 更新时间
              - text_content: 资源预览内容，包含文件列表
        """
        # 构建缓存键
        cache_key = f"{keyword}_{pan_type}_{page}_{limit}_{file_type}_{exact}"

        # 检查缓存
        if cache_key in self.search_cache:
            logger.info(
                f"使用缓存的搜索结果: {keyword}, 网盘类型: {pan_type}, 文件类型: {file_type}, 精准搜索: {exact}"
            )
            cached_result = self.search_cache[cache_key]
            # 为缓存结果添加缓存标记
            cached_result["from_cache"] = True
            return cached_result
        logger.info(
            f"请求内容: {keyword}, 网盘类型: {pan_type}, 第{page}页, 文件类型: {file_type}, 精准搜索: {exact}"
        )
        try:
            start_time = time.time()
            logger.info(
                f"开始搜索: {keyword}, 网盘类型: {pan_type}, 第{page}页, 文件类型: {file_type}, 精准搜索: {exact}"
            )

            # 将pan_type转换为API需要的格式
            api_pan_type = ""
            if pan_type:
                if isinstance(pan_type, int):
                    if pan_type == 1:
                        api_pan_type = "BDY"
                    elif pan_type == 2:
                        api_pan_type = "QUARK"
                    elif pan_type == 3:
                        api_pan_type = "ALY"
                    elif pan_type == 4:
                        api_pan_type = "XUNLEI"
                else:
                    api_pan_type = pan_type

            # 获取文件类型对应的扩展名列表
            format_extensions = []
            if file_type:
                from app.utils.common import get_format_extensions_by_type

                format_extensions = get_format_extensions_by_type(file_type)
                logger.info(f"文件类型 {file_type} 对应的扩展名: {format_extensions}")

            # 构建请求体
            request_body = {
                "page": page,
                "q": keyword,
                "user": "",
                "exact": exact,
                "format": format_extensions,  # 设置文件扩展名筛选
                "share_time": "",
                "size": limit,
                "type": api_pan_type,
                "exclude_user": [],
                "adv_params": {"wechat_pwd": "", "platform": "pc"},
            }

            # 转为JSON字符串
            body_str = json.dumps(request_body, separators=(",", ":"))

            # 生成时间戳
            x_time = self._get_x_time()

            # 生成签名
            signatures = self._generate_signatures(body_str, x_time)

            # 准备请求头
            request_headers = self.headers.copy()
            request_headers.update(
                {
                    "X-Sign": signatures["x_sign"],
                    "X-Body-Sign": signatures["x_body_sign"],
                    "X-Time": signatures["x_time"],
                    "X-Request-ID": signatures["x_request_id"],
                }
            )

            # 使用用户提供的请求头
            if headers:
                # 提取关键的浏览器指纹相关头部
                important_headers = [
                    "user-agent",
                    "accept",
                    "accept-language",
                    "accept-encoding",
                    "sec-ch-ua",
                    "sec-ch-ua-mobile",
                    "sec-ch-ua-platform",
                ]

                # 遍历并应用重要的浏览器指纹相关头部
                for header in important_headers:
                    header_lower = header.lower()
                    for key, value in headers.items():
                        if key.lower() == header_lower:
                            request_headers[key] = value
                            break
            # 向后兼容：如果只提供了user_agent参数
            elif user_agent:
                request_headers["User-Agent"] = user_agent

            # 发送请求
            request_start = time.time()
            logger.info(f"爱搜发送的请求头: {request_headers}")
            response = await self._make_request(
                "post", self.api_url, body_str, request_headers
            )
            request_time = time.time() - request_start
            # logger.info(f'响应状态码: {response.status_code}, 内容: {response.text}')
            logger.info(f"请求用时: {request_time:.4f}秒")

            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"搜索请求失败: {response.status_code}")
                return {
                    "status": "error",
                    "message": f"搜索请求失败: {response.status_code}",
                    "results": [],
                    "from_cache": False,
                }

            # 解析响应
            parse_start = time.time()
            result_data = response.json()

            # 检查API返回状态
            if result_data.get("code") != 200:
                logger.error(f"API返回错误: {result_data.get('msg')}")
                return {
                    "status": "error",
                    "message": f"API返回错误: {result_data.get('msg')}",
                    "results": [],
                    "from_cache": False,
                }

            # 如果total为0，则返回空列表
            if result_data.get("data", {}).get("total", 0) == 0:
                return {
                    "status": "success",
                    "message": "搜索成功",
                    "results": [],
                    "total": 0,
                    "totals": 0,
                    "keyword": keyword,
                    "page": page,
                    "from_cache": False,
                }

            # 提取结果
            api_results = result_data.get("data", {}).get("list", [])
            total_count = result_data.get("data", {}).get("total", 0)

            # 转换结果格式为标准格式
            results = []
            pan_type_map = {
                "BDY": 1,  # 百度网盘
                "QUARK": 2,  # 夸克网盘
                "ALY": 3,  # 阿里网盘
                "XUNLEI": 4,  # 迅雷网盘
            }

            for item in api_results:
                item_pan_type = item.get("disk_type", "")

                # 将网盘类型转换为数字
                pan_type_number = pan_type_map.get(item_pan_type)

                # 如果不在支持的网盘类型中，跳过
                if not pan_type_number:
                    continue

                # 标题需要去除HTML标签
                title = item.get("disk_name", "")
                title = re.sub(r"<[^>]+>", "", title)

                resource_id = item.get("disk_id", "")

                if pan_type_number == 1 and resource_id:
                    resource_id = "1" + resource_id

                # 提取资源预览内容  为files字段
                text_content = ""
                files_content = item.get("files", "")
                if files_content:
                    try:
                        # 去除HTML标签如 <em> 和 </em>
                        clean_files = re.sub(r"<[^>]+>", "", files_content)

                        # 分割成行并去除空行
                        file_lines = [
                            line.strip()
                            for line in clean_files.split("\n")
                            if line.strip()
                        ]
                        # 最多保留10个文件名
                        file_lines = file_lines[:10]
                        # 合并成一个字符串
                        text_content = "\n".join(file_lines)
                    except Exception as e:
                        logger.error(f"处理文件列表异常: {str(e)}")
                        text_content = ""

                # 构建结果项
                # 使用parse_publish_time处理时间并统一为updated_at字段
                parsed_time = parse_publish_time(item.get("shared_time", ""))
                results.append(
                    {
                        "title": title,
                        "pan_type": pan_type_number,
                        "detail_url": item.get("link", ""),
                        "resource_id": resource_id,  # 使用处理后的资源ID
                        "updated_at": parsed_time,
                        "share_user": item.get("share_user", ""),
                        "original_url": item.get("link", ""),
                        "text_content": text_content,  # 添加文件列表作为资源预览
                    }
                )
            parse_time = time.time() - parse_start
            logger.info(f"解析搜索结果用时: {parse_time:.4f}秒")

            # 计算总时间
            total_time = time.time() - start_time

            # 构建返回结果
            result_data = {
                "status": "success",
                "message": "搜索成功",
                "keyword": keyword,
                "page": page,
                "results": results,
                "total": len(results),
                "totals": total_count,
                "from_cache": False,
                "timing_stats": {
                    "total_time": f"{total_time:.4f}秒",
                    "request_time": f"{request_time:.4f}秒",
                    "parse_time": f"{parse_time:.4f}秒",
                },
            }
            logger.info(f"爱搜检索条数: {len(results)}")
            # 存入缓存（移除时间统计信息以减少缓存大小）
            cache_result = {k: v for k, v in result_data.items() if k != "timing_stats"}
            self.search_cache[cache_key] = cache_result

            logger.info(f"搜索结果缓存成功: {keyword}, 总耗时: {total_time:.4f}秒")
            return result_data

        except Exception as e:
            logger.error(f"搜索过程中发生异常: {str(e)}")
            return {
                "status": "error",
                "message": f"搜索异常: {str(e)}",
                "results": [],
                "from_cache": False,
            }

    async def get_share_link(self, detail_url: str) -> Dict[str, Any]:
        """获取分享链接信息（这个网站直接提供了分享链接，无需额外请求）

        Args:
            detail_url: 详情页URL/分享链接

        Returns:
            分享链接信息字典
        """
        # 从URL中提取网盘类型
        pan_type = 0
        if "pan.baidu.com" in detail_url:
            pan_type = 1
        elif "pan.quark.cn" in detail_url:
            pan_type = 2
        elif "aliyundrive.com" in detail_url or "alipan.com" in detail_url:
            pan_type = 3
        elif "pan.xunlei.com" in detail_url:
            pan_type = 4

        return {
            "status": "success",
            "share_url": detail_url,
            "pan_type": pan_type,
            "message": "获取分享链接成功",
            "from_cache": False,
        }


# 创建单例实例
esoua_crawler = EsouaCrawler()


# 测试诊断
async def run_diagnostic_test():
    """运行诊断测试"""
    try:
        logger.info("开始运行诊断测试...")
        start_time = time.time()

        result = await esoua_crawler.search(
            "凡人修仙传", pan_type="QUARK", limit=30, page=1
        )
        print(result)

        total_time = time.time() - start_time
        logger.info(f"诊断测试完成，总耗时: {total_time:.4f}秒")
        logger.info(f"性能统计: {result.get('timing_stats', {})}")

        return result
    finally:
        # 确保连接池被关闭
        await esoua_crawler.close()


# 直接运行文件时的入口点
if __name__ == "__main__":
    # 设置日志输出到控制台
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 运行测试函数
    import asyncio

    asyncio.run(run_diagnostic_test())
    print("测试完成，程序退出")
