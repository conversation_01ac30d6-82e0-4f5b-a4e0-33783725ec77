from functools import wraps
from typing import Optional, List
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.models.user import User
from app.core.auth import JWTService

# HTTP Bearer认证
security = HTTPBearer(auto_error=False)


# 预定义权限常量
class Permissions:
    # 用户管理权限
    USER_MANAGE = "user.manage"
    USER_VIEW = "user.view"

    # 资源管理权限
    RESOURCE_MANAGE = "resource.manage"
    RESOURCE_SUBMIT = "resource.submit"
    RESOURCE_VIEW = "resource.view"

    # 反馈管理权限
    FEEDBACK_MANAGE = "feedback.manage"
    FEEDBACK_CREATE = "feedback.create"
    FEEDBACK_VIEW = "feedback.view"

    # 系统配置权限
    SYSTEM_CONFIG = "system.config"
    SYSTEM_STATS = "system.stats"

    # 个人资料权限
    PROFILE_MANAGE = "profile.manage"


async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
) -> Optional[User]:
    """获取当前用户（可选，不强制要求认证）"""
    if not credentials:
        return None

    try:
        token = credentials.credentials
        payload = JWTService.verify_token(token)

        user_id = payload.get("user_id")
        user = await User.get_or_none(id=user_id, status="active").prefetch_related(
            "role"
        )

        return user
    except HTTPException:
        return None
    except Exception:
        return None


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
) -> User:
    """获取当前用户（必须认证）"""
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要认证",
            headers={"WWW-Authenticate": "Bearer"},
        )

    token = credentials.credentials
    payload = JWTService.verify_token(token)

    user_id = payload.get("user_id")
    user = await User.get_or_none(id=user_id, status="active").prefetch_related("role")

    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="用户不存在或已被禁用"
        )

    return user


async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    """获取当前活跃用户"""
    if current_user.status != "active":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="用户账户未激活"
        )
    return current_user


async def get_current_admin_user(
    current_user: User = Depends(get_current_active_user),
) -> User:
    """获取当前管理员用户"""
    if current_user.role.name != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="需要管理员权限"
        )
    return current_user


def require_permissions(required_permissions: List[str]):
    """权限验证装饰器"""

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从kwargs中获取current_user
            current_user = kwargs.get("current_user")
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED, detail="未认证用户"
                )

            # 检查用户权限
            user_permissions = await current_user.get_permissions()

            # 检查是否有所需权限
            missing_permissions = []
            for permission in required_permissions:
                if permission not in user_permissions:
                    missing_permissions.append(permission)

            if missing_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"缺少权限: {', '.join(missing_permissions)}",
                )

            return await func(*args, **kwargs)

        return wrapper

    return decorator


def require_permission(permission: str):
    """单个权限验证装饰器"""
    return require_permissions([permission])


class PermissionChecker:
    """权限检查器"""

    def __init__(self, permission: str):
        self.permission = permission

    async def __call__(self, current_user: User = Depends(get_current_active_user)):
        user_permissions = await current_user.get_permissions()
        if self.permission not in user_permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"缺少权限: {self.permission}",
            )
        return current_user


# 常用权限检查器实例
RequireUserManage = PermissionChecker(Permissions.USER_MANAGE)
RequireResourceManage = PermissionChecker(Permissions.RESOURCE_MANAGE)
RequireFeedbackManage = PermissionChecker(Permissions.FEEDBACK_MANAGE)
RequireSystemConfig = PermissionChecker(Permissions.SYSTEM_CONFIG)


async def check_resource_access(
    resource_id: int, current_user: Optional[User] = None
) -> bool:
    """检查用户是否有访问特定资源的权限"""
    from app.models.resource import PanResource

    # 获取资源
    resource = await PanResource.get_or_none(id=resource_id)
    if not resource:
        return False

    # 如果是公开资源，任何人都可以访问
    if not resource.is_mine:
        return True

    # 如果是私有资源，需要登录用户
    if not current_user:
        return False

    # 管理员可以访问所有资源
    if current_user.role.name == "admin":
        return True

    # 资源所有者可以访问
    # 这里需要根据实际业务逻辑来判断资源所有权
    # 暂时返回True，后续可以扩展
    return True


async def check_feedback_access(feedback_id: int, current_user: User) -> bool:
    """检查用户是否有访问特定反馈的权限"""
    from app.models.feedback import ResourceFeedback

    # 获取反馈
    feedback = await ResourceFeedback.get_or_none(id=feedback_id).prefetch_related(
        "user"
    )
    if not feedback:
        return False

    # 管理员可以访问所有反馈
    if current_user.role.name == "admin":
        return True

    # 反馈创建者可以访问自己的反馈
    if feedback.user and feedback.user.id == current_user.id:
        return True

    return False


# 默认角色权限配置
DEFAULT_ROLE_PERMISSIONS = {
    "admin": [
        Permissions.USER_MANAGE,
        Permissions.USER_VIEW,
        Permissions.RESOURCE_MANAGE,
        Permissions.RESOURCE_SUBMIT,
        Permissions.RESOURCE_VIEW,
        Permissions.FEEDBACK_MANAGE,
        Permissions.FEEDBACK_CREATE,
        Permissions.FEEDBACK_VIEW,
        Permissions.SYSTEM_CONFIG,
        Permissions.SYSTEM_STATS,
        Permissions.PROFILE_MANAGE,
    ],
    "user": [
        Permissions.RESOURCE_SUBMIT,
        Permissions.RESOURCE_VIEW,
        Permissions.FEEDBACK_CREATE,
        Permissions.FEEDBACK_VIEW,
        Permissions.PROFILE_MANAGE,
    ],
    "guest": [
        Permissions.RESOURCE_VIEW,
        Permissions.FEEDBACK_VIEW,
    ],
}
