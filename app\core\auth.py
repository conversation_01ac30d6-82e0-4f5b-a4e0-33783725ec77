import os
import jwt
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from fastapi import HTTPEx<PERSON>, status
from app.utils.config import settings

# JWT配置
JWT_CONFIG = {
    "algorithm": "HS256",
    "access_token_expire_minutes": 30,
    "refresh_token_expire_days": 7,
    "secret_key": os.getenv(
        "JWT_SECRET_KEY", settings.get("auth.jwt_secret_key", "your-secret-key")
    ),
    "issuer": "pan-so-backend",
}


class JWTService:
    """JWT认证服务"""

    @staticmethod
    def create_access_token(data: Dict[str, Any]) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(
            minutes=JWT_CONFIG["access_token_expire_minutes"]
        )
        to_encode.update(
            {
                "exp": expire,
                "iat": datetime.utcnow(),
                "iss": JWT_CONFIG["issuer"],
                "type": "access",
            }
        )
        return jwt.encode(
            to_encode, JWT_CONFIG["secret_key"], algorithm=JWT_CONFIG["algorithm"]
        )

    @staticmethod
    def create_refresh_token(data: Dict[str, Any]) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(
            days=JWT_CONFIG["refresh_token_expire_days"]
        )
        to_encode.update(
            {
                "exp": expire,
                "iat": datetime.utcnow(),
                "iss": JWT_CONFIG["issuer"],
                "type": "refresh",
            }
        )
        return jwt.encode(
            to_encode, JWT_CONFIG["secret_key"], algorithm=JWT_CONFIG["algorithm"]
        )

    @staticmethod
    def verify_token(token: str) -> Optional[Dict[str, Any]]:
        """验证令牌"""
        try:
            payload = jwt.decode(
                token,
                JWT_CONFIG["secret_key"],
                algorithms=[JWT_CONFIG["algorithm"]],
                issuer=JWT_CONFIG["issuer"],
            )
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="令牌已过期"
            )
        except jwt.InvalidTokenError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="无效的令牌"
            )

    @staticmethod
    def decode_token_without_verification(token: str) -> Optional[Dict[str, Any]]:
        """解码令牌但不验证（用于获取过期令牌信息）"""
        try:
            return jwt.decode(token, options={"verify_signature": False})
        except Exception:
            return None


class AuthService:
    """认证服务"""

    @staticmethod
    async def authenticate_user(username_or_email: str, password: str):
        """用户认证"""
        from app.models.user import User

        # 查找用户（支持用户名或邮箱登录）
        user = (
            await User.filter(username=username_or_email)
            .prefetch_related("role")
            .first()
        )

        if not user:
            # 如果用户名没找到，尝试用邮箱查找
            user = (
                await User.filter(email=username_or_email)
                .prefetch_related("role")
                .first()
            )

        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="用户名或密码错误"
            )

        # 检查账户状态
        if user.status == "deleted":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="账户已被删除"
            )

        if user.status == "frozen":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="账户已被冻结，请联系管理员",
            )

        # 检查账户锁定状态
        if user.is_locked():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="账户已被锁定，请稍后再试",
            )

        # 验证密码
        if not user.verify_password(password):
            # 增加登录失败次数
            user.login_attempts += 1
            if user.login_attempts >= 5:
                user.lock_account(30)  # 锁定30分钟
            await user.save()

            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="用户名或密码错误"
            )

        # 登录成功，重置失败次数
        user.login_attempts = 0
        user.last_login_at = datetime.utcnow()
        await user.save()

        return user

    @staticmethod
    async def create_user_tokens(user) -> Dict[str, str]:
        """为用户创建令牌"""
        from app.models.user import UserSession
        import secrets

        # 确保加载了角色关联数据
        await user.fetch_related("role")

        # 获取用户权限
        permissions = await user.get_permissions()

        # 创建令牌数据
        token_data = {
            "user_id": user.id,
            "username": user.username,
            "email": user.email,
            "role": user.role.name,
            "permissions": permissions,
        }

        # 生成令牌
        access_token = JWTService.create_access_token(token_data)
        refresh_token = JWTService.create_refresh_token({"user_id": user.id})

        # 创建会话记录
        session_token = secrets.token_urlsafe(32)
        session = await UserSession.create(
            user=user,
            session_token=session_token,
            refresh_token=refresh_token,
            expires_at=datetime.utcnow()
            + timedelta(days=JWT_CONFIG["refresh_token_expire_days"]),
        )

        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": JWT_CONFIG["access_token_expire_minutes"] * 60,
        }

    @staticmethod
    async def refresh_access_token(refresh_token: str) -> Dict[str, str]:
        """刷新访问令牌"""
        from app.models.user import UserSession, User

        # 验证刷新令牌
        payload = JWTService.verify_token(refresh_token)
        if payload.get("type") != "refresh":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="无效的刷新令牌"
            )

        user_id = payload.get("user_id")

        # 查找会话
        session = (
            await UserSession.filter(refresh_token=refresh_token, is_active=True)
            .prefetch_related("user__role")
            .first()
        )

        if not session or session.is_expired():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="刷新令牌已过期"
            )

        user = session.user

        # 检查用户状态
        if user.status != "active":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="用户账户异常"
            )

        # 确保加载了角色关联数据
        await user.fetch_related("role")

        # 获取用户权限
        permissions = await user.get_permissions()

        # 创建新的访问令牌
        token_data = {
            "user_id": user.id,
            "username": user.username,
            "email": user.email,
            "role": user.role.name,
            "permissions": permissions,
        }

        access_token = JWTService.create_access_token(token_data)

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": JWT_CONFIG["access_token_expire_minutes"] * 60,
        }

    @staticmethod
    async def logout_user(refresh_token: str):
        """用户登出"""
        from app.models.user import UserSession

        # 查找并禁用会话
        session = await UserSession.filter(refresh_token=refresh_token).first()
        if session:
            session.is_active = False
            await session.save()

    @staticmethod
    async def logout_all_sessions(user_id: int):
        """登出用户的所有会话"""
        from app.models.user import UserSession

        await UserSession.filter(user_id=user_id).update(is_active=False)
