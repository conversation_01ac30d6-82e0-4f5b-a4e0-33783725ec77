import yaml
from functools import reduce


class Settings:
    _config = None
    _instance = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(Settings, cls).__new__(cls, *args, **kwargs)
        return cls._instance

    def __init__(self, config_file="app/config.yaml"):
        if self._config is None:
            self.load_config(config_file)

    def load_config(self, config_file):
        with open(config_file, "r", encoding="utf-8") as f:
            self._config = yaml.safe_load(f)

    def get(self, key, default=None):
        """
        获取配置项, 支持 a.b.c 的方式获取
        :param key:
        :param default:
        :return:
        """
        try:
            # 使用 reduce 函数来深入嵌套的字典
            return reduce(lambda d, k: d[k], key.split("."), self._config)
        except (KeyError, TypeError):
            return default


# 使用不同的名字创建实例，避免命名冲突
settings = Settings()
