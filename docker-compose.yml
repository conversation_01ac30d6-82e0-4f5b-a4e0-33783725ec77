services:
  # PostgreSQL 服务
  postgres:
    image: postgres:14-alpine  # 使用一个较新的、轻量的PostgreSQL镜像
    container_name: pan_so_postgres
    environment:
      - POSTGRES_USER=pan_so_user  # 设置数据库用户名
      - POSTGRES_PASSWORD=Wsk1998107.  # 设置数据库密码 (请修改为您自己的密码)
      - POSTGRES_DB=pan_so_db  # 自动创建的数据库名称
    ports:
      - "5432:5432"  # 将容器的5432端口映射到您主机的5432端口
    volumes:
      - postgres_data:/var/lib/postgresql/data  # 将数据持久化到Docker卷中，防止容器关闭后数据丢失
    restart: unless-stopped

  # Redis 服务
  redis:
    image: redis:7-alpine  # 使用一个轻量的Redis镜像
    container_name: pan_so_redis
    ports:
      - "6379:6379"  # 将容器的6379端口映射到您主机的6379端口
    restart: unless-stopped

  # Meilisearch 服务
  meilisearch:
    image: getmeili/meilisearch:latest  # 官方Meilisearch镜像
    container_name: pan_so_meilisearch
    environment:
      - MEILI_MASTER_KEY=masterKey  # 与config.yaml保持一致，生产环境请更换为强密码
    ports:
      - "7700:7700"  # 映射Meilisearch默认端口
    restart: unless-stopped

volumes:
  postgres_data:  # 定义一个卷来持久化数据库数据 