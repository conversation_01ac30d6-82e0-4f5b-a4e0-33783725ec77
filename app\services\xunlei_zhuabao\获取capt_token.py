'''
Author: dkon <EMAIL>
Date: 2025-05-17 22:45:08
LastEditors: dkon <EMAIL>
LastEditTime: 2025-05-17 22:45:12
FilePath: \pan-so-backend\app\services\获取capt_token.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
import requests
import json

url = "https://xluser-ssl.xunlei.com/v1/shield/captcha/init"

payload = json.dumps({
   "client_id": "Xqp0kJBXWhwaTpB6",
   "action": "get:/drive/v1/share",
   "device_id": "8288479c4fb9ebed09ad6bb8e2605cae",
   "meta": {
      "username": "",
      "phone_number": "",
      "email": "",
      "package_name": "pan.xunlei.com",
      "client_version": "1.91.20",
      "captcha_sign": "1.d1613db48ac305887b729994ed88a456",
      "timestamp": "1747477736666",
      "user_id": "0"
   }
})
headers = {
   'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
   'Content-Type': 'application/json',
   'Accept': '*/*',
   'Host': 'xluser-ssl.xunlei.com',
   'Connection': 'keep-alive'
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)


"""响应内容
{
    "captcha_token": "ck0.8B7rW4mzhc-0lLkAYupkoP8Y8ZKv49dcKitXXC_ovq0_ON7qpFk9FmjUjIFADFH4dRzq2wl3txW075e5qiz6oiJdaubXz3CD41VHFteIsk1queQnDlurK5YYbmQTzUptRdJnn37oLgLhxjRT4xFDwd_PyVfV-enGYOx_jCIzYDvF0wgxK09QAcDidigfocm6thSLkNEnQMn4_SYyrlBI5zyJDxThmab4mbf92IQB0udxeqeVZGZca56NVTvp88uz9NaHT2r6S7R-l0yFN3aQmGyOJ6omrkxme17yoM-1uUfMF5IZxf75UGlJZ0KfuyjTwSoMNatB0JjLRLIyGJZX7jab-I1ANA735VZNOKR-Xd6-SUeJiVRkgAG693OOh_a-HpjUiT5yGDtezejNqErYAUsKKTc0WxsGH62_-5Hkif8.ClQIyNfI9e0yEhBYcXAwa0pCWFdod2FUcEI2GgcxLjkxLjIwIg5wYW4ueHVubGVpLmNvbSogODI4ODQ3OWM0ZmI5ZWJlZDA5YWQ2YmI4ZTI2MDVjYWUSgAGhfeu65cPndBa-Hdfy2-0oqqNryI-swwChlQ0cY56LGq2yUj1FvqWuzh8GCe7RJazQBUT9jhOsiMIkxRL8mJxkWdf0C1UczEVnYPrQEv2b7QwOlAm9LIxWrHBKdYQCUjm2gkM-lBEu2WhFPoWLqI55AkvtYIx9jHqZjXzXFSf4zg",
    "expires_in": 300
}
"""