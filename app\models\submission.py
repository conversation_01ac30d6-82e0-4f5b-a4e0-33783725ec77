import uuid
from tortoise import fields
from tortoise.models import Model
from app import models
from app.models.enums import BatchStatus, TaskStatus
from app.models.resource import PanResource


class SubmissionBatch(Model):
    """记录一次资源提交批处理的整体情况"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="最后更新时间")
    status = fields.CharEnumField(
        BatchStatus, default=BatchStatus.PENDING, description="批处理状态"
    )
    total_urls_submitted = fields.IntField(default=0, description="本次提交的总URL数量")
    tasks_created = fields.IntField(default=0, description="为此批次创建的处理任务数量")
    successful_tasks = fields.IntField(default=0, description="成功处理的任务数量")
    failed_tasks = fields.IntField(default=0, description="失败处理的任务数量")
    skipped_tasks = fields.IntField(default=0, description="因重复等原因跳过的任务数量")

    tasks: fields.ReverseRelation["models.SubmissionTask"]

    class Meta:
        table = "submission_batches"
        ordering = ["-created_at"]

    def __str__(self):
        return f"SubmissionBatch({self.id}) - {self.status}"


class SubmissionTask(Model):
    """记录单个资源URL的处理任务"""

    id = fields.UUIDField(pk=True, default=uuid.uuid4)
    batch: fields.ForeignKeyRelation[SubmissionBatch] = fields.ForeignKeyField(
        "models.SubmissionBatch", related_name="tasks", description="所属批处理"
    )
    resource: fields.ForeignKeyNullableRelation[PanResource] = fields.ForeignKeyField(
        "models.PanResource",
        related_name="submission_tasks",
        null=True,
        on_delete=fields.SET_NULL,
        description="关联的资源条目",
    )
    original_url = fields.TextField(description="提交的原始URL")
    parsed_pan_type_int = fields.IntField(
        null=True, description="解析出的网盘类型整数值"
    )
    parsed_resource_key = fields.CharField(
        max_length=255, null=True, description="解析出的资源Key"
    )
    parsed_share_pwd = fields.CharField(
        max_length=100, null=True, description="解析出的分享密码"
    )

    status = fields.CharEnumField(
        TaskStatus, default=TaskStatus.ACCEPTED, description="任务处理状态"
    )
    error_message = fields.TextField(null=True, description="处理失败时的错误信息")
    attempts = fields.IntField(default=0, description="处理尝试次数")

    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="最后更新时间")

    class Meta:
        table = "submission_tasks"
        ordering = ["created_at"]

    def __str__(self):
        return (
            f"SubmissionTask({self.id}) - {self.original_url[:30]}... - {self.status}"
        )
