import meilisearch
from app.utils.config import settings
from typing import List, Dict, Any


# Meilisearch服务封装，负责索引、检索、同步等操作
class MeiliSearchService:
    def __init__(self):
        # 从配置加载Meilisearch参数
        meili_conf = settings.get("meilisearch", {})
        self.host = meili_conf.get("host", "http://127.0.0.1:7700")
        self.api_key = meili_conf.get("api_key", "masterKey")
        self.index_name = meili_conf.get("index_name", "resources")
        # 初始化客户端
        self.client = meilisearch.Client(self.host, self.api_key)
        self.index = self.client.index(self.index_name)

    def search(self, query: str, limit: int = 2000) -> List[str]:
        """
        使用Meilisearch进行检索，返回候选资源ID列表
        """
        try:
            result = self.index.search(
                query, {"limit": limit, "attributesToRetrieve": ["resource_key"]}
            )
            return [hit["resource_key"] for hit in result.get("hits", [])]
        except Exception as e:
            # 检索异常时返回空列表
            return []

    def add_or_update_documents(self, docs: List[Dict[str, Any]]):
        """
        批量新增或更新资源到索引
        """
        try:
            result = self.index.add_documents(docs)
            print(f"add_documents返回: {result}")
            return result
        except Exception as e:
            print(f"写入Meilisearch异常: {e}")
            raise

    def delete_document(self, resource_key: str):
        """
        删除指定资源
        """
        try:
            self.index.delete_document(resource_key)
        except Exception as e:
            pass


# 单例服务对象
meilisearch_service = MeiliSearchService()
