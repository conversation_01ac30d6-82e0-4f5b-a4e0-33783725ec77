import logging
import httpx
from app.utils.config import settings

logger = logging.getLogger(__name__)


class IndexNowService:
    """
    封装与 IndexNow API 交互的服务。
    """

    def __init__(self):
        self.api_url = "https://api.indexnow.org/IndexNow"
        self.host = settings.get("seo.site_host")
        self.api_key = settings.get("seo.indexnow_api_key")
        key_file = settings.get("seo.indexnow_key_file_name")
        self.key_location = (
            f"https://{self.host}/{key_file}" if self.host and key_file else None
        )
        self.session = None
        self._session_created = False

    async def _get_session(self):
        """
        获取或创建 HTTP 客户端会话。
        """
        if self.session is None:
            self.session = httpx.AsyncClient(
                timeout=30.0,
                follow_redirects=True,
                limits=httpx.Limits(
                    max_connections=10,
                    max_keepalive_connections=5,
                ),
            )
            self._session_created = True
            logger.debug("创建了新的 IndexNow HTTP 客户端会话。")
        return self.session

    async def submit_urls(self, url_list: list[str]) -> bool:
        """
        向 IndexNow API 提交 URL 列表。

        :param url_list: 要提交的 URL 字符串列表。
        :return: 提交是否成功。
        """
        if not all([self.host, self.api_key, self.key_location]):
            logger.error(
                "IndexNow 服务配置不完整，无法提交。请检查 config.yaml 中的 seo 设置。"
            )
            return False

        if not url_list:
            logger.info("要提交的 URL 列表为空，跳过 IndexNow 提交。")
            return True

        payload = {
            "host": self.host,
            "key": self.api_key,
            "keyLocation": self.key_location,
            "urlList": url_list,
        }

        try:
            session = await self._get_session()
            response = await session.post(self.api_url, json=payload)
            response.raise_for_status()

            logger.info(f"成功向 IndexNow 提交 {len(url_list)} 个 URL。")
            return True
        except httpx.HTTPStatusError as e:
            logger.error(
                f"向 IndexNow API 提交失败。状态码: {e.response.status_code}, 响应: {e.response.text}"
            )
            if e.response.status_code == 400:
                logger.error("错误详情：请求格式无效 (Bad request)。")
            elif e.response.status_code == 403:
                logger.error(
                    "错误详情：API 密钥无效或未找到 (Forbidden)。请检查 key 和 keyLocation 是否正确。"
                )
            elif e.response.status_code == 422:
                logger.error(
                    "错误详情：URL 列表包含不属于该主机的 URL (Unprocessable Entity)。"
                )
            elif e.response.status_code == 429:
                logger.error("错误详情：请求过于频繁 (Too Many Requests)。")
            return False
        except httpx.RequestError as e:
            logger.error(f"向 IndexNow API 发送请求时发生网络错误: {e}")
            return False

    async def close(self):
        """
        关闭 httpx 客户端会话。
        """
        if self.session is not None and self._session_created:
            try:
                await self.session.aclose()
                logger.debug("IndexNowService 的 httpx 会话已关闭。")
            except Exception as e:
                logger.warning(f"关闭 IndexNowService 会话时发生错误: {e}")
            finally:
                self.session = None
                self._session_created = False
