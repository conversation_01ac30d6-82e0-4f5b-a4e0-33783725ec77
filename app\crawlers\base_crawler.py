import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any

from app.models.pydantic_models import CrawledResource
from app.utils.config import settings


class BaseCrawler(ABC):
    """
    所有爬虫的抽象基类，定义了标准接口和提供通用功能。

    每个爬虫子类都需要实现 `crawl` 方法作为一次性抓取的核心逻辑，
    或者实现 `start_monitoring` 方法作为持续监控的核心逻辑。

    通过在子类的 `__init__` 方法中调用 `super().__init__('crawler_name')`，
    可以自动加载 `config.yaml` 中对应的配置段 (如 `crawler_name_crawler`)，
    并初始化一个名为 `crawler_name` 的logger。
    """

    def __init__(self, crawler_name: str):
        """
        初始化基类。
        :param crawler_name: 爬虫的唯一名称，用于日志和配置。
        """
        self.crawler_name = crawler_name
        # 自动从 config.yaml 加载配置, 例如 crawler_name='kdocs' -> settings.get('kdocs_crawler', {})
        self.config = settings.get(f"{self.crawler_name}_crawler", {})
        self.logger = logging.getLogger(self.crawler_name)
        self.enabled = self.config.get("enabled", False)

    @abstractmethod
    async def crawl(self, **kwargs) -> List[CrawledResource]:
        """
        【一次性抓取】的核心逻辑，子类可以选择实现此方法。
        负责从目标网站抓取原始数据，并返回一个 CrawledResource 列表。
        """
        raise NotImplementedError(
            f"{self.__class__.__name__} 未实现 'crawl' 方法，适用于一次性抓取任务。"
        )

    async def run_crawl(self, **kwargs):
        """
        【一次性抓取】的统一执行入口。
        封装了启用检查和日志记录，然后调用 `crawl` 方法。
        """
        if not self.enabled:
            self.logger.info(f"爬虫 '{self.crawler_name}' 在配置中被禁用，跳过执行。")
            return []

        self.logger.info(f"开始执行一次性抓取任务: {self.crawler_name}")
        try:
            results = await self.crawl(**kwargs)
            self.logger.info(
                f"爬虫 '{self.crawler_name}' 任务完成，抓取到 {len(results)} 条数据。"
            )
            return results
        except Exception as e:
            self.logger.error(
                f"爬虫 '{self.crawler_name}' 执行出错: {e}", exc_info=True
            )
            return []

    @abstractmethod
    async def start_monitoring(self):
        """
        【持续监控】的核心逻辑，子类可以选择实现此方法。
        例如，启动一个 Telegram 客户端来监听新消息。
        """
        raise NotImplementedError(
            f"{self.__class__.__name__} 未实现 'start_monitoring' 方法，适用于持续监控任务。"
        )

    async def run_monitor(self):
        """
        【持续监控】的统一执行入口。
        封装了启用检查，然后调用 `start_monitoring`。
        """
        if not self.enabled:
            self.logger.info(f"监控器 '{self.crawler_name}' 在配置中被禁用，跳过启动。")
            return

        self.logger.info(f"准备启动持续监控: {self.crawler_name}")
        try:
            await self.start_monitoring()
            self.logger.info(f"监控器 '{self.crawler_name}' 已正常停止。")
        except Exception as e:
            # 对于一个长期运行的任务，这里的错误通常是致命的
            self.logger.critical(
                f"监控器 '{self.crawler_name}' 遇到致命错误并退出: {e}",
                exc_info=True,
            )
