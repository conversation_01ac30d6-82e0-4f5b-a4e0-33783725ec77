#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
用于手动触发Kdocs爬虫Celery任务的独立脚本。
"""
import os
import sys

# 将项目根目录添加到 Python 路径中
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

from app.tasks.crawler_tasks import run_kdocs_crawl

if __name__ == "__main__":
    try:
        print("开始触发Kdocs爬虫任务...")
        # 通过Celery的delay方法异步触发任务
        run_kdocs_crawl.delay()
        print("Kdocs爬虫任务已成功放入Celery队列。")
    except Exception as e:
        print(f"触发Kdocs爬虫任务失败: {e}")
