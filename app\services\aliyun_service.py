import asyncio
import httpx
import logging
from typing import Dict, Any, Optional, List
from app.utils.config import settings
import datetime
import time
import math
from app.models.resource import PanResource
from tortoise.expressions import Q
from datetime import datetime, timedelta, timezone

logger = logging.getLogger("aliyun-pan-service")

# 定义北京时间时区（UTC+8）
BEIJING_TIMEZONE = timezone(timedelta(hours=8))

PAN_CONF = settings.get("pan_service", {})
DEFAULT_TIMEOUT = PAN_CONF.get("default_timeout", 30.0)
MAX_RETRIES = PAN_CONF.get("max_retries", 3)
RETRY_DELAY = PAN_CONF.get("retry_delay", 1.0)

DEFAULT_HEADERS = {
    "accept": "application/json, text/plain, */*",
    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
    "content-type": "application/json",
    "origin": "https://www.aliyundrive.com",
    "referer": "https://www.aliyundrive.com/",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
    "x-canary": "client=web,app=share,version=v2.3.1",
}


class AliyunPanService:
    """
    阿里云盘服务类，提供阿里云盘的相关操作
    目前仅支持：验证分享链接有效性
    """

    def __init__(self):
        """初始化阿里云盘服务"""
        logger.info("初始化阿里云盘服务...")
        self.base_url = "https://api.aliyundrive.com"
        self.headers = DEFAULT_HEADERS.copy()

    async def initialize(self):
        """初始化服务会话"""
        logger.info("初始化阿里网盘服务会话...")
        self.session = httpx.AsyncClient(
            timeout=DEFAULT_TIMEOUT,
            follow_redirects=True,
            http2=True,
            limits=httpx.Limits(
                max_connections=100,
                max_keepalive_connections=50,
            ),
            verify=False,
            transport=httpx.AsyncHTTPTransport(retries=MAX_RETRIES, verify=False),
        )
        logger.info("阿里网盘服务会话初始化完成")

    async def check_resource_status(self, share_url: str) -> dict:
        """
        检查阿里云盘分享资源状态
        :param share_id: 分享ID
        :return: 资源状态信息
        """
        try:
            url = f"{self.base_url}/adrive/v3/share_link/get_share_by_anonymous"
            share_id = self.extract_share_id(share_url)
            payload = {"share_id": share_id}

            async with httpx.AsyncClient(
                timeout=DEFAULT_TIMEOUT, verify=False
            ) as client:
                response = await client.post(url, json=payload, headers=self.headers)

                if response.status_code == 200:
                    data = response.json()
                    return {"status": "success", "message": "资源有效", "valid": True}
                elif response.status_code == 400:
                    return {
                        "status": "error",
                        "message": "资源不存在或已失效",
                        "valid": False,
                    }
                else:
                    return {
                        "status": "error",
                        "message": f"请求失败: HTTP {response.status_code}",
                        "valid": False,
                    }

        except httpx.RequestError as e:
            logger.error(f"请求阿里云盘API失败: {str(e)}")
            return {
                "status": "error",
                "message": f"网络请求失败: {str(e)}",
                "data": None,
            }
        except Exception as e:
            logger.error(f"验证阿里云盘资源时发生错误: {str(e)}")
            return {"status": "error", "message": f"未知错误: {str(e)}", "data": None}

    async def fetch_and_update_resource_details(
        self, resource: PanResource, is_parsed: bool = True
    ) -> bool:
        """
        从阿里云盘获取资源详情并更新资源对象
        """
        logger.info(
            f"开始获取并更新阿里云盘资源详情: ID={resource.id}, 原始链接={resource.original_url}"
        )
        start_time = time.time()

        try:
            resource_details = await self._get_resource_details(resource)
            if resource_details.get("status") != "success":
                logger.error(
                    f"获取阿里云盘资源详情失败: {resource_details.get('message', '未知错误')}"
                )
                return False

            # Update PanResource object
            # 注意：不再强制设置is_parsed=True，保持用户提交时设置的值
            resource.verified_status = "valid"

            # 根据is_parsed参数决定是否填充字段
            if is_parsed:
                # is_parsed=True: 填充完整字段
                resource.share_url = resource.original_url
                resource.title = resource_details.get("title", resource.title)
                resource.author = resource_details.get("author_name", resource.author)
                resource.author_avatar = resource_details.get("author_avatar", "")

                file_list = resource_details.get("file_list", [])
                resource.text_content = "\n".join(
                    [f"{item['name']} ({item['size']})" for item in file_list]
                )

                resource.file_type = resource_details.get("file_type", "其他")
                resource.file_size = resource_details.get("file_size_str", "0B")

                updated_at_str = resource_details.get("updated_at")
                if updated_at_str:
                    resource.updated_at = datetime.fromisoformat(
                        updated_at_str.replace("Z", "+00:00")
                    )

                expiration_str = resource_details.get("expiration")
                if expiration_str:
                    try:
                        resource.expiry_date = datetime.fromisoformat(
                            expiration_str.replace("Z", "+00:00")
                        )
                    except ValueError:
                        logger.warning(f"无法解析的过期时间格式: {expiration_str}")
                        resource.expiry_date = None

                logger.info(f"is_parsed=True: 填充完整字段，author={resource.author}")
            else:
                # is_parsed=False: 仅填充基本字段，author设为97_bot
                resource.author = "97_bot"
                resource.title = resource_details.get("title", resource.title)

                file_list = resource_details.get("file_list", [])
                resource.text_content = "\n".join(
                    [f"{item['name']} ({item['size']})" for item in file_list]
                )

                resource.file_type = resource_details.get("file_type", "其他")
                resource.file_size = resource_details.get("file_size_str", "0B")

                updated_at_str = resource_details.get("updated_at")
                if updated_at_str:
                    resource.updated_at = datetime.fromisoformat(
                        updated_at_str.replace("Z", "+00:00")
                    )
                # 不填充share_url, expiry_date和author_avatar
                logger.info(f"is_parsed=False: 仅填充基本字段，author=97_bot")

            if resource.created_at is None:
                resource.created_at = datetime.now(BEIJING_TIMEZONE)
                logger.info(f"设置创建时间: {resource.created_at}")

            # 根据is_parsed参数决定更新哪些字段
            if is_parsed:
                # is_parsed=True: 更新所有字段
                await resource.save(
                    update_fields=[
                        "verified_status",
                        "share_url",
                        "title",
                        "author",
                        "author_avatar",
                        "text_content",
                        "file_type",
                        "file_size",
                        "updated_at",
                        "expiry_date",
                        "created_at",
                    ]
                )
            else:
                # is_parsed=False: 仅更新基本字段
                await resource.save(
                    update_fields=[
                        "verified_status",
                        "title",
                        "author",  # 设置为97_bot
                        "text_content",
                        "file_type",
                        "file_size",
                        "updated_at",
                        "created_at",
                        # 不更新share_url, expiry_date和author_avatar
                    ]
                )

            end_time = time.time()
            logger.info(
                f"资源 {resource.resource_key} 成功保存到阿里云盘数据库, 耗时: {end_time - start_time:.2f}秒"
            )
            return True

        except Exception as e:
            logger.error(f"更新阿里云盘资源详情时发生错误: {str(e)}")
            import traceback

            logger.error(traceback.format_exc())
            return False

    async def _get_resource_details(self, resource: PanResource) -> dict:
        """
        获取阿里云盘资源的详细信息
        """
        share_id = self.extract_share_id(resource.original_url)
        if not share_id:
            return {"status": "error", "message": "无法从链接中提取share_id"}

        async with httpx.AsyncClient(
            timeout=DEFAULT_TIMEOUT, verify=False, follow_redirects=True
        ) as client:
            try:
                # 1. 获取分享信息
                share_info = await self._get_share_info_anonymous(client, share_id)
                if "code" in share_info:
                    return {
                        "status": "error",
                        "message": share_info.get("message", "获取分享信息失败"),
                    }

                # 2. 获取分享token
                token_result = await self._get_share_token(
                    client, share_id, resource.share_pwd
                )
                if token_result.get("status") == "error":
                    return token_result
                share_token = token_result.get("share_token")

                # 3. 获取所有文件
                all_files = await self._get_all_files_recursive(
                    client, share_id, share_token
                )
                logger.info(f"获取到所有文件: {len(all_files)}个")

                # 4. 处理并返回详情
                total_size = sum(file.get("size", 0) for file in all_files)

                from collections import Counter

                categories = [file.get("category", "other") for file in all_files]
                if categories:
                    most_common_category = Counter(categories).most_common(1)[0][0]
                else:
                    most_common_category = "other"

                file_type_map = {
                    "video": "视频",
                    "audio": "音频",
                    "image": "图片",
                    "doc": "文档",
                    "zip": "压缩包",
                    "other": "其他",
                    "app": "应用",
                }
                file_type = file_type_map.get(most_common_category, "其他")

                details = {
                    "status": "success",
                    "title": share_info.get("share_name"),
                    "author_name": share_info.get("creator_name"),
                    "author_avatar": share_info.get("avatar"),
                    "updated_at": share_info.get("updated_at"),
                    "expiration": share_info.get("expiration"),
                    "file_list": all_files,
                    "file_size": total_size,
                    "file_size_str": self._format_size(total_size),
                    "file_type": file_type,
                }
                return details
            except Exception as e:
                logger.error(f"获取阿里云盘资源详情时发生异常: {e}")
                import traceback

                logger.error(traceback.format_exc())
                return {"status": "error", "message": str(e)}

    async def _get_share_info_anonymous(
        self, client: httpx.AsyncClient, share_id: str
    ) -> dict:
        url = f"{self.base_url}/adrive/v3/share_link/get_share_by_anonymous"
        payload = {"share_id": share_id}
        try:
            response = await client.post(url, json=payload, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            logger.error(
                f"获取分享信息失败: {e.response.status_code}, {e.response.text}"
            )
            try:
                return e.response.json()
            except:
                return {
                    "code": str(e.response.status_code),
                    "message": "获取分享信息失败",
                }
        except Exception as e:
            logger.error(f"获取分享信息时发生错误: {str(e)}")
            return {"code": "500", "message": f"未知错误: {str(e)}"}

    async def _get_share_token(
        self, client: httpx.AsyncClient, share_id: str, share_pwd: Optional[str]
    ) -> dict:
        url = f"{self.base_url}/v2/share_link/get_share_token"
        payload = {"share_id": share_id, "share_pwd": share_pwd or ""}
        try:
            response = await client.post(url, json=payload, headers=self.headers)
            response.raise_for_status()
            data = response.json()
            if "share_token" in data:
                return {"status": "success", "share_token": data["share_token"]}
            else:
                logger.warning(f"获取share_token失败: {data}")
                return {
                    "status": "error",
                    "message": data.get("message", "获取share_token失败"),
                }
        except Exception as e:
            logger.error(f"获取share_token时发生错误: {str(e)}")
            return {"status": "error", "message": f"获取share_token失败: {str(e)}"}

    async def _get_all_files_recursive(
        self,
        client: httpx.AsyncClient,
        share_id: str,
        share_token: str,
        parent_file_id: str = "root",
        current_depth: int = 0,
        max_depth: int = 10,  # 增加最大深度限制防止无限递归
    ) -> List[Dict]:
        if current_depth > max_depth:
            logger.warning(
                f"已达到最大递归深度 {max_depth}，在 parent_file_id={parent_file_id} 处停止"
            )
            return []

        logger.info(
            f"正在扫描文件夹: parent_file_id={parent_file_id}, 当前深度={current_depth}"
        )
        files_list: List[Dict] = []
        next_marker = None

        while True:
            try:
                result = await self._list_files_by_share(
                    client, share_id, share_token, parent_file_id, next_marker
                )
                items = result.get("items", [])

                for item in items:
                    if item.get("type") == "folder":
                        sub_folder_files = await self._get_all_files_recursive(
                            client,
                            share_id,
                            share_token,
                            item["file_id"],
                            current_depth + 1,
                            max_depth,
                        )
                        files_list.extend(sub_folder_files)
                    elif item.get("type") == "file":
                        files_list.append(item)

                next_marker = result.get("next_marker")
                if not next_marker:
                    break
            except Exception as e:
                logger.error(
                    f"递归获取文件列表时出错: {e}, parent_file_id: {parent_file_id}"
                )
                break

        return files_list

    async def _list_files_by_share(
        self,
        client: httpx.AsyncClient,
        share_id: str,
        share_token: str,
        parent_file_id: str,
        marker: Optional[str] = None,
    ) -> dict:
        url = f"{self.base_url}/adrive/v2/file/list_by_share"
        payload = {
            "share_id": share_id,
            "parent_file_id": parent_file_id,
            "limit": 100,
            "image_thumbnail_process": "image/resize,w_256/format,jpeg",
            "image_url_process": "image/resize,w_1920/format,jpeg/interlace,1",
            "video_thumbnail_process": "video/snapshot,t_1000,f_jpg,ar_auto,w_256",
            "order_by": "name",
            "order_direction": "ASC",
        }
        if marker:
            payload["marker"] = marker

        headers = self.headers.copy()
        headers["x-share-token"] = share_token

        try:
            response = await client.post(url, json=payload, headers=headers)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"获取文件列表失败: {e}")
            return {}

    async def get_file_list(self, share_id: str, share_pwd: Optional[str] = "") -> dict:
        """
        获取阿里云盘分享文件列表
        :param share_id: 分享ID
        :param share_pwd: 分享密码
        :return: 文件列表
        """
        async with httpx.AsyncClient(timeout=DEFAULT_TIMEOUT, verify=False) as client:
            token_result = await self._get_share_token(client, share_id, share_pwd)
            if token_result.get("status") == "error":
                return token_result
            share_token = token_result.get("share_token")

            all_files = await self._get_all_files_recursive(
                client, share_id, share_token
            )
            return {"status": "success", "files": all_files}

    @staticmethod
    def _format_size(size_bytes: int) -> str:
        if size_bytes <= 0:
            return "0B"
        size_name = ("B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB")
        i = int(math.floor(math.log(size_bytes, 1024)))
        p = math.pow(1024, i)
        s = round(size_bytes / p, 2)
        return f"{s}{size_name[i]}"

    @staticmethod
    def extract_share_id(share_url: str) -> Optional[str]:
        """
        从分享链接中提取share_id
        :param share_url: 分享链接
        :return: share_id 或 None
        """
        # 示例链接格式: https://www.aliyundrive.com/s/GLYnCWfyYZ6
        try:
            if "/s/" in share_url:
                return share_url.split("/s/")[-1].split("?")[0].strip()
            return None
        except Exception:
            return None


def get_aliyun_pan_service():
    """
    获取阿里云盘服务实例
    :return: AliyunPanService实例
    """
    return AliyunPanService()


aliyun_pan_service = get_aliyun_pan_service()


async def test_get_xunlei_user_info():
    """测试获取迅雷网盘用户信息"""
    print("开始测试迅雷网盘用户信息获取...")

    # 获取迅雷网盘服务实例
    aliyun_pan_service = get_aliyun_pan_service()

    # 初始化服务会话
    await aliyun_pan_service.initialize()
    share_result = await aliyun_pan_service.check_resource_status(
        share_url="https://www.aliyundrive.com/s/gWSjdeA2m4v"
    )
    print(f"分享结果: {share_result}")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_get_xunlei_user_info())
