server {
    listen 80;
    server_name **************;  # 修改为实际的后端服务器IP
    # 屏蔽敏感文件访问
    location ~ /\.(git|svn|htaccess) {
        deny all;
    }

    # 屏蔽FastAPI文档接口
    location ~ ^/api/(docs|redoc|openapi.json) {
        return 403;  # 返回禁止访问状态码
    }

    location /api/ {
    proxy_pass http://127.0.0.1:9999;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header Origin "";  # 显式传递空Origin头（如需）
}

    # 静态资源缓存（如果有）
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 7d;
        add_header Cache-Control "public, max-age=604800";
    }

    # 捕获错误信息
    proxy_intercept_errors on;

    error_page 400 401 402 403 404 500 502 503 504 /error.html;
    location = /error.html {
        internal;
        root /var/www/html;  # 请根据实际情况修改
    }
}
