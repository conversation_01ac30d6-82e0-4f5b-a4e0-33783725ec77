from enum import Enum, IntEnum


class PanType(IntEnum):
    """网盘类型枚举"""

    UNKNOWN = 0
    BAIDU = 1
    QUARK = 2
    ALIYUN = 3  # 假设的阿里云盘类型值
    XUNLEI = 4


class BatchStatus(str, Enum):
    """资源提交批处理状态"""

    PENDING = "pending"  # 待处理
    PROCESSING = "processing"  # 正在处理中
    COMPLETED = "completed"  # 全部成功
    PARTIAL_FAILURE = "partial_failure"  # 部分失败
    FAILED = "failed"  # 全部失败


class TaskStatus(str, Enum):
    """单个资源提交任务状态"""

    ACCEPTED = "accepted"  # 已被API接受
    PROCESSING = "processing"
    SUCCESS = "success"
    FAILED_FETCH_DETAILS = "failed_fetch_details"  # 获取详情失败
    FAILED_PARSE_URL = "failed_parse_url"  # URL解析失败
    INVALID_URL = "invalid_url"  # 无效的URL格式或不支持的网盘
    STUCK_REQUEUED = "stuck_requeued"  # 任务卡住并已重新入队
    FAILED_DUPLICATE = "failed_duplicate"  # 重复资源处理失败（历史兼容性）


class TimeFilter(str, Enum):
    """时间过滤选项枚举"""

    ALL = "all"  # 全部时间
    WEEK = "week"  # 最近一周
    HALF_MONTH = "half_month"  # 最近半月
    MONTH = "month"  # 最近1月
    HALF_YEAR = "half_year"  # 最近半年
    YEAR = "year"  # 最近一年


class FeedbackType(str, Enum):
    """反馈类型枚举"""

    EXPIRED = "expired"  # 链接失效
    INVALID = "invalid"  # 资源无效
    BROKEN = "broken"  # 链接损坏
    OTHER = "other"  # 其他问题


class FeedbackStatus(str, Enum):
    """反馈处理状态枚举"""

    PENDING = "pending"  # 待处理
    PROCESSING = "processing"  # 处理中
    RESOLVED = "resolved"  # 已解决
    REJECTED = "rejected"  # 已拒绝


class UserStatus(str, Enum):
    """用户状态枚举"""

    PENDING = "pending"  # 待验证
    ACTIVE = "active"  # 活跃
    FROZEN = "frozen"  # 冻结
    DELETED = "deleted"  # 已删除
