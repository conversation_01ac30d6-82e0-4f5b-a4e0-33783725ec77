# 使用PM2运行Pan-SO后端项目

## PM2简介

PM2是一个进程管理工具，可以帮助您管理和保持应用程序24/7在线。虽然它主要用于Node.js应用，但也可以用来管理Python等其他语言的应用程序。

## 安装前提

1. 安装Node.js和npm
   ```
   # 使用nvm或直接从官网下载安装
   ```

2. 全局安装PM2
   ```
   npm install -g pm2
   ```

## 使用PM2运行Python项目

### 方法一：直接命令行启动（不使用配置文件）

#### 执行 python -m app.main

如果您通常使用 `python -m app.main` 来执行项目，可以使用以下命令：

```bash
pm2 start --name "pan-so-api" -x "python3" -- -m app.main
```

#### 执行 celery服务

```bash
pm2 start /usr/local/bin/celery --name "celery-worker" --cwd /root/pan-so-backend --interpreter python3 -- -A app.core.celery_app.celery_app -b "redis://127.0.0.1:6379/1" worker -l INFO -P gevent --concurrency=1

```

这个命令会以与 `python -m app.main` 相同的方式运行您的应用，但由PM2管理。

#### 执行FastAPI应用

使用以下命令启动FastAPI应用：

```bash
# 基本格式
pm2 start --name "应用名称" --interpreter python 要执行的脚本或命令 [参数]

# 启动FastAPI应用示例
pm2 start --name "pan-so-api" --interpreter python -x -- -m uvicorn app.main:app --host 0.0.0.0 --port 9999
```

说明：
- `--name`: 指定应用名称
- `--interpreter python`: 指定使用Python解释器
- `-x`: 表示后面是解释器的参数
- `--`: 分隔符，后面的参数将传递给脚本

其他常用参数：
- `--watch`: 监视文件变化并自动重启
- `--max-memory-restart <200MB>`: 内存超限时重启
- `--log <log_path>`: 指定日志文件路径
- `--time`: 日志添加时间前缀

### 方法二：使用提供的配置文件启动应用

#### 使用 python -m app.main 的配置文件

创建一个新的配置文件 `pm2-python-module.json`:

```json
{
  "apps": [
    {
      "name": "pan-so-api",
      "script": "python",
      "args": "-m app.main",
      "instances": 1,
      "autorestart": true,
      "watch": false,
      "max_memory_restart": "1G",
      "log_date_format": "YYYY-MM-DD HH:mm:ss"
    }
  ]
}
```

然后使用以下命令启动:

```bash
pm2 start pm2-python-module.json
```

#### 使用现有的配置文件

我们已经为您准备了PM2配置文件：`pm2-app.json`，您可以直接使用以下命令启动应用：

```bash
pm2 start pm2-app.json
```

### 2. 查看应用状态

```bash
pm2 list
```

### 3. 查看日志

```bash
# 查看所有日志
pm2 logs

# 只查看api应用的日志
pm2 logs pan-so-api
```

### 4. 重启应用

```bash
pm2 restart pan-so-api
```

### 5. 停止应用

```bash
pm2 stop pan-so-api
```

### 6. 设置开机自启

为确保服务器重启后应用自动启动：

```bash
# 保存当前运行的进程列表
pm2 save

# 设置开机启动
pm2 startup

# 根据提示运行生成的命令
```

## 配置文件说明

`pm2-app.json`配置了以下内容：

```json
{
  "apps": [
    {
      "name": "pan-so-api",
      "script": "uvicorn",
      "args": "app.main:app --host 0.0.0.0 --port 9999",
      "interpreter": "python",
      "instances": 1,
      "autorestart": true,
      "watch": false,
      "max_memory_restart": "1G",
      "env": {
        "NODE_ENV": "production"
      },
      "log_date_format": "YYYY-MM-DD HH:mm:ss"
    }
  ]
}
```

主要配置说明：
- `name`: 应用名称，便于管理
- `script`: 要运行的脚本/命令
- `args`: 传递给脚本的参数
- `interpreter`: 解释器（这里是python）
- `autorestart`: 崩溃时自动重启
- `max_memory_restart`: 当内存超过指定值时自动重启

## 性能监控

PM2还提供了性能监控功能：

```bash
pm2 monit
```

这将打开一个交互式界面，显示CPU和内存使用情况。 