from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        CREATE TABLE IF NOT EXISTS "pan_resources" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "resource_key" VARCHAR(50) NOT NULL UNIQUE,
    "pan_type" INT NOT NULL,
    "original_url" VARCHAR(255) NOT NULL,
    "title" VARCHAR(255),
    "is_parsed" BOOL NOT NULL  DEFAULT False,
    "author" VA<PERSON><PERSON><PERSON>(255),
    "author_avatar" VARCHAR(512),
    "is_mine" BOOL NOT NULL  DEFAULT False,
    "thumbnail" VARCHAR(512),
    "verified_status" VARCHAR(20),
    "share_url" VARCHAR(255),
    "share_pwd" VARCHAR(128),
    "created_at" TIMESTAMPTZ   DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ,
    "expiry_date" TIMESTAMPTZ,
    "file_type" VARCHAR(20),
    "file_size" VARCHAR(20),
    "access_count" INT NOT NULL  DEFAULT 0,
    "text_content" TEXT
);
COMMENT ON COLUMN "pan_resources"."is_parsed" IS '是否已通过云盘服务解析并填充详细信息';
COMMENT ON COLUMN "pan_resources"."author" IS '资源分享者或作者';
COMMENT ON COLUMN "pan_resources"."author_avatar" IS '作者头像URL';
COMMENT ON COLUMN "pan_resources"."is_mine" IS '是否由本人上传';
COMMENT ON COLUMN "pan_resources"."thumbnail" IS '资源缩略图URL';
CREATE TABLE IF NOT EXISTS "resource_invalid_feedbacks" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "resource_id" VARCHAR(50) NOT NULL,
    "pan_type" INT NOT NULL,
    "invalid_type" INT NOT NULL,
    "description" TEXT,
    "contact_info" VARCHAR(100),
    "is_verified" BOOL NOT NULL  DEFAULT False,
    "verification_result" VARCHAR(20),
    "is_deleted" BOOL NOT NULL  DEFAULT False,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE "resource_invalid_feedbacks" IS '资源失效反馈记录表';
CREATE TABLE IF NOT EXISTS "submission_batches" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "status" VARCHAR(15) NOT NULL  DEFAULT 'pending',
    "total_urls_submitted" INT NOT NULL  DEFAULT 0,
    "tasks_created" INT NOT NULL  DEFAULT 0,
    "successful_tasks" INT NOT NULL  DEFAULT 0,
    "failed_tasks" INT NOT NULL  DEFAULT 0,
    "skipped_tasks" INT NOT NULL  DEFAULT 0
);
COMMENT ON COLUMN "submission_batches"."created_at" IS '创建时间';
COMMENT ON COLUMN "submission_batches"."updated_at" IS '最后更新时间';
COMMENT ON COLUMN "submission_batches"."status" IS '批处理状态';
COMMENT ON COLUMN "submission_batches"."total_urls_submitted" IS '本次提交的总URL数量';
COMMENT ON COLUMN "submission_batches"."tasks_created" IS '为此批次创建的处理任务数量';
COMMENT ON COLUMN "submission_batches"."successful_tasks" IS '成功处理的任务数量';
COMMENT ON COLUMN "submission_batches"."failed_tasks" IS '失败处理的任务数量';
COMMENT ON COLUMN "submission_batches"."skipped_tasks" IS '因重复等原因跳过的任务数量';
COMMENT ON TABLE "submission_batches" IS '记录一次资源提交批处理的整体情况';
CREATE TABLE IF NOT EXISTS "submission_tasks" (
    "id" UUID NOT NULL  PRIMARY KEY,
    "original_url" TEXT NOT NULL,
    "parsed_pan_type_int" INT,
    "parsed_resource_key" VARCHAR(255),
    "parsed_share_pwd" VARCHAR(100),
    "status" VARCHAR(20) NOT NULL  DEFAULT 'accepted',
    "error_message" TEXT,
    "attempts" INT NOT NULL  DEFAULT 0,
    "created_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL  DEFAULT CURRENT_TIMESTAMP,
    "batch_id" UUID NOT NULL REFERENCES "submission_batches" ("id") ON DELETE CASCADE,
    "resource_id" INT REFERENCES "pan_resources" ("id") ON DELETE SET NULL
);
COMMENT ON COLUMN "submission_tasks"."original_url" IS '提交的原始URL';
COMMENT ON COLUMN "submission_tasks"."parsed_pan_type_int" IS '解析出的网盘类型整数值';
COMMENT ON COLUMN "submission_tasks"."parsed_resource_key" IS '解析出的资源Key';
COMMENT ON COLUMN "submission_tasks"."parsed_share_pwd" IS '解析出的分享密码';
COMMENT ON COLUMN "submission_tasks"."status" IS '任务处理状态';
COMMENT ON COLUMN "submission_tasks"."error_message" IS '处理失败时的错误信息';
COMMENT ON COLUMN "submission_tasks"."attempts" IS '处理尝试次数';
COMMENT ON COLUMN "submission_tasks"."created_at" IS '创建时间';
COMMENT ON COLUMN "submission_tasks"."updated_at" IS '最后更新时间';
COMMENT ON COLUMN "submission_tasks"."batch_id" IS '所属批处理';
COMMENT ON COLUMN "submission_tasks"."resource_id" IS '关联的资源条目';
COMMENT ON TABLE "submission_tasks" IS '记录单个资源URL的处理任务';
CREATE TABLE IF NOT EXISTS "aerich" (
    "id" SERIAL NOT NULL PRIMARY KEY,
    "version" VARCHAR(255) NOT NULL,
    "app" VARCHAR(100) NOT NULL,
    "content" JSONB NOT NULL
);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        """
