import asyncio
from tortoise import Tortoise, run_async
from tortoise.exceptions import DBConnectionError
import sys
import os

# 将项目根目录添加到Python搜索路径中，这样我们就可以导入'app'模块
# __file__ -> /path/to/project/migrate_data.py
# os.path.dirname -> /path/to/project
# os.path.abspath -> /path/to/project (绝对路径)
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# 现在我们可以安全地从项目中导入模块了
from app.utils.config import settings
from app.models.resource import PanResource
from app.models.submission import SubmissionBatch, SubmissionTask
from app.models.feedback import ResourceInvalidFeedback
from app.models.enums import BatchStatus, TaskStatus

# --- Configuration ---
# Path to your old SQLite database
SQLITE_DB_PATH = "db.sqlite3"
SQLITE_DB_URL = f"sqlite://{SQLITE_DB_PATH}"

# 从标准配置文件中获取PostgreSQL URL
POSTGRES_DB_URL = settings.get("database.url")

# Define Tortoise ORM configurations for both databases
# We use different app names to manage connections separately
TORTOISE_ORM_CONFIG = {
    "connections": {
        "sqlite": SQLITE_DB_URL,
        "postgres": POSTGRES_DB_URL,
    },
    "apps": {
        "models": {
            "models": [
                "app.models.resource",
                "app.models.submission",
                "app.models.feedback",
                "aerich.models",  # Important for aerich tracking
            ],
            "default_connection": "postgres",
        },
        "sqlite_models": {
            "models": [
                "app.models.resource",
                "app.models.submission",
                "app.models.feedback",
            ],
            "default_connection": "sqlite",
        },
    },
}


async def clear_postgres_tables(to_conn_name="postgres"):
    """在迁移开始前，清空 PostgreSQL 中的目标数据表。"""
    print("\n▶️  开始清空 PostgreSQL 目标数据表...")
    to_conn = Tortoise.get_connection(to_conn_name)

    # 注意：这里的删除顺序与迁移顺序相反，以避免外键约束问题
    tables_to_clear = [
        "submission_tasks",
        "resource_invalid_feedbacks",
        "submission_batches",
        "pan_resources",
    ]

    try:
        for table in tables_to_clear:
            await to_conn.execute_script(
                f'TRUNCATE TABLE "{table}" RESTART IDENTITY CASCADE;'
            )
            print(f"   ✅ 表 '{table}' 已成功清空。")
        print("🟢 所有目标表均已清空。")
    except Exception as e:
        print(f"⚠️ 清空表时发生错误: {e}")
        print("   请手动检查您的 PostgreSQL 数据库，确保目标表是空的，然后再试。")
        raise  # 抛出异常以停止后续操作


async def migrate_model(model, from_conn_name="sqlite", to_conn_name="postgres"):
    """Generic function to migrate data for a single model."""
    print(f"▶️  开始迁移模型: {model.__name__}...")

    # Get connections
    from_conn = Tortoise.get_connection(from_conn_name)
    to_conn = Tortoise.get_connection(to_conn_name)

    # Fetch all data from the source table
    items_from_sqlite = await model.all().using_db(from_conn)
    count = len(items_from_sqlite)

    if count == 0:
        print(f"⚪️ 模型 {model.__name__} 中没有记录，跳过。")
        return

    print(f"   在 SQLite 中找到 {count} 条 {model.__name__} 记录。")

    # Create new objects for the destination database
    items_to_create = []
    for item in items_from_sqlite:
        data = {
            field: getattr(item, field)
            for field in item._meta.db_fields
            if field in model._meta.fields_map
        }

        # Handle Foreign Keys - copy the '_id' fields
        for fk_field in item._meta.fk_fields:
            fk_id_field = f"{fk_field}_id"
            data[fk_id_field] = getattr(item, fk_id_field)

        items_to_create.append(model(**data))

    # Bulk insert into PostgreSQL
    await model.bulk_create(items_to_create, using_db=to_conn)
    print(
        f"✅ 成功将 {len(items_to_create)} 条记录迁移至 PostgreSQL 的 {model.__name__} 表。"
    )


async def update_sequences(to_conn_name="postgres"):
    """Updates the auto-incrementing primary key sequences in PostgreSQL."""
    print("\n▶️  正在更新 PostgreSQL 的主键序列...")
    to_conn = Tortoise.get_connection(to_conn_name)

    # 如果它们具有自动插入整数PKS，请在此处添加表及其PK字段名称
    tables_with_sequences = {
        "pan_resources": "id",
        "resource_invalid_feedbacks": "id",
    }

    for table, pk_field in tables_with_sequences.items():
        try:
            # Find the max ID from the migrated data
            result = await to_conn.execute_query_dict(
                f'SELECT MAX("{pk_field}") as max_id FROM "{table}"'
            )
            max_id = (
                result[0]["max_id"] if result and result[0]["max_id"] is not None else 0
            )

            if max_id > 0:
                # 将序列设置为下一个可用值
                # pg_get_serial_sequence函数需要表和列名的单语引号
                await to_conn.execute_script(
                    f"SELECT setval(pg_get_serial_sequence('{table}', '{pk_field}'), {max_id}, true);"
                )
                print(f"✅ 表 '{table}' 的序列已更新至 {max_id}。")
            else:
                print(f"⚪️ 表 '{table}' 中无数据，无需更新序列。")
        except Exception as e:
            print(f"⚠️ 无法更新表 '{table}' 的序列，您可能需要手动操作。")
            print(f"   错误信息: {e}")


async def run_migration():
    """Main function to orchestrate the entire migration process."""
    if not POSTGRES_DB_URL:
        return  # Exit if config reading failed

    print("--- 开始执行从 SQLite 到 PostgreSQL 的数据迁移 ---")

    try:
        await Tortoise.init(config=TORTOISE_ORM_CONFIG)

        # Manually check connections
        print("   正在检查 SQLite 连接...")
        await Tortoise.get_connection("sqlite").execute_query("SELECT 1")
        print("   ✅ SQLite 连接成功。")

        print("   正在检查 PostgreSQL 连接...")
        await Tortoise.get_connection("postgres").execute_query("SELECT 1")
        print("   ✅ PostgreSQL 连接成功。")

    except (DBConnectionError, ConnectionRefusedError) as e:
        print("\n❌ 数据库连接错误: 无法连接到数据库。")
        print(f"   请检查您的数据库 URL 并确保服务正在运行。")
        print(f"   SQLite 路径: {SQLITE_DB_PATH}")
        print(
            f"   PostgreSQL URL: {POSTGRES_DB_URL.split('@')[1] if POSTGRES_DB_URL else 'N/A'}"
        )  # Hide user/pass
        print(f"   错误详情: {e}")
        return
    except Exception as e:
        print(f"❌ 初始化过程中发生意外错误: {e}")
        return

    try:
        # 1. 清空目标表
        await clear_postgres_tables()

        # 2. 定义迁移顺序以遵循外键约束
        migration_order = [
            PanResource,
            SubmissionBatch,
            ResourceInvalidFeedback,
            SubmissionTask,  # Depends on PanResource and SubmissionBatch
        ]

        # 3. 按顺序迁移数据
        for model in migration_order:
            await migrate_model(model)

        # 4. 更新自增主键序列
        await update_sequences()

        print("\n🎉 --- 数据迁移成功完成！ --- 🎉")

    except Exception as e:
        print(f"\n❌ 迁移过程中发生错误: {e}")
        print("   迁移可能未完成。请检查日志和您的数据库。")
    finally:
        await Tortoise.close_connections()
        print("\n   数据库连接已关闭。")


if __name__ == "__main__":
    run_async(run_migration())
