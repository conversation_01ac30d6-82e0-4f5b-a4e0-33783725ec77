#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
用于PM2定时触发 aisoua.com 爬虫任务的独立脚本。
该脚本会作为一个长时服务运行，根据配置文件中的间隔时间自动重复执行爬取任务。
"""
import asyncio
import logging
import signal
import sys
import os

# 将项目根目录添加到 Python 路径中
sys.path.append(
    os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

from app.crawlers.aisoua_crawler import AisouaCrawler
from app.db.engine import close_db, init_db
from app.utils.config import settings
from app.services.db_batch import batch_save_to_db

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("AisouaCrawlerRunner")

# 全局变量，用于控制主循环
running = True


async def main():
    """
    爬虫运行的主函数。
    """
    logger.info("正在初始化数据库连接...")
    await init_db()

    crawler = AisouaCrawler()

    if settings.get("aisoua_crawler.enabled"):
        logger.info("aisoua.com 爬虫服务已启动，进入持续监控模式...")
        await crawler.run()
    else:
        logger.info("Aisoua 爬虫在配置中被禁用，服务退出。")


def handle_signal(sig, frame):
    """
    处理终止信号，实现优雅退出。
    """
    global running
    logger.info(f"接收到信号 {sig}，准备停止服务...")
    running = False


if __name__ == "__main__":
    # 注册信号处理器
    signal.signal(signal.SIGINT, handle_signal)
    signal.signal(signal.SIGTERM, handle_signal)

    loop = asyncio.get_event_loop()
    try:
        loop.run_until_complete(main())
    finally:
        logger.info("正在关闭数据库连接...")
        loop.run_until_complete(close_db())
        logger.info("数据库连接已关闭，服务退出。")
