#!/usr/bin/env python3
"""
配置管理功能演示脚本
"""
import requests
import json
import sys
from typing import Dict, Any

# API基础URL
BASE_URL = "http://localhost:8000/api/admin"

# 演示用的认证token（实际使用时需要通过登录获取）
AUTH_TOKEN = "your_admin_token_here"

# 请求头
HEADERS = {
    "Authorization": f"Bearer {AUTH_TOKEN}",
    "Content-Type": "application/json"
}


def make_request(method: str, endpoint: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
    """发送HTTP请求"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=HEADERS)
        elif method.upper() == "POST":
            response = requests.post(url, headers=HEADERS, json=data)
        elif method.upper() == "PUT":
            response = requests.put(url, headers=HEADERS, json=data)
        else:
            raise ValueError(f"不支持的HTTP方法: {method}")
        
        response.raise_for_status()
        return response.json()
    
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        if hasattr(e, 'response') and e.response is not None:
            try:
                error_detail = e.response.json()
                print(f"错误详情: {error_detail}")
            except:
                print(f"响应内容: {e.response.text}")
        return None


def demo_get_config_list():
    """演示获取配置列表"""
    print("\n=== 演示：获取配置列表 ===")
    
    # 获取所有配置
    print("1. 获取所有配置...")
    result = make_request("GET", "/config")
    if result:
        print(f"✅ 成功获取 {result.get('total_count', 0)} 个配置项")
        print(f"配置分类数量: {len(result.get('categories', []))}")
        print(f"是否需要重启: {result.get('restart_required', False)}")
    
    # 按分类筛选
    print("\n2. 获取应用配置...")
    result = make_request("GET", "/config?category=app")
    if result:
        app_configs = result.get('categories', [])
        if app_configs:
            configs = app_configs[0].get('configs', [])
            print(f"✅ 应用配置项数量: {len(configs)}")
            for config in configs[:3]:  # 显示前3个
                print(f"  - {config['key']}: {config['value']}")
    
    # 搜索配置
    print("\n3. 搜索包含'debug'的配置...")
    result = make_request("GET", "/config?search=debug")
    if result:
        total = sum(len(cat.get('configs', [])) for cat in result.get('categories', []))
        print(f"✅ 找到 {total} 个匹配的配置项")


def demo_get_config_detail():
    """演示获取配置详情"""
    print("\n=== 演示：获取配置详情 ===")
    
    config_key = "app.debug"
    result = make_request("GET", f"/config/{config_key}")
    if result:
        config_data = result.get('data', {})
        print(f"✅ 配置项: {config_data.get('key')}")
        print(f"显示名称: {config_data.get('display_name')}")
        print(f"当前值: {config_data.get('value')}")
        print(f"数据类型: {config_data.get('type')}")
        print(f"是否必填: {config_data.get('required')}")
        print(f"生效方式: {config_data.get('effect_type')}")


def demo_validate_config():
    """演示配置验证"""
    print("\n=== 演示：配置验证 ===")
    
    # 验证有效配置
    print("1. 验证有效的端口号...")
    data = {
        "key": "api.port",
        "value": 8080
    }
    result = make_request("POST", "/config/validate", data)
    if result:
        print(f"✅ 验证结果: {result.get('valid')} - {result.get('message')}")
    
    # 验证无效配置
    print("\n2. 验证无效的端口号...")
    data = {
        "key": "api.port",
        "value": "invalid_port"
    }
    result = make_request("POST", "/config/validate", data)
    if result:
        print(f"❌ 验证结果: {result.get('valid')} - {result.get('message')}")


def demo_update_config():
    """演示配置更新"""
    print("\n=== 演示：配置更新 ===")
    
    # 注意：这个演示会实际修改配置，请谨慎使用
    print("⚠️  配置更新演示（实际会修改配置文件）")
    
    # 获取当前值
    current_result = make_request("GET", "/config/app.debug")
    if current_result:
        current_value = current_result.get('data', {}).get('value')
        print(f"当前 app.debug 值: {current_value}")
        
        # 更新配置（切换布尔值）
        new_value = not current_value if isinstance(current_value, bool) else False
        data = {
            "value": new_value,
            "comment": "演示配置更新功能"
        }
        
        print(f"尝试更新为: {new_value}")
        result = make_request("PUT", "/config/app.debug", data)
        if result:
            print("✅ 配置更新成功")
            print(f"重启要求: {result.get('data', {}).get('restart_required')}")
            
            # 恢复原值
            restore_data = {
                "value": current_value,
                "comment": "恢复原始值"
            }
            restore_result = make_request("PUT", "/config/app.debug", restore_data)
            if restore_result:
                print("✅ 配置已恢复原值")


def demo_create_backup():
    """演示创建配置备份"""
    print("\n=== 演示：创建配置备份 ===")
    
    data = {
        "comment": "演示备份功能"
    }
    result = make_request("POST", "/config/backup", data)
    if result:
        backup_data = result.get('data', {})
        print(f"✅ 备份创建成功")
        print(f"备份ID: {backup_data.get('backup_id')}")
        print(f"创建时间: {backup_data.get('created_at')}")
        return backup_data.get('backup_id')
    return None


def demo_get_backups():
    """演示获取备份列表"""
    print("\n=== 演示：获取备份列表 ===")
    
    result = make_request("GET", "/config/backups?page=1&size=5")
    if result:
        backups = result.get('backups', [])
        print(f"✅ 获取到 {len(backups)} 个备份")
        print(f"总备份数: {result.get('total', 0)}")
        
        for backup in backups[:3]:  # 显示前3个
            print(f"  - ID: {backup['id']}, 创建者: {backup['created_by_username']}")
            print(f"    时间: {backup['created_at']}, 说明: {backup.get('comment', '无')}")


def demo_get_config_history():
    """演示获取配置变更历史"""
    print("\n=== 演示：获取配置变更历史 ===")
    
    result = make_request("GET", "/config/history?page=1&size=5")
    if result:
        logs = result.get('logs', [])
        print(f"✅ 获取到 {len(logs)} 条变更记录")
        print(f"总记录数: {result.get('total', 0)}")
        
        for log in logs[:3]:  # 显示前3条
            print(f"  - 操作: {log['action']}, 用户: {log['username']}")
            print(f"    配置: {log.get('config_key', '无')}, 时间: {log['created_at']}")


def demo_get_restart_status():
    """演示获取重启状态"""
    print("\n=== 演示：获取重启状态 ===")
    
    result = make_request("GET", "/config/restart-status")
    if result:
        print(f"✅ 是否需要重启: {result.get('restart_required')}")
        print(f"待生效配置: {result.get('pending_changes', [])}")
        print(f"受影响服务: {result.get('affected_services', [])}")


def demo_get_categories():
    """演示获取配置分类"""
    print("\n=== 演示：获取配置分类 ===")
    
    result = make_request("GET", "/config/categories")
    if result:
        categories = result.get('data', {}).get('categories', [])
        print(f"✅ 获取到 {len(categories)} 个配置分类")
        
        for category in categories[:5]:  # 显示前5个
            print(f"  {category.get('icon', '📁')} {category['display_name']} ({category['name']})")


def main():
    """主演示函数"""
    print("🚀 配置管理API功能演示")
    print("=" * 50)
    
    # 检查服务是否可用
    try:
        response = requests.get(f"{BASE_URL}/config/categories", headers=HEADERS, timeout=5)
        if response.status_code == 401:
            print("❌ 认证失败，请设置正确的AUTH_TOKEN")
            print("请先登录获取管理员token，然后修改脚本中的AUTH_TOKEN变量")
            return
        elif response.status_code != 200:
            print(f"❌ 服务不可用，状态码: {response.status_code}")
            return
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到服务: {e}")
        print("请确保API服务正在运行在 http://localhost:8000")
        return
    
    print("✅ 服务连接正常，开始演示...")
    
    # 执行各种演示
    demo_get_categories()
    demo_get_config_list()
    demo_get_config_detail()
    demo_validate_config()
    demo_get_restart_status()
    demo_get_config_history()
    demo_get_backups()
    
    # 询问是否执行修改操作
    print("\n" + "=" * 50)
    choice = input("是否执行配置更新和备份演示？(y/N): ").strip().lower()
    if choice == 'y':
        backup_id = demo_create_backup()
        demo_update_config()
        demo_get_backups()
        demo_get_config_history()
    else:
        print("跳过修改操作演示")
    
    print("\n🎉 演示完成！")
    print("\n📚 更多信息请参考: docs/配置管理API使用说明.md")


if __name__ == "__main__":
    main()
