# 时间过滤功能文档

## 概述

为search接口和cached_search接口添加了时间过滤功能，允许用户根据资源的更新时间来过滤搜索结果。

## 功能特性

### 1. 新增字段

#### API响应字段
- **update_time**: 统一的更新时间字段，使用ISO 8601格式
- **updated_at**: 保留原有字段，向后兼容

#### API请求参数
- **time_filter**: 时间过滤参数，支持以下选项：
  - `all`: 全部时间（默认值）
  - `week`: 最近一周（7天）
  - `half_month`: 最近半月（15天）
  - `month`: 最近1月（30天）
  - `half_year`: 最近半年（180天）
  - `year`: 最近一年（365天）

### 2. 支持的接口

#### /api/search
搜索网盘资源接口，支持本地和在线搜索结果的时间过滤。

**请求示例：**
```bash
GET /api/search?keyword=电影&time_filter=week&page=1&limit=10
```

**响应示例：**
```json
{
  "status": "success",
  "message": "搜索成功",
  "total": 25,
  "results": [
    {
      "resource_id": "abc123",
      "title": "电影名称",
      "file_size": "1.5GB",
      "file_type": "video",
      "pan_type": 1,
      "updated_at": "2025-07-01T10:30:00+00:00",
      "update_time": "2025-07-01T10:30:00+00:00",
      "text_content": "电影描述",
      "relevance_score": 0.95
    }
  ]
}
```

#### /api/cached_resources
获取缓存资源接口，支持本地数据库资源的时间过滤。

**请求示例：**
```bash
GET /api/cached_resources?title=电影&time_filter=month&page=1&limit=10
```

### 3. 实现细节

#### 时间范围计算
- 基于当前UTC时间向前推算指定天数
- 使用闭区间 [start_time, end_time] 进行过滤

#### 时间字段统一
- 所有搜索结果统一使用 `updated_at` 字段存储时间信息
- 支持多种时间格式：datetime对象、ISO字符串、Unix时间戳
- 自动处理时区转换，统一为UTC时间

#### 缓存机制
- 时间过滤参数包含在缓存键中
- 不同时间过滤选项使用独立缓存

### 4. 数据库查询优化

#### Meilisearch过滤
本地搜索服务在Meilisearch查询中添加时间范围过滤条件：
```
updated_at >= start_timestamp AND updated_at <= end_timestamp
```

#### 内存过滤
在线搜索结果在内存中进行时间过滤，确保结果的一致性。

## 使用示例

### 1. 搜索最近一周的视频资源
```bash
curl -X GET "http://localhost:8000/api/search" \
  -G \
  -d "keyword=电影" \
  -d "time_filter=week" \
  -d "file_type=video" \
  -d "limit=20"
```

### 2. 获取最近一月的缓存资源
```bash
curl -X GET "http://localhost:8000/api/cached_resources" \
  -G \
  -d "title=文档" \
  -d "time_filter=month" \
  -d "file_type=document"
```

### 3. 使用Enum类型（Python客户端）
```python
from app.models.enums import TimeFilter

# 在API调用中使用
time_filter = TimeFilter.WEEK
```

## 错误处理

### 无效时间过滤选项
如果传入无效的时间过滤选项，系统会：
1. 记录警告日志
2. 自动回退到 `all`（全部时间）
3. 继续正常处理请求

### 时间解析失败
如果资源的时间字段无法解析，该资源会：
1. 记录警告日志
2. 被排除在过滤结果之外
3. 不影响其他资源的处理

## 性能考虑

### 1. 索引优化
- Meilisearch中的 `updated_at` 字段已建立索引
- 时间范围查询性能良好

### 2. 缓存策略
- 不同时间过滤选项使用独立缓存
- 避免缓存污染和不必要的重复计算

### 3. 内存使用
- 时间过滤在结果合并后进行
- 减少不必要的数据传输和处理

## 测试覆盖

项目包含完整的单元测试和集成测试：
- 时间范围计算测试
- 结果过滤测试
- 时间格式化测试
- 边界情况测试
- 时区处理测试

运行测试：
```bash
python -m pytest tests/test_time_filter.py -v
```

## 向后兼容性

- 新增的 `time_filter` 参数为可选，默认值为 `all`
- 保留原有的 `updated_at` 字段
- 不影响现有API调用的行为

## 未来扩展

### 可能的改进方向
1. 支持自定义时间范围（开始时间和结束时间）
2. 添加更多预设时间选项（如"今天"、"昨天"）
3. 支持相对时间表达式（如"3天前"）
4. 添加时间过滤的统计信息

### 配置选项
可以在配置文件中添加时间过滤相关的设置：
```yaml
time_filter:
  default_option: "all"
  max_range_days: 365
  cache_ttl: 3600
```
