import os
import sys
import platform

from celery import Celery
from celery.signals import worker_process_init, worker_process_shutdown
from app.utils.config import settings
from kombu import Exchange, Queue
from celery.schedules import crontab
import asyncio
from app.db.engine import init_db, close_db
import logging

# 在这里导入数据库初始化函数
from app.db.engine import init_db


@worker_process_init.connect
def setup_db_connection(**kwargs):
    """Celery worker进程启动时初始化数据库连接"""
    logging.info("Celery worker 启动，正在初始化数据库连接...")

    try:
        # 清理可能存在的事件循环
        try:
            existing_loop = asyncio.get_event_loop()
            if existing_loop and not existing_loop.is_closed():
                logging.warning("检测到现有事件循环，正在关闭...")
                existing_loop.close()
        except RuntimeError:
            # 没有现有循环，这是正常的
            pass

        # 创建全新的事件循环来初始化数据库
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(init_db())
            logging.info("数据库连接初始化成功。")
        finally:
            # 关闭初始化循环，让每个任务创建自己的循环
            try:
                loop.close()
                logging.debug("初始化事件循环已关闭")
            except Exception as e:
                logging.warning(f"关闭初始化事件循环时出现警告: {e}")

            # 清除事件循环引用
            asyncio.set_event_loop(None)

    except Exception as e:
        logging.error(f"Celery worker 数据库初始化失败: {e}", exc_info=True)
        raise


@worker_process_shutdown.connect
def close_db_connection(**kwargs):
    """Celery worker进程关闭时断开数据库连接"""
    logging.info("Celery worker 关闭，正在断开数据库连接...")
    try:
        # 获取当前事件循环，如果没有则创建新的
        try:
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                raise RuntimeError("Event loop is closed")
        except RuntimeError:
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        # 安全地关闭数据库连接
        try:
            loop.run_until_complete(close_db())
            logging.info("数据库连接已成功断开。")
        finally:
            # 如果是我们创建的循环，则关闭它
            if not loop.is_running():
                loop.close()
    except RuntimeError as e:
        if "Event loop is closed" in str(e):
            logging.warning(
                "事件循环已关闭，无法正常关闭数据库连接。这通常在进程关闭时发生，可以忽略。"
            )
        else:
            logging.error(f"关闭数据库连接时发生运行时错误: {e}")
    except Exception as e:
        logging.error(f"关闭数据库连接时发生未知错误: {e}", exc_info=True)


def create_celery_app() -> Celery:
    """
    创建并配置一个Celery应用实例。
    从全局配置中读取 broker, backend 和要包含的任务模块。
    """
    celery_app = Celery(
        "pan-so-backend",
        broker=settings.get("celery.broker_url"),
        backend=settings.get("celery.result_backend"),
        include=settings.get("celery.include"),
    )

    # For Celery 5.x, JSON is the default serializer. Explicitly setting it is good practice.
    celery_app.conf.update(
        accept_content=["json"],
        task_serializer="json",
        result_serializer="json",
        timezone="Asia/Shanghai",
        enable_utc=False,
        broker_connection_retry_on_startup=True,
        # Windows 兼容性配置
        worker_pool_restarts=True,
        worker_max_tasks_per_child=1000,  # 限制每个worker处理的任务数，避免内存泄漏
        task_acks_late=True,  # 任务完成后才确认，提高可靠性
        task_reject_on_worker_lost=True,  # worker丢失时拒绝任务
        # 事件循环和异步配置
        task_always_eager=False,  # 确保任务异步执行
        task_eager_propagates=True,  # 传播异常
        # 超时配置
        task_soft_time_limit=300,  # 5分钟软超时
        task_time_limit=600,  # 10分钟硬超时
        # 可以添加更多Celery配置
    )

    # --- 1. 定义任务队列 ---
    default_exchange = Exchange("default", type="direct")
    celery_app.conf.task_queues = (
        Queue("default", default_exchange, routing_key="default"),
        Queue("kdocs_queue", default_exchange, routing_key="kdocs_queue"),
        Queue("xuebapan_queue", default_exchange, routing_key="xuebapan_queue"),
        Queue("submission_queue", default_exchange, routing_key="submission_queue"),
        Queue("seo_queue", default_exchange, routing_key="seo_queue"),
    )

    # --- 2. 定义任务路由 ---
    celery_app.conf.task_routes = {
        "crawlers.run_kdocs_crawl": {"queue": "kdocs_queue"},
        "crawlers.run_xuebapans_crawl": {"queue": "xuebapan_queue"},
        "submission.*": {"queue": "submission_queue"},
        "crawlers.run_duanju_crawl": {"queue": "duanju_queue"},
        "tasks.daily_submit_new_urls": {"queue": "seo_queue"},
    }

    # --- 3. 定义定时任务 (Celery Beat) ---
    celery_app.conf.beat_schedule = {
        "run-kdocs-crawl-every-2-hours": {
            "task": "crawlers.run_kdocs_crawl",
            "schedule": crontab(minute="43", hour="*"),  # 每小时的43分运行
        },
        "run-xuebapans-crawl-every-6-hours": {
            "task": "crawlers.run_xuebapans_crawl",
            "schedule": crontab(minute="30", hour="*/6"),  # 每6小时的30分执行
        },
        "cleanup-stuck-tasks-hourly": {
            "task": "submission.cleanup_stuck_tasks",
            "schedule": crontab(minute="0", hour="*"),  # 每小时的0分执行
        },
        "duanju-crawl-daily": {
            "task": "crawlers.run_duanju_crawl",
            "schedule": crontab(minute="10", hour="*"),  # 每小时的10分执行
        },
        "submit-new-urls-to-indexnow-daily": {
            "task": "tasks.daily_submit_new_urls",
            "schedule": crontab(minute="0", hour="3"),  # 每天凌晨3点执行
        },
    }

    return celery_app


# 创建全局Celery实例
celery_app = create_celery_app()

# # 显式导入任务以确保注册 (现在由Celery的include自动处理)
# from app.tasks.submission import process_batch


# @celery_app.on_after_configure.connect
# def setup_periodic_tasks(sender, **kwargs):
#     """
#     设置周期性任务（如果需要）。
#     此方法已被新的 beat_schedule 配置取代，以实现更集中的管理。
#     """
#     # 每小时执行一次卡住任务的清理工作
#     sender.add_periodic_task(
#         3600.0,  # 任务执行的频率（秒），这里是1小时
#         celery_app.signature("submission.cleanup_stuck_tasks"),
#         name="Cleanup stuck submission tasks every hour",
#     )
#
#     # 每3小时执行一次学霸盘爬虫任务
#     sender.add_periodic_task(
#         10800.0,  # 3 * 60 * 60 = 10800 秒
#         celery_app.signature("crawlers.run_xuebapans_crawl"),
#         name="Run xuebapans crawler every 3 hours",
#     )


# Windows多进程修复
# 在Windows上，Celery使用'spawn'方式启动子进程，这要求在主模块中
# 调用freeze_support()。这可以防止子进程重新导入和执行主模块代码，
# 从而避免无限递归的错误。
if __name__ == "__main__":
    from multiprocessing import freeze_support

    freeze_support()

    celery_app.start()
