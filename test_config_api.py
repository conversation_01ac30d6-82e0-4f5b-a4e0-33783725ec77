#!/usr/bin/env python3
"""
配置管理API测试脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.config_service import config_service, config_validator
from app.models.config_models import ConfigUpdateRequest, ConfigValidationRequest


async def test_config_service():
    """测试配置服务"""
    print("=== 测试配置管理服务 ===")
    
    try:
        # 测试加载配置
        print("\n1. 测试加载配置...")
        config = await config_service.load_config()
        print(f"配置加载成功，包含 {len(config)} 个顶级配置项")
        
        # 测试获取配置值
        print("\n2. 测试获取配置值...")
        app_name = config_service.get_config_value(config, "app.name")
        print(f"app.name = {app_name}")
        
        app_debug = config_service.get_config_value(config, "app.debug")
        print(f"app.debug = {app_debug}")
        
        # 测试敏感信息遮蔽
        print("\n3. 测试敏感信息遮蔽...")
        jwt_secret = config_service.get_config_value(config, "auth.jwt_secret_key")
        masked_secret = config_service.mask_sensitive_value("auth.jwt_secret_key", jwt_secret)
        print(f"JWT密钥原值长度: {len(jwt_secret) if jwt_secret else 0}")
        print(f"JWT密钥遮蔽后: {masked_secret}")
        
        # 测试配置验证
        print("\n4. 测试配置验证...")
        
        # 验证有效的配置
        valid_result = config_validator.validate_config_value("app.name", "test-app")
        print(f"有效配置验证: {valid_result.valid} - {valid_result.message}")
        
        # 验证无效的配置
        invalid_result = config_validator.validate_config_value("api.port", "invalid_port")
        print(f"无效配置验证: {invalid_result.valid} - {invalid_result.message}")
        
        # 测试配置分类
        print("\n5. 测试配置分类...")
        for cat_name, cat_info in config_service.CONFIG_CATEGORIES.items():
            print(f"- {cat_name}: {cat_info['display_name']}")
        
        # 测试生效类型判断
        print("\n6. 测试配置生效类型...")
        test_keys = ["app.debug", "api.port", "logging.level", "cache.resource_cache.maxsize"]
        for key in test_keys:
            effect_type = config_service.get_effect_type(key)
            print(f"- {key}: {effect_type}")
        
        print("\n✅ 配置服务测试完成")
        
    except Exception as e:
        print(f"❌ 配置服务测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_config_backup():
    """测试配置备份功能"""
    print("\n=== 测试配置备份功能 ===")
    
    try:
        # 注意：这里需要数据库连接，在实际测试中需要初始化数据库
        print("⚠️  配置备份测试需要数据库连接，跳过此测试")
        print("在实际环境中，可以测试以下功能：")
        print("- 创建配置备份")
        print("- 恢复配置备份")
        print("- 记录配置变更日志")
        
    except Exception as e:
        print(f"❌ 配置备份测试失败: {e}")


def test_config_categories():
    """测试配置分类定义"""
    print("\n=== 测试配置分类定义 ===")
    
    categories = config_service.CONFIG_CATEGORIES
    print(f"定义了 {len(categories)} 个配置分类：")
    
    for cat_name, cat_info in categories.items():
        print(f"\n📁 {cat_info['icon']} {cat_info['display_name']} ({cat_name})")
        print(f"   描述: {cat_info['description']}")


def test_validation_rules():
    """测试验证规则"""
    print("\n=== 测试验证规则 ===")
    
    rules = config_validator.VALIDATION_RULES
    print(f"定义了 {len(rules)} 个验证规则：")
    
    for key, rule in rules.items():
        print(f"\n🔧 {key}")
        print(f"   类型: {rule.get('type', 'any')}")
        print(f"   必填: {rule.get('required', False)}")
        print(f"   敏感: {rule.get('sensitive', False)}")
        if rule.get('description'):
            print(f"   描述: {rule['description']}")


def test_sensitive_detection():
    """测试敏感配置检测"""
    print("\n=== 测试敏感配置检测 ===")
    
    test_keys = [
        "app.name",
        "app.debug", 
        "database.url",
        "auth.jwt_secret_key",
        "email.password",
        "wx_push.corp_secret",
        "redis.password",
        "baidu_accounts.0.cookie"
    ]
    
    for key in test_keys:
        is_sensitive = config_service.is_sensitive_config(key)
        status = "🔒 敏感" if is_sensitive else "🔓 普通"
        print(f"{status} {key}")


async def main():
    """主测试函数"""
    print("🚀 开始配置管理功能测试")
    
    # 基础功能测试
    await test_config_service()
    
    # 配置分类测试
    test_config_categories()
    
    # 验证规则测试
    test_validation_rules()
    
    # 敏感配置检测测试
    test_sensitive_detection()
    
    # 备份功能测试（需要数据库）
    await test_config_backup()
    
    print("\n🎉 所有测试完成")


if __name__ == "__main__":
    asyncio.run(main())
