import asyncio
import logging
from tqdm import tqdm
from app.core.celery_app import celery_app
from app.db.engine import db_transaction
from app.models.submission import SubmissionBatch, SubmissionTask
from app.models.enums import TaskStatus, BatchStatus
from app.services.submission_service import (
    process_submitted_resource_task as process_task_logic,
)

logger = logging.getLogger(__name__)


def _run_async_task(coro_func, *args, **kwargs):
    """
    Linux多进程环境下安全运行异步任务的函数

    专门针对Celery prefork池（-c 4）优化，避免事件循环冲突
    使用隔离线程确保每个任务都有独立的事件循环

    Args:
        coro_func: 异步函数（不是协程对象）
        *args, **kwargs: 传递给异步函数的参数

    Returns:
        异步函数的执行结果
    """
    import asyncio
    import threading

    def run_in_isolated_thread():
        """在完全隔离的线程中运行异步任务"""
        import threading
        import queue

        result_queue = queue.Queue()
        exception_queue = queue.Queue()

        def thread_worker():
            """线程工作函数"""
            try:
                # 确保在新线程中创建全新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    # 创建协程并运行
                    coro = coro_func(*args, **kwargs)
                    result = loop.run_until_complete(coro)
                    result_queue.put(result)
                    logger.debug("异步任务在隔离线程中执行成功")
                except Exception as e:
                    exception_queue.put(e)
                    logger.error(f"异步任务执行失败: {e}", exc_info=True)
                finally:
                    # 彻底清理事件循环
                    try:
                        # 取消所有待处理的任务
                        pending = asyncio.all_tasks(loop)
                        if pending:
                            for task in pending:
                                task.cancel()
                            # 等待任务取消完成
                            loop.run_until_complete(
                                asyncio.gather(*pending, return_exceptions=True)
                            )
                    except Exception as cleanup_error:
                        logger.warning(f"清理事件循环时出现警告: {cleanup_error}")
                    finally:
                        try:
                            loop.close()
                        except Exception as close_error:
                            logger.warning(f"关闭事件循环时出现警告: {close_error}")
            except Exception as e:
                exception_queue.put(e)
                logger.error(f"线程工作函数执行失败: {e}", exc_info=True)

        # 创建并启动线程
        thread = threading.Thread(target=thread_worker, daemon=True)
        thread.start()
        thread.join(timeout=300)  # 5分钟超时

        # 检查结果
        if not exception_queue.empty():
            raise exception_queue.get()

        if not result_queue.empty():
            return result_queue.get()

        # 如果线程仍在运行，说明超时了
        if thread.is_alive():
            logger.error("异步任务执行超时")
            raise TimeoutError("异步任务执行超时（5分钟）")

        raise RuntimeError("异步任务执行失败，未返回结果")

    try:
        # 检查是否在事件循环中
        try:
            current_loop = asyncio.get_running_loop()
            logger.debug("检测到运行中的事件循环，使用隔离线程执行")
            # 在Celery多进程环境中总是使用隔离线程避免冲突
            return run_in_isolated_thread()
        except RuntimeError:
            # 没有运行的事件循环，直接运行
            logger.debug("未检测到事件循环，直接运行")
            coro = coro_func(*args, **kwargs)
            return asyncio.run(coro)

    except Exception as e:
        logger.debug(f"切换到隔离线程执行模式: {e}")
        # 备选方案：使用隔离线程（这是正常的保护机制）
        return run_in_isolated_thread()


@celery_app.task(
    name="submission.process_resource",
    bind=True,
    max_retries=2,
    default_retry_delay=30,
    acks_late=True,
)
def process_resource(self, task_id: str):
    """
    用于处理单个提交资源的 Celery 任务。
    此任务是同步的，并能从 gevent worker 内部正确运行异步函数。
    它支持失败任务的自动重试。
    """
    logger.info(f"Celery worker 接收到任务，开始处理 submission_task_id: {task_id}")

    async def run_task_in_transaction():
        async with db_transaction():
            await process_task_logic(task_id=task_id)

    try:
        # 传递异步函数而不是协程对象
        _run_async_task(run_task_in_transaction)
        logger.info(f"成功完成 submission_task_id: {task_id} 的处理")
    except Exception as e:
        logger.error(
            f"处理 submission_task_id {task_id} 时出错。正在重试... 错误: {e}",
            exc_info=True,
        )
        # 使用 Celery 的内置机制重试任务
        raise self.retry(exc=e)


@celery_app.task(name="submission.process_batch")
def process_batch(batch_id: str):
    """
    作为分发器角色的 Celery 任务。
    它获取一个批次的所有任务，并为每个任务创建一个独立的 Celery 任务。
    """
    logger.info(
        f"Celery worker 接收到任务，开始为 submission_batch_id: {batch_id} 分发子任务"
    )

    async def run_batch_dispatching():
        # 延迟导入以避免在模块级别产生循环依赖
        from app.models.enums import BatchStatus

        async with db_transaction():
            # 查找批次中所有尚未处理或分发的任务。
            tasks = await SubmissionTask.filter(
                batch_id=batch_id, status=TaskStatus.ACCEPTED
            ).all()
            if not tasks:
                logger.warning(f"未找到批次 {batch_id} 的待处理任务。退出。")
                return

            logger.info(f"正在为批次 {batch_id} 分发 {len(tasks)} 个独立的处理任务。")

            for task in tasks:
                # 为每个提交分发一个独立的处理任务。
                process_resource.delay(task_id=str(task.id))

            # 分发后，我们可以将批处理状态更新为"处理中"
            batch = await SubmissionBatch.get_or_none(id=batch_id)
            if batch:
                batch.status = BatchStatus.PROCESSING
                await batch.save(update_fields=["status"])

    try:
        # 传递异步函数而不是协程对象
        _run_async_task(run_batch_dispatching)
        logger.info(f"成功为批次 {batch_id} 分发了所有任务")
    except Exception as e:
        logger.error(
            f"为批次 {batch_id} 分发任务时发生错误: {e}",
            exc_info=True,
        )
        # 分发器的成功至关重要。我们可能也需要为它添加重试逻辑。
        raise


@celery_app.task(name="submission.cleanup_stuck_tasks")
def cleanup_stuck_tasks():
    """
    一个周期性的维护任务，用于查找并重置那些卡在"处理中"状态太久的任务。
    新逻辑：对于卡住的任务，将其原始URL重新提交以创建一个新任务，并将旧任务标记为已重新入队。
    """
    logger.info("开始执行巡检任务：清理卡住的Celery任务...")

    async def run_cleanup():
        from datetime import datetime, timedelta, timezone
        from app.models.enums import TaskStatus, BatchStatus
        from app.models.submission import SubmissionBatch
        from app.models.resource import PanResource
        from app.utils.pan_url_parser import parse_pan_url
        from app.tasks.submission import process_batch as process_batch_task

        async with db_transaction():
            # 定义一个时间阈值，例如1小时前
            stuck_threshold = datetime.now(timezone.utc) - timedelta(hours=1)

            # 查找所有状态为 "PROCESSING" 且在阈值时间之前最后更新的任务
            stuck_tasks = await SubmissionTask.filter(
                status=TaskStatus.PROCESSING, updated_at__lt=stuck_threshold
            ).all()

            if not stuck_tasks:
                logger.info("巡检完成：未发现卡住的任务。")
                return

            logger.warning(
                f"巡检发现 {len(stuck_tasks)} 个卡住的任务。正在为它们创建新任务重新入队..."
            )

            # 为所有需要重新提交的任务创建一个总的批次
            requeue_batch = await SubmissionBatch.create(
                submitted_by="cleanup_job", total_urls_submitted=len(stuck_tasks)
            )

            tasks_for_new_batch = 0
            for task in stuck_tasks:
                logger.warning(
                    f"任务 {task.id} (Batch: {task.batch_id}) 卡住超过1小时。将重新提交URL: {task.original_url}"
                )

                # --- 重新提交的核心逻辑 ---
                url = task.original_url
                if not url:
                    logger.error(
                        f"卡住的任务 {task.id} 缺少 original_url，无法重新提交。"
                    )
                    task.status = TaskStatus.FAILED_FETCH_DETAILS
                    task.error_message = "Stuck task without original_url."
                    await task.save(update_fields=["status", "error_message"])
                    continue

                parsed_info = parse_pan_url(url)
                if not parsed_info:
                    logger.error(f"无法解析卡住的任务 {task.id} 的URL: {url}")
                    task.status = TaskStatus.FAILED_PARSE_URL
                    task.error_message = "Stuck task with unparsable URL."
                    await task.save(update_fields=["status", "error_message"])
                    continue

                # 使用 get_or_create 确保资源记录的唯一性
                resource, _ = await PanResource.get_or_create(
                    resource_key=parsed_info.get("resource_key"),
                    defaults={
                        "original_url": url,
                        "title": f"待解析资源: {url[:60]}...",
                        "pan_type": parsed_info.get("pan_type_int"),
                        "share_pwd": parsed_info.get("share_pwd"),
                        "source": "requeued_by_cleanup_job",
                    },
                )

                # 创建一个全新的任务
                await SubmissionTask.create(
                    batch=requeue_batch,
                    resource=resource,
                    original_url=url,
                    status=TaskStatus.ACCEPTED,
                )
                tasks_for_new_batch += 1
                # --- 重新提交结束 ---

                # 将旧的、卡住的任务标记为已重新入队
                task.status = TaskStatus.STUCK_REQUEUED
                task.error_message = f"Task was stuck and has been re-queued in batch {requeue_batch.id}."
                await task.save(update_fields=["status", "error_message"])

            # 更新新批次的创建任务数
            requeue_batch.tasks_created = tasks_for_new_batch
            await requeue_batch.save(update_fields=["tasks_created"])

            # 如果创建了新任务，则分发批处理任务
            if requeue_batch.tasks_created > 0:
                process_batch_task.delay(batch_id=str(requeue_batch.id))
                logger.info(
                    f"为卡住的任务创建的新批次 {requeue_batch.id} 已分发给Celery进行处理。"
                )

            logger.info(f"成功为 {len(stuck_tasks)} 个卡住的任务重新入队。")

    try:
        # 由于此任务是独立的，并且由Beat调度，它也需要运行自己的事件循环。
        # 传递异步函数而不是协程对象
        _run_async_task(run_cleanup)
        logger.info("清理卡住任务的巡检任务成功完成。")
    except Exception as e:
        logger.error(f"执行清理卡住任务的巡检任务时发生错误: {e}", exc_info=True)
        # 对于维护任务，我们通常不希望它重试，以免在根本问题未解决时循环失败。
        # 但记录错误是至关重要的。
        raise
