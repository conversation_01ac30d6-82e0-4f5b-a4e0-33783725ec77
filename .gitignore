# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage
/tests/
tests/
*.pytest_cache/
.pytest_cache/

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts


# 忽略 __pycache__ 目录
pan-so-backend/__pycache__/
__pycache__/
*/__pycache__/
*/*/__pycache__/
*/*/*/__pycache__/
*.py[cod]

/fetchdata/
/.idea/


db.sqlite3-shm
db.sqlite3-wal
../__pycache__/
db.sqlite3
.output.txt
../__pycache__/*
app/__pycache__/*
app/services/__pycache__
app/__pycache__/*
app/api/__pycache__
app/middleware/__pycache__
app/schemas/__pycache__
app/utils/__pycache__
app/core/__pycache__
app/db/__pycache__
app/models/__pycache__
app/crawlers/__pycache__/esoua_crawler.cpython-311.pyc
app/crawlers/__pycache__/panku8_crawler.cpython-311.pyc
telegram_session.session
telegram_session.session-journal


last_meili_sync.txt
