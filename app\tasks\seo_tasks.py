import logging
import asyncio
from datetime import datetime, timedelta, timezone

from app.core.celery_app import celery_app
from app.db.engine import db_transaction
from app.services.indexnow_service import IndexNowService
from app.utils.config import settings

logger = logging.getLogger(__name__)


def chunk_list(data: list, size: int) -> list[list]:
    """
    将列表分割成指定大小的块。
    """
    return [data[i : i + size] for i in range(0, len(data), size)]


@celery_app.task(name="tasks.daily_submit_new_urls")
def daily_submit_new_urls_task():
    """
    每日定时任务，用于收集前一天新增的资源链接并提交到 IndexNow。
    这是一个同步的 Celery 任务，内部运行异步逻辑。
    """
    try:
        asyncio.run(daily_submit_new_urls_async())
    except Exception as e:
        logger.error(f"SEO 任务执行失败: {e}", exc_info=True)
        raise


async def daily_submit_new_urls_async():
    """
    异步执行每日 IndexNow URL 提交的核心逻辑。
    """
    from app.models.resource import PanResource  # 在函数内部导入以避免循环依赖

    logger.info("开始执行每日 IndexNow URL 提交任务...")

    host = settings.get("seo.site_host")
    if not host or host == "www.example.com":
        logger.warning("未配置或使用了默认的 seo.site_host，无法构建 URL。任务终止。")
        return

    yesterday = datetime.now(timezone.utc) - timedelta(days=1)
    start_of_day = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
    end_of_day = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)

    new_urls = []

    # 使用独立的数据库事务来查询数据
    try:
        async with db_transaction():
            # 查询昨天新增的资源
            new_resources = await PanResource.filter(
                created_at__gte=start_of_day,
                created_at__lte=end_of_day,
            ).all()

            if not new_resources:
                logger.info("昨天没有新增的资源，任务完成。")
                return

            # 构建 URL 列表
            # 注意：这里的 URL 结构 (/resources/{resource_key}) 需要与前端路由匹配
            new_urls = [
                f"https://{host}/resources/{res.resource_key}" for res in new_resources
            ]
            logger.info(f"发现昨天新增了 {len(new_urls)} 个 URL 待提交。")
    except Exception as e:
        logger.error(f"查询数据库时发生错误: {e}", exc_info=True)
        return

    if not new_urls:
        return

    # 创建 IndexNow 服务实例并确保正确关闭
    indexnow_service = IndexNowService()
    try:
        # 按每批 10000 个 URL 分割
        url_batches = chunk_list(new_urls, 10000)
        total_batches = len(url_batches)
        logger.info(f"URL 列表被分为 {total_batches} 个批次进行提交。")

        for i, batch in enumerate(url_batches):
            logger.info(
                f"正在提交第 {i + 1}/{total_batches} 批，包含 {len(batch)} 个 URL..."
            )
            success = await indexnow_service.submit_urls(batch)
            if success:
                logger.info(f"第 {i + 1}/{total_batches} 批提交成功。")
            else:
                logger.error(f"第 {i + 1}/{total_batches} 批提交失败。")

            # 如果不是最后一批，则在两次提交之间增加一个延迟，避免请求过于频繁
            if i < total_batches - 1:
                logger.info("等待 5 秒后提交下一批...")
                await asyncio.sleep(5)

        logger.info("每日 IndexNow URL 提交任务执行完毕。")
    except Exception as e:
        logger.error(f"提交 URL 到 IndexNow 时发生错误: {e}", exc_info=True)
        raise
    finally:
        # 确保 IndexNow 服务的 HTTP 客户端被正确关闭
        try:
            await indexnow_service.close()
            logger.debug("IndexNow 服务已正确关闭。")
        except Exception as e:
            logger.warning(f"关闭 IndexNow 服务时发生错误: {e}")
