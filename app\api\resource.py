from fastapi import APIRouter, Depends, Query, Request, HTTPException
from typing import Optional, List
import logging
from fastapi.responses import JSONResponse
from datetime import datetime
import time
from tortoise.transactions import in_transaction

from app.models.pydantic_models import (
    ShareLinkResponse,
    ResourceSubmissionRequest,
    BatchSubmissionResponse,
    SubmittedResourceInfo,
    IndividualTaskStatus,
    QuerySubmissionStatusRequest,
    ResourceDetailResponse,
    ResourceDetailErrorResponse,
)
from app.middleware.verify import verify_origin
from app.services import xunlei_pan_service
from app.services.baidu_pan_service import baidu_pan_service
from app.services.quark_pan_service import quark_pan_service
from app.crawlers.panku8_crawler import panku8_crawler
from app.models.resource import PanResource
from app.models.submission import SubmissionBatch, SubmissionTask
from app.models.enums import (
    TaskStatus as TaskStatusEnum,
    TimeFilter,
)
from app.models.user import User
from app.core.permissions import get_current_user_optional, Permissions
from app.utils.common import (
    calculate_expiry_date,
    BEIJING_TIMEZONE,
    filter_results_by_time,
    format_update_time,
)
from app.utils.pan_url_parser import parse_pan_url
from app.utils.seo_utils import generate_seo_metadata
from app.utils.config import settings
from app.services.local_search_service import local_search_service
from app.services.meilisearch_service import meilisearch_service

# Celery Task import

# 定义日志记录器
logger = logging.getLogger("resource-api")

router = APIRouter(tags=["resource"])

# 加载屏蔽词
BLOCKED_KEYWORDS = settings.get("security.blocked_keywords", [])

if meilisearch_service:
    meilisearch_service = meilisearch_service
else:
    meilisearch_service = None


@router.post(
    "/get_share",
    response_model=ShareLinkResponse,
    dependencies=[Depends(verify_origin)],
    summary="获取网盘分享链接",
    description="根据resource_id获取网盘分享链接",
)
async def get_share_link(
    request: Request,
    platform: str = Query(..., description="网盘平台类型"),
    resource_id: str = Query(..., description="资源ID"),
):
    """
    获取网盘分享链接，自动转存为自己的分享并返回新链接。资源有效期为1天。
    只通过resource_id查数据库获取原始链接和密码。
    """
    start_time = time.time()

    if not resource_id:
        raise HTTPException(status_code=400, detail="resource_id不能为空")

    logger.info(f"获取分享链接请求: resource_id={resource_id}")

    try:
        # 1. 先查数据库
        resource = await PanResource.filter(resource_key=resource_id).first()
        if not resource:
            return ShareLinkResponse(status="error", message="未找到对应资源")

        # 如果是已解析的资源(is_parsed=True)，并且有分享链接，直接返回
        if resource.is_parsed and resource.share_url:
            logger.info(f"资源 {resource_id} 为已解析资源，直接返回链接。")
            resource.access_count += 1
            await resource.save(update_fields=["access_count"])
            return ShareLinkResponse(
                status="success",
                message="获取分享链接成功",
                share_url=resource.share_url,
                expiry_days=365,  # 已解析资源默认长期有效
                is_deleted=False,
            )

        # 2. 判断是否已有未过期的分享链接
        if resource.share_url and resource.expiry_date:
            now = (
                datetime.now(resource.expiry_date.tzinfo)
                if resource.expiry_date.tzinfo
                else datetime.now()
            )
            if resource.expiry_date > now:
                return ShareLinkResponse(
                    status="success",
                    message="获取分享链接成功（缓存）",
                    share_url=resource.share_url,
                    expiry_days=(resource.expiry_date - now).days or 1,
                    is_deleted=False,
                )

        # 3. 没有可用缓存，走原有转存逻辑
        share_pwd = resource.share_pwd

        # 百度网盘处理逻辑
        if platform == "baidu" or platform == "thunder":
            # 先检查数据库中的original_url是否有值
            if resource.original_url:
                # 如果有original_url，直接使用
                share_url = resource.original_url
                logger.info(f"使用数据库中的原始链接: {share_url}")
                # 新增：校验原始链接有效性
                if platform == "baidu":
                    check_result = await baidu_pan_service.check_resource_status(
                        share_url
                    )
                elif platform == "thunder":
                    check_result = await xunlei_pan_service.check_resource_status(
                        share_url
                    )
                if not check_result.get("valid", False):
                    # 链接无效，删除资源并返回提示
                    await resource.delete()

                    logger.warning(f"原始链接无效，已删除资源: {resource_id}")
                    return ShareLinkResponse(
                        status="error",
                        message="原始链接已失效，资源已删除",
                        is_deleted=True,
                    )
            else:
                # 没有原始链接时，通过爬虫获取分享链接
                if resource_id:
                    if platform == "thunder":
                        detail_url = f"https://panku8.com/res/a04{resource_id}"
                    elif platform == "baidu":
                        detail_url = f"https://panku8.com/res/a01{resource_id}"
                    # 获取用户的请求头
                    user_headers = dict(request.headers)
                    logger.info(f"请求头: {user_headers}")
                    share_result = await panku8_crawler.get_share_link(
                        detail_url, user_headers
                    )
                    if share_result.get("status") != "success":
                        logger.error(f"获取分享链接失败: {share_result.get('message')}")
                        return ShareLinkResponse(
                            status="error",
                            message=share_result.get("message", "获取分享链接失败"),
                        )
                    share_url = share_result.get("share_url")
                    if not share_url:
                        return ShareLinkResponse(
                            status="error", message="获取到的分享链接为空"
                        )

                    # 更新资源的原始链接
                    try:
                        resource.original_url = share_url
                        await resource.save(update_fields=["original_url"])
                        logger.info(f"已更新资源的原始链接: {share_url}")
                    except Exception as e:
                        logger.error(f"更新原始链接失败: {str(e)}")
                else:
                    return ShareLinkResponse(
                        status="error", message="缺少resource_id,无法获取分享链接"
                    )
        else:
            # 非百度网盘平台处理
            share_url = resource.original_url
            if not share_url:
                return ShareLinkResponse(status="error", message="资源缺少原始分享链接")
            # 新增：校验原始链接有效性（如有其他平台可补充）
            if platform == "quark":
                check_result = await quark_pan_service.check_resource_status(share_url)
            # elif platform == "aliyun":
            #     check_result = await aliyun_pan_service.check_resource_status(share_url)
            else:
                check_result = {"valid": True}  # 默认通过
            if not check_result.get("valid", False):
                await resource.delete()
                logger.warning(f"原始链接无效，已删除资源: {resource_id}")
                return ShareLinkResponse(
                    status="error",
                    message="原始链接已失效，资源已删除",
                    is_deleted=True,
                )

        # 调用转存服务
        save_start = time.time()
        if platform == "baidu":
            save_result = await baidu_pan_service.save_shared_file(
                share_url=share_url, share_pwd=share_pwd, enable_rename=False
            )
        elif platform == "quark":
            save_result = await quark_pan_service.save_shared_file(
                share_url=share_url, share_pwd=share_pwd, enable_rename=False
            )
        elif platform == "thunder":
            save_result = await xunlei_pan_service.save_and_share(
                share_url=share_url,
                share_pwd=share_pwd,
            )
        else:
            return ShareLinkResponse(status="error", message="不支持的平台类型")
        save_time = time.time() - save_start

        if save_result.get("status") != "success":
            logger.error(f"转存文件失败: {save_result.get('message')}")
            return ShareLinkResponse(
                status="error", message=save_result.get("message", "转存文件失败")
            )

        # 更新数据库
        try:
            expiry_date = calculate_expiry_date(days=1)  # 有效期1天
            resource.verified_status = "valid"
            resource.share_url = save_result.get("share_url")
            resource.share_pwd = save_result.get("share_pwd")
            resource.expiry_date = expiry_date
            resource.access_count += 1
            await resource.save()
        except Exception as db_error:
            logger.error(f"更新资源分享信息到数据库失败: {str(db_error)}")
            # 不中断流程

        total_time = time.time() - start_time
        logger.info(
            f"获取分享链接成功，总耗时: {total_time:.2f}秒, 转存: {save_time:.2f}秒"
        )

        return ShareLinkResponse(
            status="success",
            message="获取分享链接成功",
            share_url=save_result.get("share_url"),
            expiry_days=1,  # 固定1天有效期
        )

    except Exception as e:
        logger.error(f"获取分享链接过程中发生异常: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"获取分享链接过程中发生错误: {str(e)}"
        )


@router.get(
    "/cached_resources",
    summary="获取缓存资源",
    description="根据资源名称/文件名(支持模糊搜索)、网盘类型、是否只返回验证有效的资源、返回结果数量限制、页码、排序字段、排序方向、是否启用相关搜索(分词匹配)获取缓存资源",
)
async def get_cached_resources(
    title: Optional[str] = Query(None, description="资源名称/文件名(支持模糊搜索)"),
    pan_type: Optional[int] = Query(
        None, description="网盘类型: 1=百度网盘, 2=夸克网盘, 3=阿里云盘, 4=迅雷网盘"
    ),
    file_type: Optional[str] = Query(
        None,
        description="文件类型过滤: video(视频), audio(音频), image(图片), document(文档), archive(压缩), application(应用)",
    ),
    valid_only: bool = Query(False, description="只返回验证有效的资源"),
    limit: int = Query(30, description="返回结果数量限制"),
    page: int = Query(1, description="页码，从1开始"),
    sort_by: str = Query(
        "relevance", description="排序字段: relevance, access_count, created_at, title"
    ),
    sort_order: str = Query("desc", description="排序方向: asc, desc"),
    related_search: bool = Query(True, description="启用相关搜索(分词匹配)"),
    exact: bool = Query(False, description="是否精准搜索"),
    user: Optional[str] = Query(
        None, description="根据分享用户名查找资源。传空字符串可查找匿名分享的资源。"
    ),
    time_filter: TimeFilter = Query(
        TimeFilter.ALL,
        description="时间过滤: all(全部时间), week(最近一周), half_month(最近半月), month(最近1月), half_year(最近半年), year(最近一年)",
    ),
):
    start_time = time.time()
    # 检查屏蔽词
    if title and any(blocked.lower() in title.lower() for blocked in BLOCKED_KEYWORDS):
        logger.warning(f"搜索词 '{title}' 命中屏蔽词")
        return JSONResponse(
            status_code=200,
            content={
                "status": "failed",
                "message": "抱歉，我们不能提供有关该搜索词的搜索结果",
                "total": 0,
                "page": page,
                "limit": limit,
                "pages": 0,
                "resources": [],
                "related_search_enabled": related_search,
                "related_terms": [],
            },
        )

    try:
        # 重构: 完全依赖 local_search_service
        search_result = await local_search_service.search_local(
            keyword=title,
            pan_type=pan_type,
            file_type=file_type,
            exact=exact,
            sort_by=sort_by,
            sort_order=sort_order,
            valid_only=valid_only,
            user=user,
            time_filter=time_filter,  # 传递时间过滤参数
        )

        resources_orm = search_result.get("results", [])
        total_count = search_result.get("totals", 0)

        # 手动进行分页
        offset = (page - 1) * limit
        paginated_resources = resources_orm[offset : offset + limit]

        is_local_search = search_result.get("is_local_search", False)
        search_info = search_result.get("search_info", {})

        # 格式化输出
        results = []
        if is_local_search:
            # 对于来自数据库的ORM对象列表，使用属性访问
            for resource in paginated_resources:
                resource_dict = {
                    "id": resource.id,
                    "resource_id": resource.resource_key,
                    "pan_type": resource.pan_type,
                    "pan_type_name": (
                        "百度网盘"
                        if resource.pan_type == 1
                        else "夸克网盘" if resource.pan_type == 2 else "未知"
                    ),
                    "title": resource.title,
                    "verified_status": resource.verified_status,
                    "share_url": resource.share_url,
                    "share_pwd": resource.share_pwd,
                    "file_type": resource.file_type,
                    "file_size": resource.file_size,
                    "access_count": resource.access_count,
                    "text_content": resource.text_content,
                    "author": resource.author,
                    "created_at": (
                        resource.created_at.astimezone(BEIJING_TIMEZONE).isoformat()
                        if resource.created_at
                        else None
                    ),
                    "updated_at": (
                        resource.updated_at.astimezone(BEIJING_TIMEZONE).isoformat()
                        if resource.updated_at
                        else None
                    ),
                    "expiry_date": (
                        resource.expiry_date.astimezone(BEIJING_TIMEZONE).isoformat()
                        if resource.expiry_date
                        else None
                    ),
                }
                results.append(resource_dict)
        else:
            # 对于来自Meilisearch的字典列表，使用字典键访问
            for resource in paginated_resources:
                # 先将从Meili返回的ISO格式字符串解析为datetime对象
                created_at_str = resource.get("created_at")
                created_at_dt = (
                    datetime.fromisoformat(created_at_str) if created_at_str else None
                )
                updated_at_str = resource.get("updated_at")
                updated_at_dt = (
                    datetime.fromisoformat(updated_at_str) if updated_at_str else None
                )
                expiry_date_str = resource.get("expiry_date")
                expiry_date_dt = (
                    datetime.fromisoformat(expiry_date_str) if expiry_date_str else None
                )

                resource_dict = {
                    "id": resource.get("id"),
                    "resource_id": resource.get("resource_key"),
                    "pan_type": resource.get("pan_type"),
                    "pan_type_name": (
                        "百度网盘"
                        if resource.get("pan_type") == 1
                        else "夸克网盘" if resource.get("pan_type") == 2 else "未知"
                    ),
                    "title": resource.get("title"),
                    "verified_status": resource.get("verified_status"),
                    "share_url": resource.get("share_url"),
                    "share_pwd": resource.get("share_pwd"),
                    "file_type": resource.get("file_type"),
                    "file_size": resource.get("file_size"),
                    "access_count": resource.get("access_count"),
                    "text_content": resource.get("text_content"),
                    "author": resource.get("author"),
                    "created_at": (
                        created_at_dt.astimezone(BEIJING_TIMEZONE).isoformat()
                        if created_at_dt
                        else None
                    ),
                    "updated_at": (
                        updated_at_dt.astimezone(BEIJING_TIMEZONE).isoformat()
                        if updated_at_dt
                        else None
                    ),
                    "update_time": format_update_time(
                        resource.get("updated_at")
                    ),  # 统一的时间字段
                    "expiry_date": (
                        expiry_date_dt.astimezone(BEIJING_TIMEZONE).isoformat()
                        if expiry_date_dt
                        else None
                    ),
                    "relevance_score": resource.get("relevance_score"),
                }
                results.append(resource_dict)

        total = (
            total_count if is_local_search else search_info.get("estimatedTotalHits", 0)
        )
        end_time = time.time()
        logger.info(f"获取缓存资源完成，耗时: {end_time - start_time:.4f}秒")

        # 简单的相关术语逻辑 (可以保留或移除)
        related_terms = []

        return {
            "status": "success",
            "message": "获取缓存资源成功",
            "total": total,
            "page": page,
            "limit": limit,
            "pages": (total + limit - 1) // limit,
            "resources": results,
            "related_search_enabled": related_search,
            "related_terms": related_terms,
        }
    except Exception as e:
        logger.error(f"获取缓存资源时发生异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取缓存资源异常: {str(e)}")


@router.post(
    "/submit_resources",
    response_model=BatchSubmissionResponse,
    summary="批量提交资源链接进行处理 (异步)",
    description="接收一批网盘URL，立即返回任务ID，并在后台异步处理。处理速度快，不会阻塞服务。",
    dependencies=[Depends(verify_origin)],
)
async def submit_resources_for_processing(
    payload: ResourceSubmissionRequest,
    request: Request,
    # 可选的用户认证（兼容现有逻辑）
    current_user: Optional[User] = Depends(get_current_user_optional),
):
    """
    接收资源提交请求。
    1. 创建一个批处理记录 (SubmissionBatch)。
    2. 为每个URL创建一个初步的 SubmissionTask 和 PanResource。
    3. 将每个 SubmissionTask 的ID分派给Celery Worker进行后台处理。
    4. 立即返回批处理ID和每个任务的ID。
    """
    # 管理员权限校验逻辑
    if payload.is_mine or payload.admin_submit:
        # 如果设置了is_mine或admin_submit，必须有管理员权限
        if not current_user:
            raise HTTPException(status_code=401, detail="管理员功能需要认证")

        user_permissions = await current_user.get_permissions()
        if Permissions.RESOURCE_MANAGE not in user_permissions:
            raise HTTPException(status_code=403, detail="需要资源管理权限")

    # 延迟导入以避免循环依赖
    from app.tasks.submission import process_batch as process_batch_task

    logger.info(f"接收到资源提交请求，URL数量: {len(payload.urls)}")
    submitted_by = "admin" if (payload.is_mine or payload.admin_submit) else "user"

    # 处理is_parsed逻辑：控制数据库中is_parsed字段的写入值
    is_parsed_flag = payload.is_parsed if payload.is_parsed is not None else True

    async with in_transaction():
        # 1. 创建批处理记录
        batch = await SubmissionBatch.create(
            submitted_by=submitted_by, total_urls_submitted=len(payload.urls)
        )

        created_tasks_info = []
        tasks_actually_created = 0
        tasks_skipped = 0

        for item in payload.urls:
            url = item.url.strip()
            if not url:
                continue

            # 2. 初步解析URL，创建资源和任务记录
            parsed_info = parse_pan_url(url)

            # 如果URL无法解析，则跳过
            if not parsed_info:
                logger.warning(f"无法解析的URL，已跳过: {url}")
                created_tasks_info.append(
                    SubmittedResourceInfo(
                        url=url,
                        task_id=None,
                        resource_id=None,
                        status="INVALID_URL",
                        notes="无法解析该URL，链接格式可能不正确或不受支持。",
                    )
                )
                continue
            logger.info(f"初步解析URL成功: {parsed_info}")
            # 查找或创建 PanResource, 现在同时使用 resource_key 和 pan_type
            resource, created = await PanResource.get_or_create(
                resource_key=parsed_info.get("resource_key"),
                pan_type=parsed_info.get("pan_type_int"),
                defaults={
                    "original_url": url,
                    "title": f"待解析资源: {url[:60]}...",
                    "share_pwd": parsed_info.get("share_pwd"),
                    "source": "user_submission",
                    "is_mine": payload.is_mine or False,
                    "is_parsed": is_parsed_flag,  # 直接使用参数值控制is_parsed字段
                },
            )

            # --- 优化：处理重复或已存在的资源 ---
            if not created:
                # 资源已存在，但需要更新is_parsed字段（因为get_or_create的defaults不会应用到已存在的记录）
                if resource.is_parsed != is_parsed_flag:
                    resource.is_parsed = is_parsed_flag
                    await resource.save(update_fields=["is_parsed"])
                    logger.info(
                        f"更新已存在资源的is_parsed字段: {resource.resource_key} -> {is_parsed_flag}"
                    )

                # 检查其关联任务
                latest_task = (
                    await SubmissionTask.filter(resource_id=resource.id)
                    .order_by("-created_at")
                    .first()
                )

                # **新增逻辑**：如果资源存在但没有任何关联任务，视为重复/异常，并跳过
                if not latest_task:
                    logger.info(
                        f"URL {url} (Resource: {resource.resource_key}) 已存在但无关联任务，跳过。"
                    )
                    tasks_skipped += 1
                    created_tasks_info.append(
                        SubmittedResourceInfo(
                            url=url,
                            task_id=None,
                            resource_id=resource.resource_key,
                            status="EXISTING_RESOURCE",
                            notes="系统内已存在该资源但无历史任务，跳过处理。",
                        )
                    )
                    continue  # 处理下一个URL

                # 如果存在任务，则检查最新任务的状态
                status = latest_task.status
                if status == TaskStatusEnum.SUCCESS:
                    logger.info(
                        f"URL {url} (Resource: {resource.resource_key}) 已经成功处理过，跳过。"
                    )
                    tasks_skipped += 1
                    created_tasks_info.append(
                        SubmittedResourceInfo(
                            url=url,
                            task_id=latest_task.id,
                            resource_id=resource.resource_key,
                            status="EXISTING_SUCCESS",
                            notes="该资源已存在且有效，无需重复提交。",
                        )
                    )
                    continue  # 处理下一个URL

                if status in [
                    TaskStatusEnum.ACCEPTED,
                    TaskStatusEnum.PROCESSING,
                ]:
                    logger.info(
                        f"URL {url} (Resource: {resource.resource_key}) 的任务正在处理中，跳过。"
                    )
                    tasks_skipped += 1
                    created_tasks_info.append(
                        SubmittedResourceInfo(
                            url=url,
                            task_id=latest_task.id,
                            resource_id=resource.resource_key,
                            status=f"EXISTING_{status.value.upper()}",
                            notes=f"该资源正在处理中 (状态: {status.value})，请稍后查询。",
                        )
                    )
                    continue  # 处理下一个URL

                # 如果之前的任务失败了，则允许继续执行以创建新任务进行重试
                logger.info(
                    f"URL {url} (Resource: {resource.resource_key}) 的上一个任务处理失败 (状态: {status.value})，将创建新任务重试。"
                )

            # 对于新资源，或之前失败的资源，创建新任务
            task = await SubmissionTask.create(
                batch=batch,
                resource=resource,
                original_url=url,
                status=TaskStatusEnum.ACCEPTED,  # 新状态：已被API接受，等待worker拾取
            )
            tasks_actually_created += 1

            # 记录此URL的处理结果
            created_tasks_info.append(
                SubmittedResourceInfo(
                    url=url,
                    task_id=task.id,
                    resource_id=resource.resource_key,
                    status=task.status.value,
                    notes="新任务已创建，等待处理。",
                )
            )

        # 更新批处理统计
        batch.tasks_created = tasks_actually_created
        batch.skipped_tasks = tasks_skipped
        await batch.save(update_fields=["tasks_created", "skipped_tasks"])

        # 调度单个批处理任务
        if batch.tasks_created > 0:
            # 无论is_parsed参数值如何，都触发后台解析任务
            process_batch_task.delay(batch_id=str(batch.id))
            logger.info(
                f"批处理 {batch.id} 已创建，包含 {batch.tasks_created} 个新任务，已分发给Celery进行处理。"
                f"is_parsed标识设置为: {is_parsed_flag}"
            )
        else:
            logger.info(
                f"批处理 {batch.id} 中没有需要处理的新任务，所有提交的URL都已被跳过。"
            )
            # 如果没有新任务，需要手动触发一次批次状态更新，以将其标记为完成
            from app.services.submission_service import _update_batch_stats

            await _update_batch_stats(batch.id)

    return BatchSubmissionResponse(
        batch_id=batch.id,
        message="资源已成功提交后台处理。",
        total_submitted=len(payload.urls),
        accepted_for_processing=tasks_actually_created,
        initial_results=created_tasks_info,
    )


@router.post(
    "/query_submission_status",
    response_model=List[IndividualTaskStatus],
    summary="通过URL列表查询资源提交任务的状态",
    description="接收一个包含原始分享URL的列表，返回每个URL关联的所有提交任务的当前状态。注意：由于后台任务是异步的，状态更新可能存在延迟。",
    dependencies=[Depends(verify_origin)],
)
async def query_submission_status_by_urls(
    payload: QuerySubmissionStatusRequest,
    request: Request,
) -> List[IndividualTaskStatus]:
    if not payload.urls:
        return []

    query_urls = [url.strip() for url in payload.urls if url.strip()]
    if not query_urls:
        return []

    all_task_details = []

    tasks = (
        await SubmissionTask.filter(original_url__in=query_urls)
        .prefetch_related("resource", "batch")
        .order_by("-created_at")
    )

    for task in tasks:
        resource_title = None
        pan_resource_id = None
        if task.resource:
            resource_title = task.resource.title
            pan_resource_id = task.resource.id

        batch_id_for_task = None
        if task.batch:
            batch_id_for_task = task.batch.id

        all_task_details.append(
            IndividualTaskStatus(
                task_id=task.id,
                original_url=task.original_url,
                status=task.status,
                resource_title=resource_title,
                pan_resource_id=pan_resource_id,
                error_message=task.error_message,
                updated_at=(
                    task.updated_at.astimezone(BEIJING_TIMEZONE)
                    if task.updated_at
                    else None
                ),
                batch_id=batch_id_for_task,
            )
        )

    if not all_task_details:
        logger.info(f"没有找到对应的提交任务,请检查URL是否正确: {query_urls}")

    return all_task_details


@router.get(
    "/resource/{resource_key}",
    response_model=ResourceDetailResponse,
    responses={404: {"model": ResourceDetailErrorResponse}},
    summary="获取单个资源详情(SEO优化)",
    description="根据resource_key获取单个资源的详细信息，并动态生成SEO元数据。",
)
async def get_resource_details(resource_key: str) -> ResourceDetailResponse:
    """
    根据提供的 resource_key 从数据库中检索单个资源的详细信息。

    - 自动生成推荐的 SEO title 和 meta description。
    - 每次调用会使资源的 'access_count' 增加1。
    - 如果找不到资源，则会触发一个HTTP 404 Not Found错误。

    Args:
        resource_key: 资源的唯一标识符。

    Returns:
        一个包含资源详情和SEO元数据的Pydantic模型实例。
    """
    resource = await PanResource.get_or_none(resource_key=resource_key)
    if not resource:
        # 使用HTTPException是FastAPI中处理错误的标准方式
        raise HTTPException(
            status_code=404,
            detail="资源未找到",
        )

    # 原子地增加访问计数
    await PanResource.filter(id=resource.id).update(
        access_count=resource.access_count + 1
    )
    resource.access_count += 1  # 同步本地对象，以便在响应中返回正确的值

    # 生成SEO元数据
    seo_data = generate_seo_metadata(resource)

    # 从ORM模型创建Pydantic响应模型
    response_data = ResourceDetailResponse.from_orm(resource)

    response_data.created_at = (
        resource.created_at.astimezone(BEIJING_TIMEZONE)
        if resource.created_at
        else None
    )
    response_data.updated_at = (
        resource.updated_at.astimezone(BEIJING_TIMEZONE)
        if resource.updated_at
        else None
    )
    response_data.expiry_date = (
        resource.expiry_date.astimezone(BEIJING_TIMEZONE)
        if resource.expiry_date
        else None
    )

    # 填充动态生成的SEO数据
    response_data.seo_title = seo_data["seo_title"]
    response_data.seo_description = seo_data["seo_description"]

    # FastAPI会自动将Pydantic模型序列化为JSON
    return response_data


@router.get(
    "/resources/sitemap",
    summary="获取站点地图资源",
    description="为站点地图生成优化的、轻量级的资源列表。",
)
async def get_sitemap_resources(
    limit: int = Query(1000, description="返回结果数量限制", gt=0, le=5000),
    page: int = Query(1, description="页码，从1开始", gt=0),
):
    """
    专为站点地图设计，轻量级接口，仅返回必要信息。
    """
    try:
        offset = (page - 1) * limit

        # 高效地只查询必要的字段
        query = PanResource.all().order_by("-created_at").offset(offset).limit(limit)
        resources_data = await query.values("resource_key", "created_at")

        # 为了分页，我们需要总数
        total_count = await PanResource.all().count()

        # 构建响应
        sitemap_entries = [
            {
                "detail_url": f"/resource/{res['resource_key']}",
                "created_at": (
                    res["created_at"].astimezone(BEIJING_TIMEZONE).isoformat()
                    if res.get("created_at")
                    else None
                ),
            }
            for res in resources_data
        ]

        logger.info(f"sitemap_entries: {sitemap_entries}")

        return {
            "total": total_count,
            "page": page,
            "limit": limit,
            "pages": (total_count + limit - 1) // limit if limit > 0 else 0,
            "resources": sitemap_entries,
        }
    except Exception as e:
        logger.error(f"获取站点地图资源时发生异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取站点地图资源异常: {str(e)}")


@router.get(
    "/resources/count",
    summary="获取资源总数",
    description="获取系统中所有资源的总数。",
)
async def get_resources_count():
    """
    获取所有PanResource的总数。
    """
    try:
        count = await PanResource.all().count()
        return {"count": count}
    except Exception as e:
        logger.error(f"获取资源总数时发生异常: {str(e)}")
        raise HTTPException(status_code=500, detail="获取资源总数失败")


@router.get(
    "/custom_search",
    summary="定制化limit查询",
    description="支持自定义limit的资源检索，参数包括title, file_type, exact, sort_by, sort_order, valid_only, user, time_filter。",
)
async def custom_search(
    title: Optional[str] = Query(None, description="资源名称/文件名(支持模糊搜索)"),
    file_type: Optional[str] = Query(None, description="文件类型过滤"),
    exact: bool = Query(False, description="是否精准搜索"),
    sort_by: str = Query(
        "updated_at",
        description="排序字段: updated_at, created_at, relevance, access_count, title",
    ),
    sort_order: str = Query("desc", description="排序方向: asc, desc"),
    valid_only: bool = Query(False, description="只返回验证有效的资源"),
    user: Optional[str] = Query(
        None, description="根据分享用户名查找资源。传空字符串可查找匿名分享的资源。"
    ),
    limit: int = Query(10, description="返回条数", le=2000),
    query_type: str = Query(
        "最近更新",
        description="查询类型: 最近更新,相关推荐",
    ),
    time_filter: TimeFilter = Query(
        TimeFilter.ALL,
        description="时间过滤: all(全部时间), week(最近一周), half_month(最近半月), month(最近1月), half_year(最近半年), year(最近一年)",
    ),
):
    try:
        search_result = await local_search_service.search_local(
            keyword=title,
            file_type=file_type,
            exact=exact,
            sort_by=sort_by,
            sort_order=sort_order,
            valid_only=valid_only,
            user=user,
            limit=limit,
            time_filter=time_filter,  # 添加时间过滤参数
        )
        resources_orm = search_result.get("results", [])
        results = []
        for resource in resources_orm[:limit]:
            created_at_str = resource.get("created_at")
            created_at_dt = (
                datetime.fromisoformat(created_at_str) if created_at_str else None
            )
            updated_at_str = resource.get("updated_at")
            updated_at_dt = (
                datetime.fromisoformat(updated_at_str) if updated_at_str else None
            )
            resource_dict = {
                "id": resource.get("id"),
                "resource_id": resource.get("resource_key"),
                "pan_type": resource.get("pan_type"),
                "pan_type_name": (
                    "百度网盘"
                    if resource.get("pan_type") == 1
                    else "夸克网盘" if resource.get("pan_type") == 2 else "未知"
                ),
                "title": resource.get("title"),
                "verified_status": resource.get("verified_status"),
                "share_url": resource.get("share_url"),
                "share_pwd": resource.get("share_pwd"),
                "file_type": resource.get("file_type"),
                "file_size": resource.get("file_size"),
                "access_count": resource.get("access_count"),
                "text_content": resource.get("text_content"),
                "author": resource.get("author"),
                "created_at": (
                    created_at_dt.astimezone(BEIJING_TIMEZONE).isoformat()
                    if created_at_dt
                    else None
                ),
                "updated_at": (
                    updated_at_dt.astimezone(BEIJING_TIMEZONE).isoformat()
                    if updated_at_dt
                    else None
                ),
            }
            results.append(resource_dict)
        return {
            "status": "success",
            "message": f"{query_type}查询成功",
            "total": len(results),
            "resources": results,
        }
    except Exception as e:
        logger.error(f"{query_type}查询异常: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"{query_type}查询异常: {str(e)}")
