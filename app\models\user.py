from tortoise import fields, models
from tortoise.contrib.pydantic import pydantic_model_creator
from passlib.context import Crypt<PERSON>ontext
from datetime import datetime, timedelta
import secrets
import hashlib
from typing import Optional, List

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class Role(models.Model):
    """角色表"""
    
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=50, unique=True, description="角色名称")  # admin/user/guest
    display_name = fields.Char<PERSON>ield(max_length=100, description="显示名称")
    description = fields.TextField(null=True, description="角色描述")
    permissions = fields.JSONField(default=list, description="权限列表")
    is_active = fields.BooleanField(default=True, description="是否启用")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    class Meta:
        table = "roles"
        
    def __str__(self):
        return f"{self.display_name} ({self.name})"


class User(models.Model):
    """用户表"""
    
    id = fields.IntField(pk=True)
    username = fields.CharField(max_length=50, unique=True, description="用户名")
    email = fields.CharField(max_length=100, unique=True, description="邮箱地址")
    password_hash = fields.CharField(max_length=255, description="密码哈希")
    nickname = fields.CharField(max_length=100, null=True, description="昵称")
    avatar = fields.CharField(max_length=512, null=True, description="头像URL")
    phone = fields.CharField(max_length=20, null=True, description="手机号")
    
    # 用户状态
    status = fields.CharField(
        max_length=20, 
        default="pending", 
        description="用户状态: pending/active/frozen/deleted"
    )
    
    # 角色关联
    role: fields.ForeignKeyRelation["Role"] = fields.ForeignKeyField(
        "models.Role", 
        related_name="users", 
        default=2,  # 默认普通用户角色ID
        description="用户角色"
    )
    
    # 邮箱验证
    email_verified = fields.BooleanField(default=False, description="邮箱是否已验证")
    email_verify_token = fields.CharField(max_length=255, null=True, description="邮箱验证令牌")
    email_verify_expires = fields.DatetimeField(null=True, description="邮箱验证令牌过期时间")
    
    # 密码重置
    password_reset_token = fields.CharField(max_length=255, null=True, description="密码重置令牌")
    password_reset_expires = fields.DatetimeField(null=True, description="密码重置令牌过期时间")
    
    # 登录相关
    last_login_at = fields.DatetimeField(null=True, description="最后登录时间")
    login_attempts = fields.IntField(default=0, description="登录失败次数")
    locked_until = fields.DatetimeField(null=True, description="账户锁定到期时间")
    
    # 时间戳
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")
    
    class Meta:
        table = "users"
        
    def __str__(self):
        return f"{self.username} ({self.email})"
    
    @classmethod
    def hash_password(cls, password: str) -> str:
        """密码哈希"""
        return pwd_context.hash(password)
    
    def verify_password(self, password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(password, self.password_hash)
    
    def set_password(self, password: str):
        """设置密码"""
        self.password_hash = self.hash_password(password)
    
    def generate_email_verify_token(self) -> str:
        """生成邮箱验证令牌"""
        token = secrets.token_urlsafe(32)
        self.email_verify_token = hashlib.sha256(token.encode()).hexdigest()
        self.email_verify_expires = datetime.utcnow() + timedelta(hours=24)
        return token
    
    def generate_password_reset_token(self) -> str:
        """生成密码重置令牌"""
        token = secrets.token_urlsafe(32)
        self.password_reset_token = hashlib.sha256(token.encode()).hexdigest()
        self.password_reset_expires = datetime.utcnow() + timedelta(hours=24)
        return token
    
    def verify_email_token(self, token: str) -> bool:
        """验证邮箱验证令牌"""
        if not self.email_verify_token or not self.email_verify_expires:
            return False
        if datetime.utcnow() > self.email_verify_expires:
            return False
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        return token_hash == self.email_verify_token
    
    def verify_password_reset_token(self, token: str) -> bool:
        """验证密码重置令牌"""
        if not self.password_reset_token or not self.password_reset_expires:
            return False
        if datetime.utcnow() > self.password_reset_expires:
            return False
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        return token_hash == self.password_reset_token
    
    def is_locked(self) -> bool:
        """检查账户是否被锁定"""
        if not self.locked_until:
            return False
        return datetime.utcnow() < self.locked_until
    
    def lock_account(self, duration_minutes: int = 30):
        """锁定账户"""
        self.locked_until = datetime.utcnow() + timedelta(minutes=duration_minutes)
    
    def unlock_account(self):
        """解锁账户"""
        self.locked_until = None
        self.login_attempts = 0
    
    async def get_permissions(self) -> List[str]:
        """获取用户权限列表"""
        await self.fetch_related('role')
        return self.role.permissions if self.role else []
    
    async def has_permission(self, permission: str) -> bool:
        """检查用户是否有指定权限"""
        permissions = await self.get_permissions()
        return permission in permissions


class UserSession(models.Model):
    """用户会话表"""
    
    id = fields.IntField(pk=True)
    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User", 
        related_name="sessions", 
        on_delete=fields.CASCADE,
        description="关联用户"
    )
    session_token = fields.CharField(max_length=255, unique=True, description="会话令牌")
    refresh_token = fields.CharField(max_length=255, unique=True, description="刷新令牌")
    expires_at = fields.DatetimeField(description="过期时间")
    user_agent = fields.TextField(null=True, description="用户代理")
    ip_address = fields.CharField(max_length=45, null=True, description="IP地址")  # 支持IPv6
    is_active = fields.BooleanField(default=True, description="是否活跃")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    
    class Meta:
        table = "user_sessions"
        
    def __str__(self):
        return f"Session {self.id} for {self.user.username}"
    
    def is_expired(self) -> bool:
        """检查会话是否过期"""
        return datetime.utcnow() > self.expires_at


# 创建Pydantic模型用于API响应
UserPydantic = pydantic_model_creator(User, name="User", exclude=("password_hash", "email_verify_token", "password_reset_token"))
UserInPydantic = pydantic_model_creator(User, name="UserIn", include=("username", "email", "password_hash", "nickname"))
RolePydantic = pydantic_model_creator(Role, name="Role")
