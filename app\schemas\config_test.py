from pydantic import BaseModel, Field
from typing import Dict, List, Optional


class WxPushConfig(BaseModel):
    """微信推送配置"""
    corp_id: str = Field(..., description="企业微信CorpID")
    corp_secret: str = Field(..., description="企业微信CorpSecret")
    agent_id: str = Field(..., description="企业微信应用AgentID")
    to_party: str = Field(..., description="接收消息的部门ID")


class DatabaseConfig(BaseModel):
    """数据库配置"""
    url: str = Field(..., description="数据库连接URL")
    echo: bool = Field(False, description="是否输出SQL语句")
    pool_size: int = Field(5, description="连接池大小")
    max_overflow: int = Field(10, description="连接池最大溢出数")


class ApiConfig(BaseModel):
    """API服务配置"""
    host: str = Field("127.0.0.1", description="API服务监听地址")
    port: int = Field(8000, description="API服务监听端口")
    debug: bool = Field(False, description="是否开启调试模式")
    cors_origins: List[str] = Field(default_factory=list, description="CORS允许的来源域名")
    max_concurrent_requests: int = Field(10, description="最大并发请求数")


class CrawlerConfig(BaseModel):
    """爬虫配置"""
    user_agents: List[str] = Field(default_factory=list, description="随机使用的User-Agent列表")
    request_timeout: int = Field(30, description="请求超时时间(秒)")
    retry_count: int = Field(3, description="请求失败重试次数")
    cookies: Dict[str, str] = Field(default_factory=dict, description="请求Cookie")


class CacheConfig(BaseModel):
    """缓存配置"""
    ttl: int = Field(3600, description="缓存过期时间(秒)")
    max_size: int = Field(1000, description="缓存最大项数")


class AppConfig(BaseModel):
    """应用全局配置"""
    wx_push: Optional[WxPushConfig] = None
    database: Optional[DatabaseConfig] = None
    api: Optional[ApiConfig] = None
    crawler: Optional[CrawlerConfig] = None
    cache: Optional[CacheConfig] = None 