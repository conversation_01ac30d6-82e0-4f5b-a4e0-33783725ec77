from fastapi import HTTPException, Request, Header
from typing import Optional, List

# 允许访问的来源域名
ALLOWED_ORIGINS = [
    "http://127.0.0.1:9999",
    "http://127.0.0.1:9998",  # 添加9998端口
    "https://panso.dkon.cn",
    "http://localhost:3000",
    "http://0.0.0.0:9999",
    "http://**************",
    "http://**************",
    "http://127.0.0.1",
    "http://localhost",
    "https://pansoo.cn",
    "https://www.pansoo.cn",
]


async def verify_origin(request: Request, referer: Optional[str] = Header(None)):
    """验证请求来源是否合法"""
    # 允许访问文档页面和静态资源
    if request.url.path in [
        "/docs",
        "/redoc",
        "/openapi.json",
    ] or request.url.path.startswith("/static"):
        return True

    if not ALLOWED_ORIGINS:  # 如果没有设置限制，则允许所有来源
        return True

    # 检查Origin头或Referer头
    origin = request.headers.get("origin")
    if origin and origin in ALLOWED_ORIGINS:
        return True

    if referer:
        for allowed_origin in ALLOWED_ORIGINS:
            if referer.startswith(allowed_origin):
                return True

    # 允许来自同一主机的请求
    host = request.headers.get("host", "").split(":")[0]
    if host in [
        url.split("://")[1].split(":")[0].split("/")[0]
        for url in ALLOWED_ORIGINS
        if "://" in url
    ]:
        return True

    raise HTTPException(status_code=403, detail="禁止访问：请求来源不被允许")
