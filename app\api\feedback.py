from fastapi import APIRouter, Request, HTTPException, Depends
import logging
from app.models.pydantic_models import (
    ResourceInvalidFeedbackRequest,
    ResourceInvalidFeedbackResponse,
)
from app.models.resource import PanResource
from app.models.feedback import ResourceInvalidFeedback
import time
from app.middleware.verify import verify_origin
from app.services.baidu_pan_service import baidu_pan_service
from app.services.quark_pan_service import quark_pan_service
from app.services.xunlei_pan_service import xunlei_pan_service
from app.services.aliyun_service import aliyun_pan_service

# 定义日志记录器
logger = logging.getLogger("feedback-api")

router = APIRouter(tags=["feedback"])


@router.post(
    "/report_invalid_resource",
    response_model=ResourceInvalidFeedbackResponse,
    dependencies=[Depends(verify_origin)],
    summary="报告资源失效",
    description="报告资源失效，并进行实时验证，如果资源确实失效则删除该资源",
)
async def report_invalid_resource(
    request: Request, feedback: ResourceInvalidFeedbackRequest
):
    """
    报告资源失效接口

    允许用户报告资源失效，并进行实时验证，如果资源确实失效则删除该资源

    - 失效类型:
      - 1: 链接错误
      - 2: 资源失效
      - 3: 文件不存在
    """
    start_time = time.time()
    logger.info(
        f"收到资源失效反馈: resource_id={feedback.resource_id}, pan_type={feedback.pan_type}, invalid_type={feedback.invalid_type}"
    )
    try:
        resource = await PanResource.filter(resource_key=feedback.resource_id).first()
        if not resource:
            return ResourceInvalidFeedbackResponse(
                status="error", message="未找到对应资源"
            )
        await ResourceInvalidFeedback.create(
            resource_id=feedback.resource_id,
            pan_type=feedback.pan_type,
            invalid_type=feedback.invalid_type,
            description=feedback.description,
            contact_info=feedback.contact_info,
        )
        share_url = resource.share_url if resource.share_url else resource.original_url
        if not share_url:
            return ResourceInvalidFeedbackResponse(
                status="error", message="资源链接不存在"
            )
        if feedback.pan_type == 1:
            status_result = await baidu_pan_service.check_resource_status(share_url)
        elif feedback.pan_type == 2:
            status_result = await quark_pan_service.check_resource_status(share_url)
        elif feedback.pan_type == 3:
            status_result = await aliyun_pan_service.check_resource_status(share_url)
        elif feedback.pan_type == 4:
            status_result = await xunlei_pan_service.check_resource_status(share_url)
        else:
            return ResourceInvalidFeedbackResponse(
                status="error", message="不支持的网盘类型"
            )
        is_valid = status_result.get("valid", False)
        feedback_record = (
            await ResourceInvalidFeedback.filter(resource_id=feedback.resource_id)
            .order_by("-id")
            .first()
        )
        if feedback_record:
            feedback_record.is_verified = True
            feedback_record.verification_result = "valid" if is_valid else "invalid"
            await feedback_record.save()
        if not is_valid:
            resource.verified_status = "invalid"
            await resource.save()
            await resource.delete()
            if feedback_record:
                feedback_record.is_deleted = True
                await feedback_record.save()
            total_time = time.time() - start_time
            logger.info(
                f"资源已确认失效并标记删除: {feedback.resource_id}, 耗时: {total_time:.2f}秒"
            )
            return ResourceInvalidFeedbackResponse(
                status="success", message="资源已确认失效并标记删除", is_deleted=True
            )
        else:
            total_time = time.time() - start_time
            logger.info(
                f"资源验证仍然有效: {feedback.resource_id}, 耗时: {total_time:.2f}秒"
            )
            return ResourceInvalidFeedbackResponse(
                status="success",
                message="感谢反馈，但资源目前仍然有效",
                is_deleted=False,
            )
    except Exception as e:
        total_time = time.time() - start_time
        logger.error(
            f"处理资源失效反馈过程中发生异常: {str(e)}, 耗时: {total_time:.2f}秒"
        )
        return ResourceInvalidFeedbackResponse(
            status="error", message=f"处理失效反馈异常: {str(e)}"
        )
