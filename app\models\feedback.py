from tortoise import fields, models


class ResourceInvalidFeedback(models.Model):
    """资源失效反馈记录表"""

    id = fields.IntField(pk=True)
    resource_id = fields.CharField(max_length=50)  # 资源ID
    pan_type = fields.IntField()  # 1=百度网盘, 2=夸克网盘
    invalid_type = fields.IntField()  # 失效类型: 1=链接错误, 2=资源失效, 3=文件不存在
    description = fields.TextField(null=True)  # 补充说明
    contact_info = fields.CharField(max_length=100, null=True)  # 联系方式

    is_verified = fields.BooleanField(default=False)  # 是否已验证
    verification_result = fields.CharField(
        max_length=20, null=True
    )  # 验证结果: valid, invalid
    is_deleted = fields.BooleanField(default=False)  # 资源是否已删除

    created_at = fields.DatetimeField(auto_now_add=True)  # 反馈时间
    updated_at = fields.DatetimeField(auto_now=True)  # 更新时间

    class Meta:
        table = "resource_invalid_feedbacks"

    def __str__(self):
        return f"Feedback {self.id} for resource {self.resource_id}"
