import asyncio
import httpx
from lxml import html
from typing import List, Optional
import random

from app.crawlers.base_crawler import BaseCrawler
from app.models.pydantic_models import CrawledResource
from app.services.db_batch import add_to_db_queue, batch_save_to_db
from app.utils.common import parse_time_string, infer_file_type
from app.utils.pan_url_parser import parse_pan_url
from app.utils.config import settings
import logging


class AisouaCrawler(BaseCrawler):
    """
    aisoua.com 网站的爬虫。
    """

    def __init__(self):
        self.config = settings.get("aisoua_crawler", {})
        self.base_url = self.config.get("base_url")
        self.timeout = self.config.get("timeout", 10)
        self.logger = logging.getLogger(self.__class__.__name__)
        self.headers = {
            "User-Agent": "Mozilla/5.0 (compatible; Bytespider; https://zhanzhang.toutiao.com/) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/70.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.9",
        }
        self.last_detail_links = set()  # 新增：用于对比上一次的链接

    async def crawl(self, **kwargs) -> List[CrawledResource]:
        """
        执行一次 aisoua.com 的爬取任务。
        从资源详情页面获取最新入库的节点，然后获取各个子节点的详情链接。
        """
        if not self.base_url:
            self.logger.error("aisoua_crawler 的 base_url 未在配置中设置。")
            return []

        async with httpx.AsyncClient(
            headers=self.headers,
            timeout=self.timeout,
            follow_redirects=True,
        ) as client:
            while True:
                try:
                    # 1. 访问资源详情页面获取最新入库的节点
                    self.logger.info(f"正在访问资源详情页面: {self.base_url}")
                    response = await client.get(self.base_url)
                    response.raise_for_status()
                    tree = html.fromstring(response.text)

                    # 2. 从页面中解析出最新入库的资源卡片链接
                    # 根据提供的HTML结构，资源卡片在resource-grid中
                    detail_links = tree.xpath(
                        '//div[@class="resource-grid"]//div[@class="resource-card"]//div[@class="card-title"]/a/@href'
                    )
                    self.logger.info(
                        f"在资源详情页面找到 {len(detail_links)} 个子节点详情链接。"
                    )

                    # 3. 处理链接，确保是完整的URL
                    full_detail_links = []
                    for link in detail_links:
                        if link.startswith("/"):
                            # 如果是相对路径，需要拼接基础域名
                            base_domain = self.base_url.split("/details/")[
                                0
                            ]  # 获取基础域名
                            full_link = base_domain + link
                        else:
                            full_link = link
                        full_detail_links.append(full_link)

                    current_detail_links = set(full_detail_links)
                    if current_detail_links == self.last_detail_links:
                        self.logger.info("无新资源，跳过本轮详情页爬取。")
                    else:
                        new_links = current_detail_links - self.last_detail_links
                        if new_links:
                            self.logger.info(
                                f"发现 {len(new_links)} 个新资源，开始爬取详情页。"
                            )
                            crawled_resources = []
                            for link in new_links:
                                res = await self.parse_detail_page(client, link)
                                if res:  # 只添加成功解析的资源
                                    crawled_resources.append(res)
                                await asyncio.sleep(random.uniform(0.5, 0.7))

                            self.logger.info(
                                f"成功解析了 {len(crawled_resources)} 个新资源。"
                            )
                            # 存入数据库
                            if crawled_resources:
                                items_to_save = [
                                    res.model_dump() for res in crawled_resources
                                ]
                                await add_to_db_queue(items_to_save)
                                self.logger.info(
                                    f"已将 {len(items_to_save)} 个资源添加到数据库处理队列。"
                                )
                                # 强制将队列中的数据写入数据库
                                await batch_save_to_db(force=True)
                                self.logger.info("数据库批量保存完成。")
                        else:
                            self.logger.info("有资源变动，但无新增资源。")
                        self.last_detail_links = current_detail_links
                except httpx.HTTPStatusError as e:
                    self.logger.error(f"访问资源详情页面失败: {e}")
                except Exception as e:
                    self.logger.error(f"爬虫执行过程中发生未知错误: {e}", exc_info=True)
                # 随机暂停10-30秒
                await asyncio.sleep(random.uniform(10, 30))

    async def parse_detail_page(
        self, client: httpx.AsyncClient, url: str
    ) -> Optional[CrawledResource]:
        """
        解析单个资源详情页。
        现在解析的是子节点的详情页面，需要获取网盘下载链接等信息。
        """
        try:
            self.logger.debug(f"正在解析详情页: {url}")
            response = await client.get(url)
            response.raise_for_status()
            tree = html.fromstring(response.text)

            # 从页面标题或主要内容区域获取资源标题
            title_candidates = [tree.xpath("/html/body/div/div[1]/h1/text()")]

            title = None
            for candidate in title_candidates:
                if candidate:
                    title = "".join(candidate).strip()
                    if title:
                        break

            if not title:
                self.logger.warning(f"无法在页面 {url} 上找到标题。")
                return None

            # 查找下载链接，尝试多种可能的选择器
            origin_url_selectors = [
                '//a[@id="down"]/@href',
                '//a[contains(@class, "download")]/@href',
                '//a[contains(text(), "下载")]/@href',
                '//a[contains(@href, "pan.baidu.com")]/@href',
                '//a[contains(@href, "pan.quark.cn")]/@href',
                '//a[contains(@href, "drive.uc.cn")]/@href',
            ]

            origin_url = None
            for selector in origin_url_selectors:
                origin_url_list = tree.xpath(selector)
                if origin_url_list:
                    origin_url = origin_url_list[0]
                    break

            if not origin_url:
                self.logger.warning(f"无法在页面 {url} 上找到下载链接。")
                return None

            # 解析网盘链接
            pan_resource = parse_pan_url(origin_url)
            self.logger.info(f"解析网盘链接: {pan_resource}")
            if not pan_resource:
                self.logger.warning(f"无法解析网盘链接: {origin_url}")
                return None

            # 提取文件大小 - 尝试多种可能的位置
            file_size_text = None

            # 首先尝试结构化的meta信息
            meta_items = tree.xpath(
                '//div[@class="resource-meta-grid"]/div[@class="meta-item"]'
            )
            for item in meta_items:
                label = item.xpath('.//div[@class="meta-label"]/text()')
                value = item.xpath('.//div[@class="meta-value"]/text()')
                if label and value:
                    label_text = label[0].strip()
                    value_text = value[0].strip()
                    if label_text == "文件大小":
                        file_size_text = value_text
                        break

            # 如果没找到，尝试从页面文本中提取
            if not file_size_text:
                page_text = tree.text_content()
                import re

                size_pattern = r"(\d+(?:\.\d+)?\s*[KMGT]?B)"
                size_matches = re.findall(size_pattern, page_text, re.IGNORECASE)
                if size_matches:
                    file_size_text = size_matches[0]

            # 提取分享时间 - 尝试多种可能的位置
            share_time_text = None
            time_selectors = [
                "/html/body/div/div[1]/div[1]/div[3]/div[2]/div[2]/text()",
                '//div[contains(@class, "time")]//text()',
                '//span[contains(@class, "date")]//text()',
                '//div[contains(text(), "时间")]//text()',
            ]

            for selector in time_selectors:
                time_elements = tree.xpath(selector)
                if time_elements:
                    time_text = "".join(time_elements).strip()
                    if time_text:
                        share_time_text = (
                            time_text.replace("分享时间：", "")
                            .replace("时间：", "")
                            .strip()
                        )
                        break

            # 如果还是没找到时间，使用当前时间
            if not share_time_text:
                from datetime import datetime

                share_time_text = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            updated_at = parse_time_string(share_time_text)

            # 从资源标题中解析文件类型
            file_type = infer_file_type(title, "")

            # 打印所有信息
            self.logger.info(f"标题: {title}")
            self.logger.info(f"原始链接: {origin_url}")
            self.logger.info(f"文件大小: {file_size_text}")
            self.logger.info(f"分享时间: {share_time_text}")
            self.logger.info(f"更新时间: {updated_at}")
            self.logger.info(f"文件类型: {file_type}")
            self.logger.info(f"网盘链接: {pan_resource}")

            return CrawledResource(
                title=title,
                original_url=origin_url,
                file_size=file_size_text,
                updated_at=updated_at,
                resource_key=pan_resource.get("resource_key"),
                pan_type=pan_resource.get("pan_type_int"),
                file_type=file_type,
                source=self.__class__.__name__,
            )
        except httpx.HTTPStatusError as e:
            self.logger.error(f"访问详情页失败 {url}: {e}")
        except Exception as e:
            self.logger.error(f"解析详情页 {url} 失败: {e}", exc_info=True)
        return None

    async def run(self):
        """
        提供一个统一的运行入口，调用 crawl。
        """
        await self.crawl()

    async def start_monitoring(self):
        """占位方法，暂不实现"""
        pass
