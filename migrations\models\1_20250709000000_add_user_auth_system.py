from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        -- 创建角色表
        CREATE TABLE IF NOT EXISTS "roles" (
            "id" SERIAL NOT NULL PRIMARY KEY,
            "name" VARCHAR(50) NOT NULL UNIQUE,
            "display_name" VARCHAR(100) NOT NULL,
            "description" TEXT,
            "permissions" JSONB NOT NULL DEFAULT '[]',
            "is_active" BOOL NOT NULL DEFAULT True,
            "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
            "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
        COMMENT ON TABLE "roles" IS '用户角色表';
        COMMENT ON COLUMN "roles"."name" IS '角色名称';
        COMMENT ON COLUMN "roles"."display_name" IS '显示名称';
        COMMENT ON COLUMN "roles"."description" IS '角色描述';
        COMMENT ON COLUMN "roles"."permissions" IS '权限列表';
        COMMENT ON COLUMN "roles"."is_active" IS '是否启用';
        
        -- 创建用户表
        CREATE TABLE IF NOT EXISTS "users" (
            "id" SERIAL NOT NULL PRIMARY KEY,
            "username" VARCHAR(50) NOT NULL UNIQUE,
            "email" VARCHAR(100) NOT NULL UNIQUE,
            "password_hash" VARCHAR(255) NOT NULL,
            "nickname" VARCHAR(100),
            "avatar" VARCHAR(512),
            "phone" VARCHAR(20),
            "status" VARCHAR(20) NOT NULL DEFAULT 'pending',
            "role_id" INT NOT NULL DEFAULT 2 REFERENCES "roles" ("id") ON DELETE RESTRICT,
            "email_verified" BOOL NOT NULL DEFAULT False,
            "email_verify_token" VARCHAR(255),
            "email_verify_expires" TIMESTAMPTZ,
            "password_reset_token" VARCHAR(255),
            "password_reset_expires" TIMESTAMPTZ,
            "last_login_at" TIMESTAMPTZ,
            "login_attempts" INT NOT NULL DEFAULT 0,
            "locked_until" TIMESTAMPTZ,
            "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
            "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
        COMMENT ON TABLE "users" IS '用户表';
        COMMENT ON COLUMN "users"."username" IS '用户名';
        COMMENT ON COLUMN "users"."email" IS '邮箱地址';
        COMMENT ON COLUMN "users"."password_hash" IS '密码哈希';
        COMMENT ON COLUMN "users"."nickname" IS '昵称';
        COMMENT ON COLUMN "users"."avatar" IS '头像URL';
        COMMENT ON COLUMN "users"."phone" IS '手机号';
        COMMENT ON COLUMN "users"."status" IS '用户状态: pending/active/frozen/deleted';
        COMMENT ON COLUMN "users"."role_id" IS '用户角色';
        COMMENT ON COLUMN "users"."email_verified" IS '邮箱是否已验证';
        COMMENT ON COLUMN "users"."email_verify_token" IS '邮箱验证令牌';
        COMMENT ON COLUMN "users"."email_verify_expires" IS '邮箱验证令牌过期时间';
        COMMENT ON COLUMN "users"."password_reset_token" IS '密码重置令牌';
        COMMENT ON COLUMN "users"."password_reset_expires" IS '密码重置令牌过期时间';
        COMMENT ON COLUMN "users"."last_login_at" IS '最后登录时间';
        COMMENT ON COLUMN "users"."login_attempts" IS '登录失败次数';
        COMMENT ON COLUMN "users"."locked_until" IS '账户锁定到期时间';
        
        -- 创建用户会话表
        CREATE TABLE IF NOT EXISTS "user_sessions" (
            "id" SERIAL NOT NULL PRIMARY KEY,
            "user_id" INT NOT NULL REFERENCES "users" ("id") ON DELETE CASCADE,
            "session_token" VARCHAR(255) NOT NULL UNIQUE,
            "refresh_token" VARCHAR(255) NOT NULL UNIQUE,
            "expires_at" TIMESTAMPTZ NOT NULL,
            "user_agent" TEXT,
            "ip_address" VARCHAR(45),
            "is_active" BOOL NOT NULL DEFAULT True,
            "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
        );
        COMMENT ON TABLE "user_sessions" IS '用户会话表';
        COMMENT ON COLUMN "user_sessions"."user_id" IS '关联用户';
        COMMENT ON COLUMN "user_sessions"."session_token" IS '会话令牌';
        COMMENT ON COLUMN "user_sessions"."refresh_token" IS '刷新令牌';
        COMMENT ON COLUMN "user_sessions"."expires_at" IS '过期时间';
        COMMENT ON COLUMN "user_sessions"."user_agent" IS '用户代理';
        COMMENT ON COLUMN "user_sessions"."ip_address" IS 'IP地址';
        COMMENT ON COLUMN "user_sessions"."is_active" IS '是否活跃';
        

        
        -- 创建索引
        CREATE INDEX IF NOT EXISTS "idx_users_username" ON "users" ("username");
        CREATE INDEX IF NOT EXISTS "idx_users_email" ON "users" ("email");
        CREATE INDEX IF NOT EXISTS "idx_users_status" ON "users" ("status");
        CREATE INDEX IF NOT EXISTS "idx_users_role_id" ON "users" ("role_id");
        CREATE INDEX IF NOT EXISTS "idx_user_sessions_user_id" ON "user_sessions" ("user_id");
        CREATE INDEX IF NOT EXISTS "idx_user_sessions_refresh_token" ON "user_sessions" ("refresh_token");

        
        -- 插入默认角色数据
        INSERT INTO "roles" ("id", "name", "display_name", "description", "permissions") VALUES
        (1, 'admin', '管理员', '系统管理员，拥有所有权限', '["user.manage", "user.view", "resource.manage", "resource.submit", "resource.view", "feedback.manage", "feedback.create", "feedback.view", "system.config", "system.stats", "profile.manage"]'),
        (2, 'user', '注册用户', '普通注册用户', '["resource.submit", "resource.view", "feedback.create", "feedback.view", "profile.manage"]'),
        (3, 'guest', '游客', '未注册用户', '["resource.view", "feedback.view"]')
        ON CONFLICT (id) DO NOTHING;
        
        -- 重置序列
        SELECT setval('roles_id_seq', (SELECT MAX(id) FROM roles));
    """


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        -- 删除索引
        DROP INDEX IF EXISTS "idx_user_sessions_refresh_token";
        DROP INDEX IF EXISTS "idx_user_sessions_user_id";
        DROP INDEX IF EXISTS "idx_users_role_id";
        DROP INDEX IF EXISTS "idx_users_status";
        DROP INDEX IF EXISTS "idx_users_email";
        DROP INDEX IF EXISTS "idx_users_username";
        
        -- 删除表
        DROP TABLE IF EXISTS "user_sessions";
        DROP TABLE IF EXISTS "users";
        DROP TABLE IF EXISTS "roles";
    """
