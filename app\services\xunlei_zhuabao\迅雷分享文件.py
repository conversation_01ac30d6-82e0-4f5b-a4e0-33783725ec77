
import requests
import json

url = "https://api-pan.xunlei.com/drive/v1/share"

payload = json.dumps({
   "file_ids": [
      "VNrk8ZPpTI9ryjo-SOFLZiljA1"
   ],
   "share_to": "copy",
   "params": {
      "subscribe_push": "false",
      "WithPassCodeInLink": "true"
   },
   "title": "云盘资源分享",
   "restore_limit": "-1",
   "expiration_days": "1"
})
headers = {
   'x-captcha-token': 'ck0.hETpfoSsBJRm1rtt7cpL0V1sCiUcnevvCEYxQ6W18gez9YoWiywkVxIN3bSHVv--dpv28_1qG7S77NX4Mcg9aWPONhuL4M732oiE-etYw9t9ruwdeo_u4-anBy-tSrS9eKL9bu-vMT3L1S4m4eiERHbxrLVhrrXGIs2bSj94sfb4VLg64O2PjA8Ef_4x9ye13OgXqY7FShDEzkqMF5Fye1jMntagd9mOudzpaNIrEyuBf0iB6Y2mfV3tEpnr22nD0JUqw9yg25rmMqjiFLRe-CTLlUSC7JSLHsIqxnFCw_uyEZhfvpelZgQEAfqHGj3dUpsoEuJ4qlgpWZTInuWGTTumRihJfJwDh9JBCFAGBAzWnAY9tJltmvKdadDsUTcUxIlKvy3A7G7B94F4udfA7MieFTX0R7mJKTjWyTdq8kA.ClQIz9Xt7-0yEhBYcXAwa0pCWFdod2FUcEI2GgcxLjkxLjIwIg5wYW4ueHVubGVpLmNvbSogODI4ODQ3OWM0ZmI5ZWJlZDA5YWQ2YmI4ZTI2MDVjYWUSgAF4wOGl8dXd88rFW_exiEBltfWziI-S9Y7Vqor89Hpjo_DJhgS6kagws2Ospl_TR_5bwxWybdzOBzYKpk1z8KWV8eO0TQ0E9ypHstlcskYD0dfO8ol2mB09FFNt3IFE00nZVBXn71b3HMUYwJit6M5KZ0ASbU0bcQCr7hCd1A5rYw',
   'x-client-id': 'Xqp0kJBXWhwaTpB6',
   'x-device-id': '8288479c4fb9ebed09ad6bb8e2605cae',
   'authorization': 'Bearer ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
   'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
   'Content-Type': 'application/json',
   'Accept': '*/*',
   'Host': 'api-pan.xunlei.com',
   'Connection': 'keep-alive'
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)


"""响应
{
    "share_id": "VOQTj3Ca52Hx5jDuizX9mGvbA1",
    "share_url": "https://pan.xunlei.com/s/VOQTj3Ca52Hx5jDuizX9mGvbA1",
    "pass_code": "4bmj",
    "share_text": "链接：https://pan.xunlei.com/s/VOQTj3Ca52Hx5jDuizX9mGvbA1?pwd=4bmj# 复制这段内容后打开「手机迅雷 App」即可获取。无需下载在线查看，视频原画享倍速播放",
    "share_list": [],
    "share_error_files": [],
    "share_text_ext": ""
}
"""