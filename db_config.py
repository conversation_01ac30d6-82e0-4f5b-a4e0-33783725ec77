from app.utils.config import settings

# 从应用的全局配置中获取数据库设置
db_config = settings.get("database", {})

db_user = db_config.get("user", "pan_so_user")
db_password = db_config.get("password", "Wsk1998107.")
db_host = db_config.get("host", "localhost")
db_port = db_config.get("port", 5432)
db_name = db_config.get("db", "pan_so_db")

# 构建数据库连接URL
DATABASE_URL = f"postgres://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

# 为 Aerich 和 Tortoise ORM 提供配置
TORTOISE_ORM = {
    "connections": {"default": DATABASE_URL},
    "apps": {
        "models": {
            "models": [
                "app.models.resource",
                "app.models.feedback",
                "app.models.submission",
                "app.models.user",
                "aerich.models",
            ],
            "default_connection": "default",
        },
    },
}
